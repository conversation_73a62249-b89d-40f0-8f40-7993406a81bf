#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一学生C记录格式
将旧的时间戳版本格式统一为新的v1.0格式
"""

import json
import logging
from pathlib import Path
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def unify_student_c_records(analysis_output_dir: str = "E:/LLMData/analysis_output", 
                           target_version: str = "v1.0", 
                           dry_run: bool = True):
    """
    统一学生C记录格式
    
    Args:
        analysis_output_dir: 分析输出目录
        target_version: 目标版本号
        dry_run: 是否为试运行
    """
    analysis_dir = Path(analysis_output_dir)
    
    if not analysis_dir.exists():
        logger.error(f"❌ 目录不存在: {analysis_dir}")
        return
    
    logger.info(f"🔧 开始统一学生C记录格式")
    logger.info(f"目录: {analysis_dir}")
    logger.info(f"目标版本: {target_version}")
    logger.info(f"模式: {'试运行' if dry_run else '实际执行'}")
    
    json_files = list(analysis_dir.glob("*.json"))
    logger.info(f"📁 找到 {len(json_files)} 个JSON文件")
    
    stats = {
        'total_files': len(json_files),
        'processed_files': 0,
        'updated_files': 0,
        'error_files': 0,
        'student_c_files': 0
    }
    
    # 处理文件
    for i, file_path in enumerate(json_files):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查是否有python_student_c字段
            decision_logic = data.get('decisionLogic', {})
            python_student_c = decision_logic.get('python_student_c', {})
            
            if not python_student_c:
                stats['processed_files'] += 1
                continue
            
            stats['student_c_files'] += 1
            
            # 检查是否需要更新
            old_versions = []
            for version_key in list(python_student_c.keys()):
                if version_key.startswith('v_') and len(version_key) > 10:
                    old_versions.append(version_key)
            
            if old_versions:
                logger.debug(f"📝 需要更新: {file_path.name}")
                logger.debug(f"  旧版本: {old_versions}")
                
                if not dry_run:
                    # 合并所有旧版本的数据，保留最新的
                    merged_data = None
                    latest_timestamp = ""
                    
                    for old_version in old_versions:
                        version_data = python_student_c[old_version]
                        current_timestamp = version_data.get('timestamp', '')
                        
                        if not merged_data or current_timestamp > latest_timestamp:
                            merged_data = version_data.copy()
                            latest_timestamp = current_timestamp
                        
                        # 删除旧版本
                        del python_student_c[old_version]
                    
                    # 更新版本信息
                    if merged_data:
                        merged_data['studentVersion'] = target_version.replace('v', '')
                        merged_data['ruleVersion'] = target_version.replace('v', '')
                        
                        # 添加新版本
                        python_student_c[target_version] = merged_data
                        
                        # 写回文件
                        with open(file_path, 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        
                        stats['updated_files'] += 1
                        logger.debug(f"✅ 已更新: {file_path.name}")
            
            stats['processed_files'] += 1
            
            # 每1000个文件显示一次进度
            if (i + 1) % 1000 == 0:
                logger.info(f"📈 进度: {i + 1}/{len(json_files)}")
            
        except Exception as e:
            logger.error(f"❌ 处理文件失败 {file_path}: {e}")
            stats['error_files'] += 1
    
    # 显示统计结果
    print(f"\n📊 格式统一结果:")
    print("="*40)
    print(f"  总文件数: {stats['total_files']}")
    print(f"  处理文件数: {stats['processed_files']}")
    print(f"  有学生C记录: {stats['student_c_files']}")
    print(f"  更新文件数: {stats['updated_files']}")
    print(f"  错误文件数: {stats['error_files']}")
    
    if dry_run:
        print(f"\n⚠️ 这是试运行，没有实际修改文件")
        print(f"如需实际执行，请设置 dry_run=False")
    else:
        print(f"\n✅ 格式统一完成！")

def validate_unified_format(analysis_output_dir: str = "E:/LLMData/analysis_output", 
                           target_version: str = "v1.0"):
    """
    验证统一后的格式
    
    Args:
        analysis_output_dir: 分析输出目录
        target_version: 目标版本号
    """
    logger.info("✅ 验证统一后的格式...")
    
    analysis_dir = Path(analysis_output_dir)
    json_files = list(analysis_dir.glob("*.json"))
    
    stats = {
        'total_files': 0,
        'student_c_files': 0,
        'unified_files': 0,
        'old_format_files': 0,
        'error_files': 0
    }
    
    # 检查前1000个文件
    sample_files = json_files[:1000]
    
    for file_path in sample_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            stats['total_files'] += 1
            
            decision_logic = data.get('decisionLogic', {})
            python_student_c = decision_logic.get('python_student_c', {})
            
            if python_student_c:
                stats['student_c_files'] += 1
                
                versions = list(python_student_c.keys())
                has_target_version = target_version in versions
                has_old_format = any(v.startswith('v_') and len(v) > 10 for v in versions)
                
                if has_target_version and not has_old_format:
                    stats['unified_files'] += 1
                elif has_old_format:
                    stats['old_format_files'] += 1
            
        except Exception as e:
            logger.error(f"❌ 验证文件失败 {file_path}: {e}")
            stats['error_files'] += 1
    
    # 显示验证结果
    print(f"\n📊 格式验证结果 (样本: {len(sample_files)} 文件):")
    print("="*50)
    print(f"  总文件数: {stats['total_files']}")
    print(f"  有学生C记录: {stats['student_c_files']}")
    print(f"  已统一格式: {stats['unified_files']}")
    print(f"  仍为旧格式: {stats['old_format_files']}")
    print(f"  错误文件数: {stats['error_files']}")
    
    if stats['student_c_files'] > 0:
        unified_rate = stats['unified_files'] / stats['student_c_files'] * 100
        print(f"  统一率: {unified_rate:.1f}%")
        
        if unified_rate >= 95:
            print(f"\n✅ 格式统一成功！")
            return True
        else:
            print(f"\n⚠️ 格式统一不完整，建议重新执行")
            return False
    else:
        print(f"\n⚠️ 没有找到学生C记录")
        return False

def main():
    """主函数"""
    print("🔧 学生C记录格式统一工具")
    print("="*50)
    
    # 1. 试运行
    print("\n1️⃣ 试运行格式统一")
    unify_student_c_records(dry_run=True)
    
    # 2. 询问是否执行
    print("\n2️⃣ 确认执行")
    response = input("是否执行实际的格式统一？(y/N): ").strip().lower()
    
    if response == 'y':
        print("\n🚀 执行格式统一...")
        unify_student_c_records(dry_run=False)
        
        # 3. 验证结果
        print("\n3️⃣ 验证格式统一结果")
        is_success = validate_unified_format()
        
        if is_success:
            print("\n🎉 学生C记录格式统一完成！")
            print("现在所有记录都使用统一的v1.0版本格式")
        else:
            print("\n⚠️ 格式统一可能存在问题，请检查")
    else:
        print("\n⏹️ 取消执行，文件未修改")

if __name__ == "__main__":
    main()
