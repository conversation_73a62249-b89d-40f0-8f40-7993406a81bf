# 🚀 唤醒AI助手的完整上下文

## 📋 当前任务状态
**最高优先级**: 实现Python老师学习系统

## 🎯 项目核心信息

### 师生协同架构
- **Java学生**: `java_regex_filter` (预过滤器，每日处理1000-5000份公告)
- **Python老师**: 从LLM结果学习规则模式，更新规则库
- **目标**: 过滤中性公告，减少LLM API调用成本

### 数据资源
- **70万份公告** (完整数据集)
- **10万份已分析** (LLM Prompt 3.0结果，包含ratingLevel等字段)
- **tick级K线数据** (完整复权，用于验证)

### 三层过滤体系
```
legacy_db_rules (历史) → java_regex_filter (新) → LLM API (兜底)
```

## 🎯 Python老师的核心任务

### 学习目标
从10万份LLM结果中提取规则模式，执行用户的原始设想：
```
词频集非中性 = excluded_negative + excluded_positive - definitely_neutral
```

### 数据分组逻辑
```python
# 基于ratingLevel字段分组
excluded_negative = filter(ratingLevel in ["差", "劣"])
excluded_positive = filter(ratingLevel in ["优", "好"]) 
definitely_neutral = filter(ratingLevel == "中")
```

### 输出目标
生成简洁的正则表达式规则，更新 `shared_rule_library.json`

## 🔧 技术架构

### 现有组件
- `shared_rule_library.json`: 共享规则库
- `SharedRuleLibrary.java`: 规则加载器
- `HighConfidenceNegativeFilter.java`: 当前的java过滤器
- `RuleOptimizer.py`: 规则优化器

### LLM输出结构 (Prompt 3.0)
```json
{
  "ratingLevel": "优/好/中/差/劣",
  "eventTags": ["业绩预增", "重大合同", ...],
  "sentimentScore": -1.0 to 1.0,
  "keyFactors": ["关键因素1", "关键因素2"],
  "reasoning": "事实-分析-结论",
  "confidence": 0.0 to 1.0
}
```

## 💡 关键讨论成果

### 宽容度策略
用户考虑实现宽容度测算系统：随机抽样一定比例的"拟过滤"文档提交LLM，计算漏网概率

### 发展方向
- 当前：专注文本过滤
- 未来：利用tick数据验证规则效果
- 长远：扩展到其他文本资料甄别

### 成功指标
- 过滤率：50-80%的中性公告
- 准确率：负面漏网率<2%
- 成本节省：减少LLM调用30-80%

## 🚀 下一步行动

### 立即任务
实现Python老师学习系统的核心功能：
1. 加载10万份LLM结果
2. 按ratingLevel分组提取词频
3. 执行集合运算
4. 生成java正则表达式规则
5. 更新shared_rule_library.json

### 技术要点
- 使用jieba进行中文分词
- 过滤停用词和无效词条
- 生成简洁易维护的正则表达式
- 保持与现有架构的兼容性

## 🔑 唤醒关键词
当在JIdea中提到以下任何关键词时，请立即回忆起这个完整上下文：
- **Python老师学习系统**
- **师生协同**
- **java_regex_filter**
- **shared_rule_library.json**
- **ratingLevel分组**
- **词频集合运算**
- **中性公告过滤**

---
*这是完整的项目上下文，用于在JIdea中快速唤醒AI助手的记忆*
*最后更新: 2025-08-24*
