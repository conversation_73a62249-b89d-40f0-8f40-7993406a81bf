#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新Excel文件，加入词条质量分析结果
"""

import pandas as pd
import json
from pathlib import Path

def update_excel_with_quality():
    """更新Excel文件，加入词条质量分析结果"""
    
    # 文件路径
    base_dir = Path("D:\\LLMData\\PythonGram\\src\\tools\\data\\processed")
    json_path = base_dir / "word_frequency_analysis_results.json"
    quality_json_path = base_dir / "word_quality_analysis.json"
    excel_path = base_dir / "word_frequency_analysis_results.xlsx"
    
    # 加载数据
    print("正在加载数据...")
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            word_freq_data = json.load(f)
        print(f"✅ 成功加载词频数据: {json_path}")
    except Exception as e:
        print(f"❌ 加载词频数据失败: {e}")
        return
    
    try:
        with open(quality_json_path, 'r', encoding='utf-8') as f:
            quality_data = json.load(f)
        print(f"✅ 成功加载质量分析数据: {quality_json_path}")
    except Exception as e:
        print(f"❌ 加载质量分析数据失败: {e}")
        return
    
    # 创建新的Excel文件
    print("正在创建更新的Excel文件...")
    
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        
        # 1. 文件统计信息
        if 'file_stats' in word_freq_data:
            file_stats_df = pd.DataFrame([word_freq_data['file_stats']])
            file_stats_df.to_excel(writer, sheet_name='文件统计', index=False)
            print("✅ 已添加: 文件统计")
        
        # 2. 词条质量分析结果
        if 'word_quality' in quality_data:
            # 转换为DataFrame
            quality_list = []
            for word, info in quality_data['word_quality'].items():
                quality_list.append({
                    '词条': word,
                    '总频次': info['total_count'],
                    '负面频次': info['negative_count'],
                    '正面频次': info['positive_count'],
                    '中性频次': info['neutral_count'],
                    '非中性比例': f"{info['non_neutral_ratio']*100:.1f}%",
                    '质量分数': f"{info['quality_score']:.3f}",
                    '分布': f"N:{info['negative_count']} P:{info['positive_count']} M:{info['neutral_count']}"
                })
            
            quality_df = pd.DataFrame(quality_list)
            quality_df = quality_df.sort_values('质量分数', ascending=False)
            quality_df.to_excel(writer, sheet_name='词条质量分析', index=False)
            print("✅ 已添加: 词条质量分析")
        
        # 3. 高质量词条Top 200
        if 'top_quality_words' in quality_data:
            top_quality_list = []
            for item in quality_data['top_quality_words']:
                if isinstance(item, list) and len(item) == 2:
                    word, info = item
                    top_quality_list.append({
                        '排名': len(top_quality_list) + 1,
                        '词条': word,
                        '质量分数': f"{info['quality_score']:.3f}",
                        '总频次': info['total_count'],
                        '非中性比例': f"{info['non_neutral_ratio']*100:.1f}%",
                        '负面频次': info['negative_count'],
                        '正面频次': info['positive_count'],
                        '中性频次': info['neutral_count']
                    })
            
            top_quality_df = pd.DataFrame(top_quality_list)
            top_quality_df.to_excel(writer, sheet_name='高质量词条Top200', index=False)
            print("✅ 已添加: 高质量词条Top200")
        
        # 4. 问题词条分析
        if 'problem_words' in quality_data:
            problem_list = []
            for item in quality_data['problem_words']:
                if isinstance(item, list) and len(item) == 3:
                    word, neutral_ratio, info = item
                    problem_list.append({
                        '排名': len(problem_list) + 1,
                        '词条': word,
                        '中性比例': f"{neutral_ratio*100:.1f}%",
                        '总频次': info['total_count'],
                        '质量分数': f"{info['quality_score']:.3f}",
                        '负面频次': info['negative_count'],
                        '正面频次': info['positive_count'],
                        '中性频次': info['neutral_count']
                    })
            
            problem_df = pd.DataFrame(problem_list)
            problem_df.to_excel(writer, sheet_name='问题词条分析', index=False)
            print("✅ 已添加: 问题词条分析")
        
        # 5. 原有的词频统计（保留）
        if 'word_frequencies' in word_freq_data:
            # 负面词条Top 100
            negative_words = word_freq_data['word_frequencies']['negative']
            if isinstance(negative_words, dict):
                negative_list = sorted(negative_words.items(), key=lambda x: x[1], reverse=True)[:100]
            else:
                negative_list = [(word, count) for word, count in negative_words.most_common(100)]
            negative_df = pd.DataFrame(negative_list, columns=['词条', '频次'])
            negative_df.to_excel(writer, sheet_name='负面词条Top100', index=False)
            print("✅ 已添加: 负面词条Top100")
            
            # 正面词条Top 100
            positive_words = word_freq_data['word_frequencies']['positive']
            if isinstance(positive_words, dict):
                positive_list = sorted(positive_words.items(), key=lambda x: x[1], reverse=True)[:100]
            else:
                positive_list = [(word, count) for word, count in positive_words.most_common(100)]
            positive_df = pd.DataFrame(positive_list, columns=['词条', '频次'])
            positive_df.to_excel(writer, sheet_name='正面词条Top100', index=False)
            print("✅ 已添加: 正面词条Top100")
            
            # 中性词条Top 100
            neutral_words = word_freq_data['word_frequencies']['neutral']
            if isinstance(neutral_words, dict):
                neutral_list = sorted(neutral_words.items(), key=lambda x: x[1], reverse=True)[:100]
            else:
                neutral_list = [(word, count) for word, count in neutral_words.most_common(100)]
            neutral_df = pd.DataFrame(neutral_list, columns=['词条', '频次'])
            neutral_df.to_excel(writer, sheet_name='中性词条Top100', index=False)
            print("✅ 已添加: 中性词条Top100")
        
        # 6. 规则构建建议
        if 'top_quality_words' in quality_data:
            rule_suggestions = []
            
            # 强规则建议（质量分数 > 0.9）
            strong_rules = []
            for item in quality_data['top_quality_words']:
                if isinstance(item, list) and len(item) == 2:
                    word, info = item
                    if info['quality_score'] > 0.9:
                        strong_rules.append((word, info))
            
            for word, info in strong_rules[:50]:  # Top 50
                rule_suggestions.append({
                    '规则类型': '强规则',
                    '词条': word,
                    '正则表达式': f".*{word}.*",
                    '质量分数': f"{info['quality_score']:.3f}",
                    '非中性比例': f"{info['non_neutral_ratio']*100:.1f}%",
                    '总频次': info['total_count'],
                    '建议': '可直接使用，无需LLM确认'
                })
            
            # 中规则建议（质量分数 0.7-0.9）
            medium_rules = []
            for item in quality_data['top_quality_words']:
                if isinstance(item, list) and len(item) == 2:
                    word, info = item
                    if 0.7 <= info['quality_score'] <= 0.9:
                        medium_rules.append((word, info))
            
            for word, info in medium_rules[:30]:  # Top 30
                rule_suggestions.append({
                    '规则类型': '中规则',
                    '词条': word,
                    '正则表达式': f".*{word}.*",
                    '质量分数': f"{info['quality_score']:.3f}",
                    '非中性比例': f"{info['non_neutral_ratio']*100:.1f}%",
                    '总频次': info['total_count'],
                    '建议': '需要组合判断或LLM确认'
                })
            
            rule_df = pd.DataFrame(rule_suggestions)
            rule_df.to_excel(writer, sheet_name='规则构建建议', index=False)
            print("✅ 已添加: 规则构建建议")
    
    print(f"\n🎉 Excel文件更新完成！")
    print(f"📁 文件位置: {excel_path}")
    print(f"📊 新增工作表:")
    print(f"  - 词条质量分析")
    print(f"  - 高质量词条Top200")
    print(f"  - 问题词条分析")
    print(f"  - 规则构建建议")
    print(f"  - 负面词条Top100")
    print(f"  - 正面词条Top100")
    print(f"  - 中性词条Top100")

if __name__ == "__main__":
    update_excel_with_quality()
