#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel标注系统 - 完整实现
机器标注 + Excel人工标注 + 自动学习更新
"""

import json
import csv
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List

class ExcelAnnotationSystem:
    """Excel标注系统"""
    
    def __init__(self, lexicon_file: str = "E:/LLMData/research_archive/01_raw_data/production_lexicon.json"):
        """初始化系统"""
        self.version = "3.4.0"
        self.lexicon_file = lexicon_file
        self.load_lexicon()
        
        print(f"📊 Excel标注系统 v{self.version} 启动")
    
    def load_lexicon(self):
        """加载生产词库"""
        try:
            with open(self.lexicon_file, 'r', encoding='utf-8') as f:
                lexicon_data = json.load(f)
            
            word_lists = lexicon_data.get("word_lists", {})
            self.negative_words = set(word_lists.get("all_negative_words", []))
            self.positive_words = set(word_lists.get("all_positive_words", []))
            self.controversial_words = set(word_lists.get("controversial_words", []))
            
            # 从增强信息中加载争议词汇
            enhancements = lexicon_data.get("enhancements", {})
            if "critical_missing_words" in enhancements:
                critical = enhancements["critical_missing_words"]
                if "E_TERRIBLE" in critical and "争议词汇_负面优先" in critical["E_TERRIBLE"]:
                    controversial_priority = critical["E_TERRIBLE"]["争议词汇_负面优先"]
                    self.controversial_words.update(controversial_priority)
            
            print(f"✅ 加载词库: v{lexicon_data.get('version', 'unknown')}")
            print(f"   负面词汇: {len(self.negative_words)} 个")
            print(f"   正面词汇: {len(self.positive_words)} 个")
            print(f"   争议词汇: {len(self.controversial_words)} 个")
            
        except Exception as e:
            print(f"⚠️ 词库加载失败，使用内置词库: {e}")
            self._init_builtin_lexicon()
    
    def _init_builtin_lexicon(self):
        """初始化内置词库"""
        self.negative_words = {
            "减少", "下降", "降低", "缩减", "困难", "压力", "危机", "问题",
            "流失", "恶化", "萎缩", "衰退", "严峻", "疲软", "低迷",
            "退市", "亏损", "违规", "处罚", "风险", "损失"
        }
        self.positive_words = {
            "突破", "签署", "获得", "实现", "重大", "显著", "优异", "卓越",
            "合作", "协议", "增长", "提升", "改善", "优化", "成功", "创新"
        }
        self.controversial_words = {"减少", "下降", "降低", "缩减"}
    
    def machine_annotate(self, text: str) -> Dict:
        """机器标注"""
        if not text or not text.strip():
            return {
                "score": 0.0, "level": "C_NEUTRAL", "keywords": [],
                "controversial_words": [], "confidence": 0.0,
                "explanation": "文本为空"
            }
        
        negative_score = 0.0
        positive_score = 0.0
        signal_count = 0
        keywords = []
        controversial = []
        
        # 争议词汇检查（权重1.5）
        for word in self.controversial_words:
            if word in text:
                negative_score += 1.5
                signal_count += 1
                keywords.append(word)
                controversial.append(word)
        
        # 负面词汇检查（权重1.0）
        for word in self.negative_words:
            if word in text and word not in self.controversial_words:
                negative_score += 1.0
                signal_count += 1
                keywords.append(word)
        
        # 正面词汇检查（权重0.8）
        for word in self.positive_words:
            if word in text:
                positive_score += 0.8
                signal_count += 1
                keywords.append(word)
        
        # 计算最终分数
        if signal_count == 0:
            final_score = 0.0
        else:
            final_score = (positive_score - negative_score) / (signal_count + 1)
            final_score = max(-2.0, min(2.0, final_score))
        
        # 激进模式分级
        if controversial:
            level = "E_TERRIBLE"
        elif negative_score >= 2.0:
            level = "E_TERRIBLE"
        elif negative_score >= 1.0:
            level = "D_BAD"
        elif negative_score > 0:
            level = "C_NEGATIVE"
        elif positive_score >= 1.5:
            level = "A_EXCELLENT"
        elif positive_score > 0:
            level = "B_GOOD"
        else:
            level = "C_NEUTRAL"
        
        # 计算置信度
        confidence = 0.5
        if signal_count >= 3:
            confidence += 0.3
        elif signal_count >= 1:
            confidence += 0.2
        if controversial:
            confidence += 0.2
        if len(text) >= 20:
            confidence += 0.1
        confidence = min(1.0, confidence)
        
        # 生成解释
        explanations = []
        if controversial:
            explanations.append(f"包含争议词汇: {','.join(controversial)}")
        if negative_score > 0:
            explanations.append(f"负面信号: {negative_score:.1f}分")
        if positive_score > 0:
            explanations.append(f"正面信号: {positive_score:.1f}分")
        
        explanation = "; ".join(explanations) if explanations else "未检测到明显情感信号"
        
        return {
            "score": round(final_score, 3),
            "level": level,
            "keywords": keywords[:10],
            "controversial_words": controversial,
            "confidence": confidence,
            "explanation": explanation
        }
    
    def parse_original_file(self, file_path: str) -> List[Dict]:
        """解析原始标注文件"""
        print(f"📖 解析原始标注文件: {file_path}")
        
        data_list = []
        
        try:
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'ansi']
            lines = None

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        lines = f.readlines()
                    print(f"✅ 使用编码: {encoding}")
                    break
                except UnicodeDecodeError:
                    continue

            if lines is None:
                raise Exception("无法识别文件编码")
            
            data_started = False
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                if "文件名" in line and "Score" in line and "文摘" in line:
                    data_started = True
                    continue
                
                if not data_started:
                    continue
                
                # 解析数据行
                parts = re.split(r'\t+|\s{2,}', line)
                if len(parts) >= 3:
                    filename = parts[0].strip()
                    original_score = parts[1].strip()
                    summary = parts[2].strip()
                    
                    if filename.endswith(('.PDF', '.pdf', '.json')):
                        data_list.append({
                            "filename": filename,
                            "original_score": original_score,
                            "summary": summary
                        })
            
            print(f"✅ 解析完成，共 {len(data_list)} 条数据")
            return data_list
            
        except Exception as e:
            print(f"❌ 解析文件失败: {e}")
            return []
    
    def generate_excel_file(self, input_file: str, output_file: str = None) -> str:
        """生成Excel标注文件"""
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"标注文件V2_{timestamp}.txt"
        
        print(f"🏷️ 生成Excel标注文件...")
        print(f"   输入: {input_file}")
        print(f"   输出: {output_file}")
        
        # 解析原始文件
        data_list = self.parse_original_file(input_file)
        
        if not data_list:
            print("❌ 没有有效数据")
            return None
        
        # 定义表头
        headers = [
            "文件名", "原始Score", "文摘",
            "机器Score", "机器等级", "关键词", "争议词", "置信度", "机器理由",
            "人工Score", "人工等级", "人工理由", "质量评分", "备注"
        ]
        
        with open(output_file, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f, delimiter='\t', quoting=csv.QUOTE_MINIMAL)
            
            # 写入表头
            writer.writerow(headers)
            
            # 处理每条数据
            for i, data in enumerate(data_list):
                print(f"   处理进度: {i+1}/{len(data_list)} - {data['filename']}")
                
                # 机器标注
                machine_result = self.machine_annotate(data['summary'])
                
                # 写入数据行
                row = [
                    data['filename'],
                    data['original_score'],
                    data['summary'],
                    machine_result['score'],
                    machine_result['level'],
                    ','.join(machine_result['keywords']),
                    ','.join(machine_result['controversial_words']),
                    f"{machine_result['confidence']:.3f}",
                    machine_result['explanation'],
                    "",  # 人工Score - 待填写
                    "",  # 人工等级 - 待填写
                    "",  # 人工理由 - 待填写
                    "",  # 质量评分 - 待填写
                    ""   # 备注 - 待填写
                ]
                writer.writerow(row)
        
        print(f"✅ Excel标注文件生成完成: {output_file}")
        return output_file

    def create_excel_guide(self):
        """创建Excel标注指南"""
        guide_content = """# Excel标注指南

## 📊 使用步骤
1. 用Excel打开标注文件（选择制表符分隔）
2. 填写"人工Score"、"人工等级"、"人工理由"列
3. 对机器标注质量进行评分（1-5分）
4. 保存为制表符分隔的文本文件

## 📋 评分标准
### 人工Score (-2到2)
- -2: 极度负面
- -1: 负面
- 0: 中性
- 1: 正面
- 2: 极度正面

### 人工等级
- E_TERRIBLE: 极差
- D_BAD: 差
- C_NEUTRAL: 中性
- B_GOOD: 好
- A_EXCELLENT: 优秀

## 💡 标注技巧
- 重点关注争议词汇的案例
- 低置信度的机器标注需要仔细核查
- 相似内容保持标注一致性
- 详细说明重要判断的理由

## 🔧 Excel操作建议
### 数据验证
- 人工Score列：数据验证 → 整数 → -2到2
- 人工等级列：数据验证 → 列表 → E_TERRIBLE,D_BAD,C_NEUTRAL,B_GOOD,A_EXCELLENT

### 条件格式
- 按等级设置背景色便于识别
- E_TERRIBLE: 红色, D_BAD: 橙色, C_NEUTRAL: 黄色, B_GOOD: 浅绿, A_EXCELLENT: 绿色

### 筛选排序
- 按置信度排序：优先标注低置信度案例
- 按争议词筛选：重点关注争议词汇案例
- 按机器等级分组：批量处理相同等级的案例
"""

        with open("Excel标注指南.md", 'w', encoding='utf-8') as f:
            f.write(guide_content)

        print("📋 Excel标注指南已生成: Excel标注指南.md")

def main():
    """主函数"""
    print("📊 Excel标注系统")
    print("="*50)

    # 创建系统
    system = ExcelAnnotationSystem()

    # 生成Excel标注文件
    input_file = "E:/LLMData/research_archive/标注文件/标注文件V1.txt"

    print(f"\n🎯 处理标注文件: {input_file}")

    # 检查文件是否存在
    if not Path(input_file).exists():
        print(f"❌ 文件不存在: {input_file}")
        print("请确认文件路径是否正确")
        return

    # 生成Excel标注文件
    output_file = system.generate_excel_file(input_file)

    if output_file:
        # 生成指南
        system.create_excel_guide()

        print(f"\n🎉 Excel标注系统准备完成！")
        print(f"📁 标注文件: {output_file}")
        print(f"📋 操作指南: Excel标注指南.md")
        print(f"\n💡 下一步:")
        print(f"1. 用Excel打开 {output_file}")
        print(f"2. 按照指南进行人工标注")
        print(f"3. 保存后可用于词库更新")

        # 显示示例
        print(f"\n📝 标注示例:")
        print(f"文摘: '订单减少客户流失' → 机器判定: E_TERRIBLE (争议词汇)")
        print(f"文摘: '技术创新取得突破' → 机器判定: A_EXCELLENT (正面词汇)")

if __name__ == "__main__":
    main()
