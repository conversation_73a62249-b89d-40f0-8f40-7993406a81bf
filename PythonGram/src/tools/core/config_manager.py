#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
统一管理Python学生C的配置信息
支持路径映射、规则配置、性能参数等
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.config = {}
        self._load_config()
    
    def _load_config(self) -> None:
        """加载配置文件"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                logger.info(f"✅ 配置文件加载成功: {self.config_path}")
            else:
                logger.warning(f"⚠️ 配置文件不存在: {self.config_path}，将使用默认配置")
                self.config = self._get_default_config()
                self._save_default_config()
        except Exception as e:
            logger.error(f"❌ 配置文件加载失败: {e}")
            self.config = self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "metadata": {
                "version": "1.0.0",
                "description": "Python学生C配置文件",
                "created_by": "ConfigManager"
            },
            "path_mappings": [
                {
                    "name": "supmicro4_mapping",
                    "description": "supmicro4服务器映射",
                    "network_prefix": "\\\\supmicro4\\F\\",
                    "local_prefix": "F:\\",
                    "enabled": True
                }
            ],
            "filters": [
                {
                    "name": "HighConfidenceNegativeFilter",
                    "priority": 1,
                    "confidence_base": 0.95,
                    "confidence_increment": 0.02,
                    "confidence_max": 0.99,
                    "enabled": True,
                    "patterns": [
                        "警示函", "业绩预亏", "亏损", "违规", "处罚",
                        "风险提示", "停牌", "退市", "诉讼", "仲裁"
                    ]
                },
                {
                    "name": "HighConfidencePositiveFilter",
                    "priority": 2,
                    "confidence_base": 0.90,
                    "confidence_increment": 0.02,
                    "confidence_max": 0.98,
                    "enabled": True,
                    "patterns": [
                        "重大合同", "技术突破", "业绩增长", "盈利", "分红",
                        "收购", "合作协议", "新产品发布"
                    ],
                    "negative_check_patterns": [
                        "亏损", "违规", "风险", "警示"
                    ]
                },
                {
                    "name": "HighConfidenceNeutralFilter",
                    "priority": 3,
                    "confidence_base": 0.99,
                    "confidence_increment": 0.0,
                    "confidence_max": 0.99,
                    "enabled": True,
                    "patterns": [
                        "^.*股东大会.*$",
                        "^.*工商变更.*$",
                        "^.*董事会决议.*$",
                        "^.*监事会决议.*$",
                        "^.*年度报告.*$",
                        "^.*季度报告.*$"
                    ],
                    "negative_check_patterns": [
                        "亏损", "违规", "风险", "警示"
                    ]
                }
            ],
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s",
                "file_enabled": False,
                "file_path": "python_student_c.log"
            },
            "performance": {
                "enable_timing": True,
                "enable_caching": True,
                "max_content_length": 100000,
                "timeout_seconds": 30
            },
            "pdf_extraction": {
                "preferred_library": "pdfminer",
                "fallback_libraries": ["PyPDF2", "pypdf"],
                "max_file_size_mb": 50,
                "encoding": "utf-8"
            }
        }
    
    def _save_default_config(self) -> None:
        """保存默认配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            logger.info(f"✅ 默认配置已保存: {self.config_path}")
        except Exception as e:
            logger.error(f"❌ 保存默认配置失败: {e}")
    
    def get_path_mappings(self) -> List[Dict]:
        """获取路径映射配置"""
        mappings = self.config.get("path_mappings", [])
        # 只返回启用的映射
        return [m for m in mappings if m.get("enabled", True)]
    
    def get_filter_configs(self) -> List[Dict]:
        """获取过滤器配置"""
        filters = self.config.get("filters", [])
        # 只返回启用的过滤器，并按优先级排序
        enabled_filters = [f for f in filters if f.get("enabled", True)]
        return sorted(enabled_filters, key=lambda x: x.get("priority", 999))
    
    def get_logging_config(self) -> Dict:
        """获取日志配置"""
        return self.config.get("logging", {})
    
    def get_performance_config(self) -> Dict:
        """获取性能配置"""
        return self.config.get("performance", {})
    
    def get_pdf_extraction_config(self) -> Dict:
        """获取PDF提取配置"""
        return self.config.get("pdf_extraction", {})

    def get_versioning_config(self) -> Dict:
        """获取版本管理配置"""
        return self.config.get("versioning", {})

    def get_current_version(self) -> str:
        """获取当前版本号"""
        versioning = self.get_versioning_config()
        student_version = versioning.get("student_version", "1.0")
        return f"v{student_version}"

    def get_rule_version(self) -> str:
        """获取规则库版本号"""
        versioning = self.get_versioning_config()
        return versioning.get("rule_version", "1.0")

    def increment_version(self, version_type: str = "minor") -> str:
        """
        递增版本号

        Args:
            version_type: 版本类型 ("major" 或 "minor")

        Returns:
            新的版本号
        """
        try:
            versioning = self.get_versioning_config()
            current_version = versioning.get("student_version", "1.0")

            # 解析当前版本号
            parts = current_version.split('.')
            major = int(parts[0]) if len(parts) > 0 else 1
            minor = int(parts[1]) if len(parts) > 1 else 0

            # 递增版本号
            if version_type == "major":
                major += 1
                minor = 0
            else:  # minor
                minor += 1

            new_version = f"{major}.{minor}"

            # 更新配置
            self.update_config("versioning.student_version", new_version)
            self.update_config("versioning.rule_version", new_version)

            logger.info(f"✅ 版本号已更新: {current_version} → {new_version}")
            return new_version

        except Exception as e:
            logger.error(f"❌ 版本号递增失败: {e}")
            return self.get_current_version()
    
    def get_filter_config_by_name(self, filter_name: str) -> Optional[Dict]:
        """根据名称获取过滤器配置"""
        for filter_config in self.get_filter_configs():
            if filter_config.get("name") == filter_name:
                return filter_config
        return None
    
    def update_config(self, key_path: str, value: Any) -> bool:
        """
        更新配置值
        
        Args:
            key_path: 配置键路径，如 "performance.enable_timing"
            value: 新值
            
        Returns:
            是否更新成功
        """
        try:
            keys = key_path.split('.')
            current = self.config
            
            # 导航到目标位置
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
            
            # 设置值
            current[keys[-1]] = value
            
            # 保存到文件
            self._save_config()
            logger.info(f"✅ 配置更新成功: {key_path} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 配置更新失败: {e}")
            return False
    
    def _save_config(self) -> None:
        """保存配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"❌ 保存配置失败: {e}")
            raise
    
    def add_path_mapping(self, name: str, network_prefix: str, local_prefix: str, 
                        description: str = "", enabled: bool = True) -> bool:
        """
        添加路径映射
        
        Args:
            name: 映射名称
            network_prefix: 网络路径前缀
            local_prefix: 本地路径前缀
            description: 描述
            enabled: 是否启用
            
        Returns:
            是否添加成功
        """
        try:
            new_mapping = {
                "name": name,
                "description": description,
                "network_prefix": network_prefix,
                "local_prefix": local_prefix,
                "enabled": enabled
            }
            
            if "path_mappings" not in self.config:
                self.config["path_mappings"] = []
            
            self.config["path_mappings"].append(new_mapping)
            self._save_config()
            
            logger.info(f"✅ 路径映射添加成功: {name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 添加路径映射失败: {e}")
            return False
    
    def get_config_summary(self) -> Dict:
        """获取配置摘要"""
        return {
            "config_file": str(self.config_path),
            "config_exists": self.config_path.exists(),
            "path_mappings_count": len(self.get_path_mappings()),
            "enabled_filters_count": len(self.get_filter_configs()),
            "logging_level": self.get_logging_config().get("level", "INFO"),
            "pdf_preferred_library": self.get_pdf_extraction_config().get("preferred_library", "pdfminer")
        }
    
    def validate_config(self) -> List[str]:
        """
        验证配置的有效性
        
        Returns:
            验证错误列表，空列表表示配置有效
        """
        errors = []
        
        # 验证路径映射
        path_mappings = self.get_path_mappings()
        for i, mapping in enumerate(path_mappings):
            if not mapping.get("network_prefix"):
                errors.append(f"路径映射 {i}: network_prefix 不能为空")
            if not mapping.get("local_prefix"):
                errors.append(f"路径映射 {i}: local_prefix 不能为空")
        
        # 验证过滤器配置
        filter_configs = self.get_filter_configs()
        for filter_config in filter_configs:
            name = filter_config.get("name", "未知")
            if not filter_config.get("patterns"):
                errors.append(f"过滤器 {name}: patterns 不能为空")
            
            confidence_base = filter_config.get("confidence_base", 0)
            if not (0 <= confidence_base <= 1):
                errors.append(f"过滤器 {name}: confidence_base 必须在 0-1 之间")
        
        return errors

def test_config_manager():
    """测试配置管理器"""
    print("🧪 测试配置管理器...")
    
    # 创建测试配置文件
    test_config_path = "test_config.json"
    config_manager = ConfigManager(test_config_path)
    
    # 显示配置摘要
    summary = config_manager.get_config_summary()
    print(f"📊 配置摘要: {summary}")
    
    # 测试路径映射
    path_mappings = config_manager.get_path_mappings()
    print(f"🗂️ 路径映射数量: {len(path_mappings)}")
    
    # 测试过滤器配置
    filter_configs = config_manager.get_filter_configs()
    print(f"🔍 过滤器数量: {len(filter_configs)}")
    
    # 验证配置
    errors = config_manager.validate_config()
    if errors:
        print(f"❌ 配置验证失败: {errors}")
    else:
        print("✅ 配置验证通过")
    
    # 清理测试文件
    import os
    if os.path.exists(test_config_path):
        os.remove(test_config_path)
        print(f"🧹 清理测试文件: {test_config_path}")

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
    )
    
    test_config_manager()
