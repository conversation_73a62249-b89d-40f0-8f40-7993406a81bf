#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
负面纯度分析器 (Negative Purity Analyzer)

职责:
1. 接收按评级分类的数据。
2. 提取有意义的词汇。
3. 计算每个词汇的负面纯度、区分度和排他性。
4. 筛选并返回高纯度的负面关键词。
"""

import logging
import re
from collections import defaultdict
from typing import Dict, List

import pandas as pd

logger = logging.getLogger(__name__)


class NegativePurityAnalyzer:
    """封装了所有与负面纯度相关的分析逻辑"""

    def __init__(self):
        logger.info(" purity_analyzer 初始化完成")

    def _extract_meaningful_words(self, text: str) -> List[str]:
        """提取有意义的词汇 - 优化版，提取更通用的关键词"""
        if not text:
            return []

        # 1. 提取核心负面关键词（优先级最高）
        core_negative_keywords = [
            '亏损', '违规', '违法', '处罚', '警示', '风险', '退市', '停牌', '破产',
            '立案', '调查', '诉讼', '仲裁', '冻结', '查封', '减持', '下降', '下滑',
            '预降', '大幅', '严重', '异常', '暂停', '终止', '监管', '关注', '问询',
            '行政', '刑事', '拘留', '罚款', '谴责', '整改', '限制', '禁止'
        ]

        # 2. 提取核心正面关键词
        core_positive_keywords = [
            '增长', '盈利', '合同', '突破', '收购', '分红', '中标', '签署',
            '业绩', '净利润', '营业收入', '技术', '新产品', '合作', '协议',
            '投资', '扩张', '发展', '创新', '优化', '提升', '改善'
        ]

        # 3. 提取核心中性关键词
        core_neutral_keywords = [
            '股东大会', '董事会', '监事会', '决议', '年度', '季度', '定期',
            '临时', '召开', '审议', '选举', '聘任', '章程', '制度'
        ]

        all_core_keywords = core_negative_keywords + core_positive_keywords + core_neutral_keywords

        # 4. 从文本中查找核心关键词
        found_keywords = []
        text_lower = text.lower()

        for keyword in all_core_keywords:
            if keyword in text_lower:
                found_keywords.append(keyword)

        # 5. 补充提取：通用的中文词汇（2-4个字符，避免过长的片段）
        chinese_words = re.findall(r'[\u4e00-\u9fff]{2,4}', text)

        # 过滤停用词和过于通用的词汇
        stop_words = {
            '公司', '股份', '有限', '公告', '通知', '决议', '报告', '说明',
            '关于', '根据', '按照', '依据', '为了', '由于', '因为', '所以',
            '但是', '然而', '同时', '此外', '另外', '其中', '包括', '具体',
            '主要', '重要', '相关', '有关', '进行', '实施', '执行', '完成',
            '我们', '他们', '这个', '那个', '什么', '怎么', '如何', '可以',
            '应该', '需要', '必须', '已经', '正在', '将要', '可能', '或者',
            '以及', '并且', '而且', '不过', '只是', '就是', '还是', '都是'
        }

        # 过滤通用词汇，只保留可能有意义的
        filtered_words = []
        for word in chinese_words:
            if (word not in stop_words and
                    len(word) >= 2 and
                    not word.isdigit() and
                    word not in found_keywords):  # 避免重复
                filtered_words.append(word)

        # 6. 合并核心关键词和过滤后的词汇
        meaningful_words = found_keywords + filtered_words

        # 7. 去重并返回
        return list(set(meaningful_words))

    def analyze_word_purity(self, rating_analysis: Dict) -> pd.DataFrame:
        """分析词汇纯度"""
        word_stats = defaultdict(lambda: {'negative': 0, 'positive': 0, 'neutral': 0})

        for rating, items in rating_analysis.items():
            for item in items:
                text_parts = [
                    item.get('title', ''), item.get('summary', ''),
                    str(item.get('keyFactors', '')), str(item.get('riskFactors', '')),
                    str(item.get('eventTags', '')), item.get('reasoning', '')
                ]
                full_text = ' '.join([t for t in text_parts if t])
                words = self._extract_meaningful_words(full_text)

                for word in words:
                    word_stats[word][rating.lower()] += 1

        word_data = []
        for word, stats in word_stats.items():
            total_docs = stats['negative'] + stats['positive'] + stats['neutral']
            if total_docs == 0: continue

            purity = stats['negative'] / total_docs
            max_other = max(stats['positive'], stats['neutral'])
            discrimination = (stats['negative'] - max_other) / total_docs
            exclusivity = 1 - (stats['positive'] + stats['neutral']) / total_docs if stats['negative'] > 0 else -1

            word_data.append({
                'word': word,
                'negative_purity': purity,
                'discrimination': discrimination,
                'exclusivity': exclusivity,
                'total_docs': total_docs,
                'negative_docs': stats['negative'],
                'positive_docs': stats['positive'],
                'neutral_docs': stats['neutral'],
                'matches_user_pattern': word in self._extract_meaningful_words('')  # 示例，实际应有更复杂的逻辑
            })

        df = pd.DataFrame(word_data)
        if not df.empty:
            df = df.sort_values(['negative_purity', 'discrimination'], ascending=[False, False])
        return df

    def get_high_purity_keywords(self, purity_df: pd.DataFrame, purity_threshold: float,
                                 discrimination_threshold: float, min_docs: int) -> pd.DataFrame:
        """筛选高纯度关键词"""
        if purity_df.empty:
            return pd.DataFrame()
        return purity_df[
            (purity_df['negative_purity'] >= purity_threshold) &
            (purity_df['discrimination'] >= discrimination_threshold) &
            (purity_df['total_docs'] >= min_docs)
            ]

    def generate_purity_report(self, purity_df: pd.DataFrame):
        """生成纯度报告"""
        if purity_df.empty:
            logger.info("纯度分析结果为空，无法生成报告。")
            return

        logger.info("\n--- 负面纯度分析报告 ---")
        logger.info(f"分析了 {len(purity_df)} 个词汇。")
        top_10 = purity_df.head(10)
        logger.info("Top 10 高纯度负面词汇:")
        for _, row in top_10.iterrows():
            logger.info(
                f"  - {row['word']:<10} | 纯度: {row['negative_purity']:.2f} | "
                f"区分度: {row['discrimination']:.2f} | "
                f"出现文档数: {row['total_docs']}"
            )
        logger.info("-------------------------\n")