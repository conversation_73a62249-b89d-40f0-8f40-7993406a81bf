#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级特征纯度分析器 (Advanced Feature Purity Analyzer)

职责:
1. 接收来自 python_teacher_learning_v2 的词汇评分DataFrame。
2. 基于卡方值计算每个词条对特定类别（负面、正面、中性）的“统计纯度”。
3. 筛选出高纯度的、具有强区分能力的特征词。
4. 生成一份详尽的、可指导规则制定的Excel分析报告。
"""

import logging
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict

logger = logging.getLogger(__name__)

class FeaturePurityAnalyzer:
    """基于统计显著性计算特征纯度的分析器"""
    
    def __init__(self, word_scores_df: pd.DataFrame):
        """
        初始化分析器
        
        Args:
            word_scores_df: 来自 InformationGainScorer 的包含统计评分的DataFrame。
        """
        if word_scores_df is None or word_scores_df.empty:
            raise ValueError("输入的 word_scores_df 为空或无效。")
        self.df = word_scores_df.copy()
        logger.info("📊 高级特征纯度分析器初始化完成。")

    def analyze_purity(self, p_value_threshold: float = 0.05, min_doc_count: int = 3):
        """
        计算每个词条的“纯度”并筛选出高质量特征。
        """
        logger.info("🚀 开始计算特征纯度...")

        # 1. 基础过滤：确保统计显著性 (P值足够小) 且有一定支持度
        # 我们只关心与“负面”或“正面”显著相关的词
        significant_mask = (
            (self.df['P值_负向'] < p_value_threshold) | 
            (self.df['P值_正向'] < p_value_threshold)
        ) & (self.df['总文档数'] >= min_doc_count)

        self.df = self.df[significant_mask].copy()
        logger.info(f"经过P值和最低文档数过滤后，剩余 {len(self.df)} 个候选特征。")

        if self.df.empty:
            logger.warning("没有找到足够显著的特征词进行纯度分析。")
            return

        # 2. 计算卡方总分和各类别的“纯度”
        # 添加一个极小值 epsilon 防止除以零
        epsilon = 1e-9
        chi2_sum = self.df['卡方值_负向'] + self.df['卡方值_正向'] + self.df['卡方值_中性'] + epsilon
        
        self.df['负面纯度'] = self.df['卡方值_负向'] / chi2_sum
        self.df['正面纯度'] = self.df['卡方值_正向'] / chi2_sum
        self.df['中性纯度'] = self.df['卡方值_中性'] / chi2_sum

        # 3. 确定每个词条的主要倾向类别
        self.df['主要倾向'] = self.df[['负面纯度', '正面纯度', '中性纯度']].idxmax(axis=1).str.replace('纯度', '')

        logger.info("✅ 特征纯度计算完成。")

    def get_pure_keywords(self, category: str, purity_threshold: float = 0.7, top_n: int = 100) -> pd.DataFrame:
        """
        获取指定类别的高纯度关键词列表。

        Args:
            category (str): '负面', '正面', 或 '中性'.
            purity_threshold (float): 纯度阈值。
            top_n (int): 返回前N个结果。

        Returns:
            pd.DataFrame: 筛选并排序后的高纯度关键词DataFrame。
        """
        if f'{category}纯度' not in self.df.columns:
            logger.error(f"无效的类别: {category}。请从 ['负面', '正面', '中性'] 中选择。")
            return pd.DataFrame()

        purity_col = f'{category}纯度'
        chi2_col = f'卡方值_{category}'

        # 筛选出主要倾向为该类别，且纯度达标的词条
        filtered_df = self.df[
            (self.df['主要倾向'] == category) &
            (self.df[purity_col] >= purity_threshold)
        ].copy()

        # 按该类别的卡方值排序
        sorted_df = filtered_df.sort_values(by=chi2_col, ascending=False)

        return sorted_df.head(top_n)

    def save_purity_report(self, output_path: str):
        """保存到Excel文件，按类别分工作表"""
        if self.df.empty or '负面纯度' not in self.df.columns:
            logger.warning("⚠️ 没有数据可保存")
            return
        
        output_path_obj = Path(output_path)
        output_path_obj.parent.mkdir(parents=True, exist_ok=True)

        with pd.ExcelWriter(output_path_obj, engine='openpyxl') as writer:
            # 1. 保存完整的分析结果
            self.df.sort_values(by='卡方总分', ascending=False).to_excel(writer, sheet_name='总览_按卡方总分排序', index=False)

            # 2. 为每个类别保存高纯度词汇列表
            for category in ['负面', '正面', '中性']:
                pure_keywords_df = self.get_pure_keywords(category, purity_threshold=0.7, top_n=500)
                if not pure_keywords_df.empty:
                    sheet_name = f'高纯度_{category}词'
                    pure_keywords_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        logger.info(f"💾 特征纯度分析报告已保存: {output_path}")

def main():
    """主函数"""
    # 这是一个示例用法，展示如何将它与 python_teacher_learning_v2 集成
    from python_teacher_learning_v2 import PythonTeacherLearningV2

    logger.info("步骤 1: 运行主学习流程以获取词汇评分...")
    # 假设这是主流程的入口
    teacher = PythonTeacherLearningV2(
        data_dir="E:/LLMData/analysis_output",
        output_dir="E:/LLMData/PythonGram/src/tools/output"
    )
    word_scores_df = teacher.run_complete_analysis()

    if word_scores_df is not None and not word_scores_df.empty:
        logger.info("\n步骤 2: 使用高级特征纯度分析器进行深度分析...")
        purity_analyzer = FeaturePurityAnalyzer(word_scores_df)
        purity_analyzer.analyze_purity()

        # 获取并打印高纯度负面词示例
        pure_negative_keywords = purity_analyzer.get_pure_keywords('负面', top_n=20)
        logger.info("--- Top 20 高纯度负面关键词 ---")
        logger.info("\n" + pure_negative_keywords[['词条', '负面纯度', '卡方值_负向']].to_string())

        # 保存完整的纯度分析报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = f"E:/LLMData/PythonGram/src/tools/output/feature_purity_report_{timestamp}.xlsx"
        purity_analyzer.save_purity_report(report_path)

if __name__ == "__main__":
    main()
