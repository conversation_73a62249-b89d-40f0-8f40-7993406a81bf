#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的词库效果
对比原始词库和基于真实数据优化的词库的性能
"""

import json
import time
from pathlib import Path
from typing import Dict, List, Tuple
from dataclasses import dataclass

@dataclass
class TestResult:
    """测试结果"""
    text: str
    original_result: str
    optimized_result: str
    original_confidence: float
    optimized_confidence: float
    original_signals: List[str]
    optimized_signals: List[str]
    improvement: str

class OptimizedLexiconTester:
    """优化词库测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.original_lexicon = None
        self.optimized_lexicon = None
        self.load_lexicons()
    
    def load_lexicons(self):
        """加载原始和优化后的词库"""
        # 加载原始词库
        original_files = [
            "financial_negative_words.json",
            "financial_negative_lexicon.json"
        ]
        
        for file_path in original_files:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        self.original_lexicon = json.load(f)
                    print(f"✅ 加载原始词库: {file_path}")
                    break
                except Exception as e:
                    print(f"⚠️ 加载原始词库失败 {file_path}: {e}")
        
        # 加载优化词库
        optimized_file = "optimized_financial_lexicon.json"
        if Path(optimized_file).exists():
            try:
                with open(optimized_file, 'r', encoding='utf-8') as f:
                    self.optimized_lexicon = json.load(f)
                print(f"✅ 加载优化词库: {optimized_file}")
            except Exception as e:
                print(f"❌ 加载优化词库失败: {e}")
        else:
            print(f"❌ 优化词库文件不存在: {optimized_file}")
    
    def classify_with_original(self, text: str) -> Tuple[str, float, List[str]]:
        """使用原始词库分类"""
        if not self.original_lexicon:
            return "UNKNOWN", 0.0, []
        
        # 获取原始词库的负面词汇
        negative_words = []
        if "all_negative_words" in self.original_lexicon:
            negative_words = self.original_lexicon["all_negative_words"]
        elif "high_priority_words" in self.original_lexicon:
            negative_words = self.original_lexicon["high_priority_words"]
        
        # 检查匹配
        matched_words = []
        for word in negative_words:
            if word in text:
                matched_words.append(word)
        
        # 简单分类逻辑
        if len(matched_words) == 0:
            return "NEUTRAL", 0.0, []
        elif len(matched_words) >= 2:
            return "NEGATIVE", 0.8, matched_words
        else:
            return "NEGATIVE", 0.6, matched_words
    
    def classify_with_optimized(self, text: str) -> Tuple[str, float, List[str]]:
        """使用优化词库分类"""
        if not self.optimized_lexicon:
            return "UNKNOWN", 0.0, []
        
        categories = self.optimized_lexicon.get("categories", {})
        matched_signals = []
        category_scores = {}
        
        # 遍历所有类别
        for category, category_data in categories.items():
            atomic_signals = category_data.get("atomic_signals", {})
            category_matches = []
            
            # 检查各长度的原子信号
            for length_key, words in atomic_signals.items():
                for word in words:
                    if word in text:
                        category_matches.append(word)
                        matched_signals.append(word)
            
            # 计算类别分数
            if category_matches:
                priority_level = category_data.get("priority_level", 1)
                match_count = len(category_matches)
                category_scores[category] = priority_level * match_count
        
        # 确定最终分类 - 调整为更激进的策略
        if not category_scores:
            return "NEUTRAL", 0.0, []

        # 计算各大类的总分
        negative_score = category_scores.get("E_TERRIBLE", 0) + category_scores.get("D_BAD", 0)
        positive_score = category_scores.get("A_EXCELLENT", 0) + category_scores.get("B_GOOD", 0)
        neutral_score = category_scores.get("C_NEUTRAL", 0)

        # 激进策略：只要有负面信号就优先考虑
        if negative_score > 0:
            result_category = "NEGATIVE"
            confidence = min(0.95, 0.7 + negative_score * 0.05)
        elif positive_score > neutral_score:
            result_category = "POSITIVE"
            confidence = min(0.90, 0.6 + positive_score * 0.05)
        else:
            result_category = "NEUTRAL"
            confidence = min(0.85, 0.5 + neutral_score * 0.05)

        return result_category, confidence, matched_signals
    
    def run_comprehensive_test(self) -> List[TestResult]:
        """运行综合测试"""
        print("🧪 开始综合测试...")
        
        # 测试案例 - 涵盖各种情况
        test_cases = [
            # 明显负面案例
            "公司股票进入退市整理期",
            "净利润同比由盈转亏",
            "收到证监会立案调查通知",
            "公司面临严重亏损困难",
            "因违规被监管部门处罚",
            
            # 细粒度负面案例
            "业绩下滑收入减少",
            "市场竞争加剧压力增大",
            "资金紧张现金流不足",
            "订单减少客户流失",
            "成本上升利润下降",
            
            # 边界案例
            "公司经营遇到困难",
            "面临市场风险挑战",
            "业务发展存在压力",
            "收入略有下降",
            "利润出现减少",
            
            # 中性案例
            "股东大会审议年度报告",
            "董事会决议通过议案",
            "公司发布季度公告",
            "召开投资者交流会",
            "完成工商变更登记",
            
            # 正面案例
            "业绩大幅增长超预期",
            "净利润显著提升",
            "市场份额持续扩大",
            "技术创新取得突破",
            "签署重大合作协议"
        ]
        
        results = []
        
        for text in test_cases:
            # 使用原始词库分类
            orig_category, orig_confidence, orig_signals = self.classify_with_original(text)
            
            # 使用优化词库分类
            opt_category, opt_confidence, opt_signals = self.classify_with_optimized(text)
            
            # 判断改进情况
            improvement = self._analyze_improvement(
                text, orig_category, opt_category, orig_confidence, opt_confidence
            )
            
            result = TestResult(
                text=text,
                original_result=orig_category,
                optimized_result=opt_category,
                original_confidence=orig_confidence,
                optimized_confidence=opt_confidence,
                original_signals=orig_signals[:3],  # 只显示前3个
                optimized_signals=opt_signals[:3],  # 只显示前3个
                improvement=improvement
            )
            
            results.append(result)
        
        return results
    
    def _analyze_improvement(self, text: str, orig_cat: str, opt_cat: str, 
                           orig_conf: float, opt_conf: float) -> str:
        """分析改进情况"""
        # 基于文本内容判断预期分类
        negative_indicators = ["退市", "亏损", "违规", "处罚", "调查", "下滑", "减少", "困难", "风险", "压力"]
        positive_indicators = ["增长", "提升", "扩大", "突破", "合作", "超预期"]
        neutral_indicators = ["股东大会", "董事会", "审议", "公告", "召开", "登记"]
        
        expected = "NEUTRAL"
        if any(indicator in text for indicator in negative_indicators):
            expected = "NEGATIVE"
        elif any(indicator in text for indicator in positive_indicators):
            expected = "POSITIVE"
        elif any(indicator in text for indicator in neutral_indicators):
            expected = "NEUTRAL"
        
        # 分析改进
        if orig_cat == opt_cat == expected:
            if opt_conf > orig_conf:
                return "✅ 置信度提升"
            else:
                return "✅ 分类正确"
        elif orig_cat != expected and opt_cat == expected:
            return "🎯 修正错误"
        elif orig_cat == expected and opt_cat != expected:
            return "⚠️ 引入错误"
        elif orig_cat != expected and opt_cat != expected:
            if opt_cat == "NEGATIVE":  # 宁可错杀不可放过
                return "🛡️ 保守策略"
            else:
                return "❓ 仍有偏差"
        else:
            return "➡️ 无明显变化"
    
    def print_test_results(self, results: List[TestResult]):
        """打印测试结果"""
        print(f"\n📊 测试结果对比:")
        print(f"{'文本':<35} {'原始':<10} {'优化':<10} {'原始置信度':<10} {'优化置信度':<10} {'改进情况'}")
        print("-" * 120)
        
        improvement_stats = {}
        
        for result in results:
            print(f"{result.text[:33]:<35} "
                  f"{result.original_result:<10} "
                  f"{result.optimized_result:<10} "
                  f"{result.original_confidence:<10.3f} "
                  f"{result.optimized_confidence:<10.3f} "
                  f"{result.improvement}")
            
            # 统计改进情况
            improvement_stats[result.improvement] = improvement_stats.get(result.improvement, 0) + 1
        
        # 显示统计摘要
        print(f"\n📈 改进统计:")
        for improvement, count in improvement_stats.items():
            print(f"   {improvement}: {count} 个案例")
        
        # 计算召回率改进
        negative_cases = [r for r in results if any(word in r.text for word in 
                         ["退市", "亏损", "违规", "处罚", "调查", "下滑", "减少", "困难"])]
        
        orig_negative_detected = len([r for r in negative_cases if r.original_result == "NEGATIVE"])
        opt_negative_detected = len([r for r in negative_cases if r.optimized_result == "NEGATIVE"])
        
        if negative_cases:
            orig_recall = orig_negative_detected / len(negative_cases) * 100
            opt_recall = opt_negative_detected / len(negative_cases) * 100
            
            print(f"\n🎯 召回率分析 (负面案例):")
            print(f"   原始词库召回率: {orig_recall:.1f}% ({orig_negative_detected}/{len(negative_cases)})")
            print(f"   优化词库召回率: {opt_recall:.1f}% ({opt_negative_detected}/{len(negative_cases)})")
            print(f"   召回率提升: {opt_recall - orig_recall:+.1f}%")

def main():
    """主函数"""
    print("🚀 优化词库效果测试")
    print("=" * 60)
    
    # 创建测试器
    tester = OptimizedLexiconTester()
    
    if not tester.original_lexicon or not tester.optimized_lexicon:
        print("❌ 词库加载失败，无法进行测试")
        return
    
    # 显示词库信息
    print(f"\n📚 词库信息:")
    if tester.original_lexicon:
        orig_version = tester.original_lexicon.get("version", "unknown")
        orig_words = len(tester.original_lexicon.get("all_negative_words", []))
        print(f"   原始词库: v{orig_version}, {orig_words} 个词汇")
    
    if tester.optimized_lexicon:
        opt_version = tester.optimized_lexicon.get("version", "unknown")
        opt_stats = tester.optimized_lexicon.get("statistics", {})
        opt_total = opt_stats.get("total_signals", 0)
        print(f"   优化词库: v{opt_version}, {opt_total} 个信号")
    
    # 运行测试
    results = tester.run_comprehensive_test()
    
    # 显示结果
    tester.print_test_results(results)
    
    print(f"\n✅ 测试完成！基于真实数据的优化词库已验证。")
    print(f"🎯 优化策略: 宁可错杀不可放过 - 提升负面内容召回率")

if __name__ == "__main__":
    main()
