#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构后的系统架构
整理和优化所有核心组件，提供清晰的API接口
"""

import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ClassificationResult:
    """标准化分类结果"""
    category: str                    # 分类结果：E_TERRIBLE, D_BAD, A_EXCELLENT, B_GOOD, C_NEUTRAL, UNCERTAIN
    confidence: float               # 置信度：0.0-1.0
    method: str                     # 分类方法：atomic_signal, semantic_context, hybrid
    matched_signals: List[str]      # 匹配的信号
    processing_time: float          # 处理时间（秒）
    explanation: str                # 分类解释
    metadata: Dict                  # 额外元数据
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return asdict(self)
    
    def to_json(self) -> str:
        """转换为JSON格式"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)

class BaseClassifier(ABC):
    """分类器基类"""
    
    @abstractmethod
    def classify(self, title: str, content: str = "") -> ClassificationResult:
        """执行分类"""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """获取分类器名称"""
        pass
    
    @abstractmethod
    def get_version(self) -> str:
        """获取分类器版本"""
        pass

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "classifier_config.json"):
        """初始化配置管理器"""
        self.config_file = Path(config_file)
        self.config = self._load_config()
    
    def _load_config(self) -> Dict:
        """加载配置"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"加载配置失败: {e}，使用默认配置")
        
        return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "version": "1.0.0",
            "atomic_signal": {
                "lexicon_file": "atomic_signal_lexicon.json",
                "min_confidence": 0.7,
                "enable_carrier_filter": True
            },
            "semantic_context": {
                "window_size": 10,
                "enable_distance_weighting": True,
                "carrier_words_file": None
            },
            "hybrid": {
                "atomic_priority_threshold": 0.8,
                "enable_conflict_resolution": True,
                "method_weights": {
                    "atomic_signal": 0.7,
                    "semantic_context": 0.3
                }
            },
            "performance": {
                "enable_caching": True,
                "max_cache_size": 1000,
                "enable_batch_processing": True
            },
            "logging": {
                "level": "INFO",
                "enable_detailed_trace": False
            }
        }
    
    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            logger.info(f"配置已保存: {self.config_file}")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        """初始化性能监控器"""
        self.stats = {
            'total_classifications': 0,
            'method_usage': {},
            'category_distribution': {},
            'processing_times': [],
            'confidence_scores': [],
            'error_count': 0
        }
    
    def record_classification(self, result: ClassificationResult):
        """记录分类结果"""
        self.stats['total_classifications'] += 1
        
        # 记录方法使用
        method = result.method
        self.stats['method_usage'][method] = self.stats['method_usage'].get(method, 0) + 1
        
        # 记录类别分布
        category = result.category
        self.stats['category_distribution'][category] = self.stats['category_distribution'].get(category, 0) + 1
        
        # 记录性能指标
        self.stats['processing_times'].append(result.processing_time)
        self.stats['confidence_scores'].append(result.confidence)
    
    def record_error(self, error: Exception):
        """记录错误"""
        self.stats['error_count'] += 1
        logger.error(f"分类错误: {error}")
    
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        if self.stats['total_classifications'] == 0:
            return {"message": "暂无分类数据"}
        
        total = self.stats['total_classifications']
        
        return {
            "总分类次数": total,
            "错误率": f"{self.stats['error_count']/total*100:.2f}%",
            "平均处理时间": f"{sum(self.stats['processing_times'])/len(self.stats['processing_times'])*1000:.2f}ms",
            "平均置信度": f"{sum(self.stats['confidence_scores'])/len(self.stats['confidence_scores']):.3f}",
            "方法使用分布": {
                method: f"{count} ({count/total*100:.1f}%)" 
                for method, count in self.stats['method_usage'].items()
            },
            "类别分布": {
                category: f"{count} ({count/total*100:.1f}%)" 
                for category, count in self.stats['category_distribution'].items()
            }
        }

class ClassifierFactory:
    """分类器工厂"""
    
    def __init__(self, config_manager: ConfigManager):
        """初始化分类器工厂"""
        self.config_manager = config_manager
        self._classifiers = {}
    
    def create_classifier(self, classifier_type: str) -> Optional[BaseClassifier]:
        """创建分类器"""
        if classifier_type in self._classifiers:
            return self._classifiers[classifier_type]
        
        try:
            if classifier_type == "atomic_signal":
                classifier = self._create_atomic_signal_classifier()
            elif classifier_type == "semantic_context":
                classifier = self._create_semantic_context_classifier()
            elif classifier_type == "hybrid":
                classifier = self._create_hybrid_classifier()
            else:
                logger.error(f"不支持的分类器类型: {classifier_type}")
                return None
            
            self._classifiers[classifier_type] = classifier
            return classifier
            
        except Exception as e:
            logger.error(f"创建分类器失败: {e}")
            return None
    
    def _create_atomic_signal_classifier(self):
        """创建原子信号分类器"""
        # 这里会导入实际的分类器实现
        # from atomic_signal_classifier import AtomicSignalClassifier
        # return AtomicSignalClassifier(self.config_manager.get('atomic_signal'))
        pass
    
    def _create_semantic_context_classifier(self):
        """创建语义上下文分类器"""
        # from semantic_context_analyzer import SemanticContextAnalyzer
        # return SemanticContextAnalyzer(self.config_manager.get('semantic_context'))
        pass
    
    def _create_hybrid_classifier(self):
        """创建混合分类器"""
        # from hybrid_intelligent_classifier import HybridIntelligentClassifier
        # return HybridIntelligentClassifier(self.config_manager.get('hybrid'))
        pass

class IntelligentClassificationSystem:
    """智能分类系统 - 主入口"""
    
    def __init__(self, config_file: str = "classifier_config.json"):
        """初始化智能分类系统"""
        self.config_manager = ConfigManager(config_file)
        self.performance_monitor = PerformanceMonitor()
        self.classifier_factory = ClassifierFactory(self.config_manager)
        
        # 默认使用混合分类器
        self.default_classifier = self.classifier_factory.create_classifier("hybrid")
        
        logger.info("智能分类系统初始化完成")
    
    def classify(self, title: str, content: str = "", 
                classifier_type: str = "hybrid") -> ClassificationResult:
        """
        执行分类
        
        Args:
            title: 标题
            content: 内容
            classifier_type: 分类器类型 (atomic_signal, semantic_context, hybrid)
            
        Returns:
            分类结果
        """
        start_time = time.time()
        
        try:
            # 获取分类器
            classifier = self.classifier_factory.create_classifier(classifier_type)
            if not classifier:
                classifier = self.default_classifier
            
            if not classifier:
                return ClassificationResult(
                    category="ERROR",
                    confidence=0.0,
                    method="system_error",
                    matched_signals=[],
                    processing_time=time.time() - start_time,
                    explanation="无可用分类器",
                    metadata={"error": "No available classifier"}
                )
            
            # 执行分类
            result = classifier.classify(title, content)
            
            # 记录性能
            self.performance_monitor.record_classification(result)
            
            return result
            
        except Exception as e:
            self.performance_monitor.record_error(e)
            return ClassificationResult(
                category="ERROR",
                confidence=0.0,
                method="error",
                matched_signals=[],
                processing_time=time.time() - start_time,
                explanation=f"分类错误: {str(e)}",
                metadata={"error": str(e)}
            )
    
    def batch_classify(self, texts: List[Tuple[str, str]], 
                      classifier_type: str = "hybrid") -> List[ClassificationResult]:
        """
        批量分类
        
        Args:
            texts: [(title, content), ...] 文本列表
            classifier_type: 分类器类型
            
        Returns:
            分类结果列表
        """
        results = []
        for title, content in texts:
            result = self.classify(title, content, classifier_type)
            results.append(result)
        
        return results
    
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        return self.performance_monitor.get_performance_report()
    
    def get_system_info(self) -> Dict:
        """获取系统信息"""
        return {
            "version": self.config_manager.get("version", "1.0.0"),
            "available_classifiers": ["atomic_signal", "semantic_context", "hybrid"],
            "default_classifier": "hybrid",
            "config_file": str(self.config_manager.config_file),
            "performance_stats": self.get_performance_report()
        }

def create_system_architecture_demo():
    """创建系统架构演示"""
    print("🏗️ 重构后的系统架构演示")
    print("="*60)
    
    # 创建系统
    system = IntelligentClassificationSystem()
    
    # 显示系统信息
    info = system.get_system_info()
    print("📊 系统信息:")
    for key, value in info.items():
        if key != "performance_stats":
            print(f"   {key}: {value}")
    
    # 模拟分类（由于依赖模块可能不存在，这里只是演示接口）
    print(f"\n🧪 API接口演示:")
    print("# 单个分类")
    print('result = system.classify("公司股票进入退市整理期")')
    print('print(result.to_json())')
    
    print(f"\n# 批量分类")
    print('texts = [("年度报告显示盈利", ""), ("公司面临退市风险", "")]')
    print('results = system.batch_classify(texts)')
    
    print(f"\n# 性能监控")
    print('report = system.get_performance_report()')
    print('print(report)')
    
    print(f"\n💡 重构后的优势:")
    print("✅ 统一的API接口")
    print("✅ 标准化的结果格式")
    print("✅ 配置管理系统")
    print("✅ 性能监控机制")
    print("✅ 工厂模式的分类器管理")
    print("✅ 错误处理和日志记录")
    print("✅ 批量处理支持")

if __name__ == "__main__":
    create_system_architecture_demo()
