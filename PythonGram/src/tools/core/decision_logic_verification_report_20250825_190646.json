{"verification_timestamp": "2025-08-25T19:06:46.651934", "analysis_output_dir": "E:\\\\LLMData\\\\analysis_output", "total_files_verified": 100, "verification_results": [{"file_name": "9365423.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9365423.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9377867.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9377867.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9344023.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9344023.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9377505.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9377505.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9365009.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9365009.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9352276.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9352276.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9377363.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9377363.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9360210.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9360210.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9357915.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9357915.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9352656.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9352656.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9332484.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9332484.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9351749.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9351749.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9344756.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9344756.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9338030.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9338030.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9396671.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9396671.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9346078.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9346078.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9363754.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9363754.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9375479.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9375479.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9354075.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9354075.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9391153.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9391153.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9381976.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9381976.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9381243.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9381243.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9336079.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9336079.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9332807.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9332807.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9367144.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9367144.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9341397.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9341397.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9338412.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9338412.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9369439.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9369439.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9331602.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9331602.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9370395.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9370395.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9328295.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9328295.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9355736.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9355736.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9396665.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9396665.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9379815.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9379815.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9367734.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9367734.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9340562.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9340562.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9324947.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9324947.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9331771.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9331771.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9354737.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9354737.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9341162.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9341162.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9367776.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9367776.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9365180.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9365180.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9340754.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9340754.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9359491.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9359491.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9373977.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9373977.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9353076.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9353076.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9344372.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9344372.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9340136.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9340136.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9336887.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9336887.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9370162.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9370162.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9335976.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9335976.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9344497.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9344497.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9376771.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9376771.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9340091.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9340091.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9394166.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9394166.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9370252.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9370252.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9342080.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9342080.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9351263.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9351263.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9373318.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9373318.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9361042.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9361042.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9356099.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9356099.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9332402.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9332402.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9352954.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9352954.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9393413.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9393413.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9330753.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9330753.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9372207.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9372207.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9364553.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9364553.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9380049.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9380049.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9370466.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9370466.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9338469.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9338469.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9369299.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9369299.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9370148.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9370148.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9358633.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9358633.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9353678.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9353678.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9365780.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9365780.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9392643.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9392643.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9374347.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9374347.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9334100.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9334100.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9338386.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9338386.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9363988.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9363988.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9334265.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9334265.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9376063.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9376063.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9364643.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9364643.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9336697.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9336697.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9368991.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9368991.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9366058.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9366058.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9355969.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9355969.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9355063.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9355063.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9365655.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9365655.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9372991.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9372991.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9379297.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9379297.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9325904.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9325904.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9358445.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9358445.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9372980.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9372980.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9373364.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9373364.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9376612.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9376612.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9352358.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9352358.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9371807.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9371807.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9328792.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9328792.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9377199.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9377199.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}], "summary": {"total_files": 100, "successful_files": 100, "partial_files": 0, "failed_files": 0, "error_files": 0, "has_decision_logic": 100, "has_python_student_c": 100, "versions": {"v1": 100}}}