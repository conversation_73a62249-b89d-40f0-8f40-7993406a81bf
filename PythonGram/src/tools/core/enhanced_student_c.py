#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版学生C
集成现代化配置管理和规则引擎
"""

import json
import time
import logging
from pathlib import Path
from typing import Optional, Dict, Tuple

# 导入现代化模块
try:
    from modern_config import ModernConfigManager
    from modern_rule_engine import ModernRuleEngine, ClassificationResult, FilterResult
except ImportError as e:
    print(f"⚠️ 导入现代化模块失败: {e}")
    ModernConfigManager = None
    ModernRuleEngine = None

# 导入风险优先级模块
try:
    from versioned_knowledge_config import VersionedKnowledgeManager
    from risk_priority_classifier import RiskPriorityClassifier, RiskPriorityResult
    RISK_PRIORITY_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 导入风险优先级模块失败: {e}")
    VersionedKnowledgeManager = None
    RiskPriorityClassifier = None
    RiskPriorityResult = None
    RISK_PRIORITY_AVAILABLE = False

logger = logging.getLogger(__name__)

class EnhancedStudentC:
    """增强版学生C - 集成风险优先级分类器"""

    def __init__(self, config_path: str = "modern_config.json", use_risk_priority: bool = True):
        """
        初始化增强版学生C

        Args:
            config_path: 现代化配置文件路径
            use_risk_priority: 是否使用风险优先级分类器
        """
        self.config_path = config_path
        self.use_risk_priority = use_risk_priority and RISK_PRIORITY_AVAILABLE

        # 优先使用风险优先级分类器
        if self.use_risk_priority:
            self.knowledge_manager = VersionedKnowledgeManager()
            self.risk_classifier = RiskPriorityClassifier(self.knowledge_manager)
            logger.info("✅ 风险优先级分类器初始化成功")

            # 获取版本信息
            version_info = self.knowledge_manager.get_current_version_info()
            self.knowledge_version = version_info.get('knowledge_version', '1.0.0')
            self.code_version = version_info.get('code_version', '1.0.0')
        else:
            # 回退到现代化配置管理器
            if ModernConfigManager:
                self.config_manager = ModernConfigManager(config_path)
                self.config = self.config_manager.config
                logger.info("✅ 现代化配置管理器初始化成功")
            else:
                self.config_manager = None
                self.config = None
                logger.warning("⚠️ 现代化配置管理器不可用")

            # 初始化现代化规则引擎
            if ModernRuleEngine and self.config:
                rule_engine_config = self.config.rule_engine.model_dump() if self.config else {}
                self.rule_engine = ModernRuleEngine(rule_engine_config)
                self._update_rules_from_config()
                logger.info("✅ 现代化规则引擎初始化成功")
            else:
                self.rule_engine = None
                logger.warning("⚠️ 现代化规则引擎不可用")

            self.knowledge_version = "1.0.0"
            self.code_version = "1.0.0"

        # 统计信息
        self.stats = {
            'total_classifications': 0,
            'successful_classifications': 0,
            'failed_classifications': 0,
            'answers_written_back': 0,
            'risk_priority_used': 0,
            'fallback_used': 0
        }

        logger.info(f"🎓 增强版学生C初始化完成 (风险优先级: {'启用' if self.use_risk_priority else '禁用'})")
    
    def _update_rules_from_config(self):
        """从配置更新规则引擎"""
        if not self.rule_engine or not self.config:
            return
        
        try:
            filter_configs = self.config_manager.get_filter_configs()
            for filter_config in filter_configs:
                self.rule_engine.update_rule_from_config(filter_config)
            logger.info(f"✅ 从配置更新了 {len(filter_configs)} 个规则")
        except Exception as e:
            logger.error(f"❌ 从配置更新规则失败: {e}")
    
    def classify(self, title: str, content: str = ""):
        """使用增强版规则引擎进行分类"""
        self.stats['total_classifications'] += 1

        try:
            if self.use_risk_priority:
                # 使用风险优先级分类器
                result = self.risk_classifier.classify(title, content)
                self.stats['successful_classifications'] += 1
                self.stats['risk_priority_used'] += 1

                # 转换为兼容的结果格式
                return self._convert_risk_priority_result(result)
            elif self.rule_engine:
                # 使用现代化规则引擎
                result = self.rule_engine.classify(title, content)
                self.stats['successful_classifications'] += 1
                self.stats['fallback_used'] += 1
                return result
            else:
                # 最后的回退方案
                return self._fallback_classify(title, content)
        except Exception as e:
            logger.error(f"❌ 增强版分类失败: {e}")
            self.stats['failed_classifications'] += 1
            return self._fallback_classify(title, content)

    def _convert_risk_priority_result(self, risk_result: RiskPriorityResult):
        """将风险优先级结果转换为兼容格式"""
        # 映射结果类型
        result_mapping = {
            'NEGATIVE': FilterResult.NEGATIVE,
            'POSITIVE': FilterResult.POSITIVE,
            'NEUTRAL': FilterResult.NEUTRAL,
            'UNCERTAIN': FilterResult.UNCERTAIN
        }

        filter_result = result_mapping.get(risk_result.result.name, FilterResult.UNCERTAIN)

        # 创建兼容的ClassificationResult
        return ClassificationResult(
            result=filter_result,
            confidence=risk_result.confidence,
            matched_patterns=risk_result.matched_patterns,
            processing_time=risk_result.processing_time,
            filter_name=f"RiskPriorityClassifier_{risk_result.risk_level.level}",
            rule_trace=risk_result.classification_trace
        )
    
    def _fallback_classify(self, title: str, content: str) -> ClassificationResult:
        """回退分类方法"""
        start_time = time.time()
        full_text = f"{title} {content}"
        
        negative_keywords = ['亏损', '违规', '风险', '警示', '停牌']
        positive_keywords = ['增长', '合同', '盈利', '分红']
        neutral_keywords = ['股东大会', '董事会', '公告']
        
        if any(keyword in full_text for keyword in negative_keywords):
            result_type = FilterResult.NEGATIVE
            confidence = 0.85
        elif any(keyword in full_text for keyword in positive_keywords):
            result_type = FilterResult.POSITIVE
            confidence = 0.80
        elif any(keyword in full_text for keyword in neutral_keywords):
            result_type = FilterResult.NEUTRAL
            confidence = 0.90
        else:
            result_type = FilterResult.UNCERTAIN
            confidence = 0.0
        
        return ClassificationResult(
            result=result_type,
            confidence=confidence,
            matched_patterns=[],
            processing_time=time.time() - start_time,
            filter_name="FallbackClassifier",
            rule_trace=["使用回退分类器"]
        )
    
    def classify_json_file(self, json_file_path: str, write_back: bool = True) -> Optional[ClassificationResult]:
        """
        分类JSON文件中的公告

        Args:
            json_file_path: JSON文件路径
            write_back: 是否将结果写回JSON文件

        Returns:
            分类结果，失败时返回None
        """
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            # 提取标题和内容
            title = json_data.get('title', '')
            content = json_data.get('content', '')

            # 执行分类
            result = self.classify(title, content)

            # 写回结果到JSON文件
            if write_back:
                self._write_classification_back_to_json(json_file_path, json_data, result)

            return result

        except Exception as e:
            logger.error(f"❌ 处理JSON文件失败 {json_file_path}: {e}")
            return None

    def _write_classification_back_to_json(self, json_file_path: str, json_data: Dict,
                                         classification: ClassificationResult):
        """将学生C的分类结果写回JSON文件"""
        try:
            # 确保decisionLogic字段存在
            if 'decisionLogic' not in json_data:
                json_data['decisionLogic'] = {}

            if 'python_student_c' not in json_data['decisionLogic']:
                json_data['decisionLogic']['python_student_c'] = {}

            # 获取版本号（使用新的版本管理系统）
            version = f"v{self.code_version}"
            student_version = self.code_version
            rule_version = self.knowledge_version

            # 确定引擎类型
            engine_type = "risk_priority" if self.use_risk_priority else "enhanced"

            # 写入增强版的答券
            json_data['decisionLogic']['python_student_c'][version] = {
                "decision": classification.result.name,
                "confidence": classification.confidence,
                "matchedPatterns": classification.matched_patterns,
                "filterName": classification.filter_name,
                "processingTime": classification.processing_time,
                "timestamp": time.strftime('%Y-%m-%dT%H:%M:%S'),
                "studentVersion": student_version,
                "ruleVersion": rule_version,
                "knowledgeVersion": self.knowledge_version,  # 新增：知识库版本
                "codeVersion": self.code_version,  # 新增：代码版本
                "ruleTrace": classification.rule_trace,
                "engineType": engine_type,
                "riskPriorityEnabled": self.use_risk_priority  # 新增：风险优先级标识
            }

            # 写回文件
            with open(json_file_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)

            logger.debug(f"✅ 增强版答券已写回: {json_file_path}")
            self.stats['answers_written_back'] += 1

        except Exception as e:
            logger.error(f"❌ 写回答券失败 {json_file_path}: {e}")

    def get_system_status(self) -> Dict:
        """获取系统状态"""
        status = {
            "enhanced_student_c": True,
            "config_manager_available": self.config_manager is not None,
            "rule_engine_available": self.rule_engine is not None,
            "statistics": self.stats.copy()
        }

        if self.config_manager:
            status.update(self.config_manager.get_config_summary())

        if self.rule_engine:
            status["rule_engine_status"] = self.rule_engine.get_engine_status()

        return status

def test_enhanced_student_c():
    """测试增强版学生C"""
    print("🧪 测试增强版学生C")
    print("="*50)
    
    student_c = EnhancedStudentC()
    
    # 显示系统状态
    status = student_c.get_system_status()
    print(f"📊 系统状态:")
    for key, value in status.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for sub_key, sub_value in value.items():
                print(f"    {sub_key}: {sub_value}")
        else:
            print(f"  {key}: {value}")
    
    # 测试分类功能
    test_cases = [
        ("业绩预警公告", "公司预计2023年度净利润亏损5000万元"),
        ("重大合同签署", "公司与客户签署15亿元销售合同"),
        ("股东大会通知", "公司定于2024年3月15日召开年度股东大会"),
        ("一般性公告", "公司发布一般性业务公告")
    ]
    
    print(f"\n🔍 增强版分类测试:")
    for title, content in test_cases:
        result = student_c.classify(title, content)
        print(f"📄 {title}:")
        print(f"  结果: {result.result.label}")
        print(f"  置信度: {result.confidence:.3f}")
        print(f"  匹配模式: {result.matched_patterns}")
        print(f"  过滤器: {result.filter_name}")
        if result.rule_trace:
            print(f"  规则追踪: {result.rule_trace}")
        print(f"  处理时间: {result.processing_time:.4f}秒")
        print()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_enhanced_student_c()
