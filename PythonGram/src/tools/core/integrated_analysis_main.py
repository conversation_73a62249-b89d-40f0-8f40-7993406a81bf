#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合分析主程序
结合严格词频分析器和特征纯度分析器的完整分析流程
"""

import logging
import pandas as pd
from datetime import datetime
from pathlib import Path

# 导入我们的分析器
from enhanced_word_frequency_analyzer import StrictWordFrequencyAnalyzer
from feature_purity_analyzer import FeaturePurityAnalyzer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class IntegratedAnalysisSystem:
    """整合分析系统"""
    
    def __init__(self, data_dir: str = "E:/LLMData/analysis_output", output_dir: str = "E:/LLMData"):
        """
        初始化整合分析系统
        
        Args:
            data_dir: LLM分析结果目录
            output_dir: 输出目录
        """
        self.data_dir = data_dir
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建分析器
        self.word_freq_analyzer = StrictWordFrequencyAnalyzer(data_dir)
        
        logger.info("🚀 整合分析系统初始化完成")
    
    def run_word_frequency_analysis(self) -> pd.DataFrame:
        """运行词频分析"""
        logger.info("📊 开始词频分析...")
        
        try:
            df, excel_file, readable_file = self.word_freq_analyzer.run_complete_analysis()
            
            if not df.empty:
                # 移动文件到输出目录
                import shutil
                if Path(excel_file).exists():
                    shutil.move(excel_file, self.output_dir / excel_file)
                    logger.info(f"📁 Excel文件已移动到: {self.output_dir / excel_file}")
                
                if Path(readable_file).exists():
                    shutil.move(readable_file, self.output_dir / readable_file)
                    logger.info(f"📁 可读文件已移动到: {self.output_dir / readable_file}")
                
                # 保存CSV格式到输出目录
                csv_file = self.output_dir / f"word_frequency_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                df.to_csv(csv_file, index=False, encoding='utf-8-sig')
                logger.info(f"💾 CSV文件已保存: {csv_file}")
                
                return df
            else:
                logger.error("❌ 词频分析失败")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"❌ 词频分析过程中出现错误: {e}")
            return pd.DataFrame()
    
    def convert_to_purity_format(self, word_freq_df: pd.DataFrame) -> pd.DataFrame:
        """
        将词频分析结果转换为特征纯度分析器需要的格式
        
        Args:
            word_freq_df: 词频分析结果DataFrame
            
        Returns:
            转换后的DataFrame
        """
        if word_freq_df.empty:
            return pd.DataFrame()
        
        logger.info("🔄 转换数据格式用于纯度分析...")
        
        # 创建转换后的DataFrame
        converted_data = []
        
        for _, row in word_freq_df.iterrows():
            # 计算简单的卡方值（基于文档分布）
            total_docs = row['total_docs']
            expected_per_category = total_docs / 5  # 假设5个类别均匀分布
            
            # 计算各类别的卡方贡献
            chi2_a = ((row['A_docs'] - expected_per_category) ** 2) / (expected_per_category + 1e-9)
            chi2_b = ((row['B_docs'] - expected_per_category) ** 2) / (expected_per_category + 1e-9)
            chi2_c = ((row['C_docs'] - expected_per_category) ** 2) / (expected_per_category + 1e-9)
            chi2_d = ((row['D_docs'] - expected_per_category) ** 2) / (expected_per_category + 1e-9)
            chi2_e = ((row['E_docs'] - expected_per_category) ** 2) / (expected_per_category + 1e-9)
            
            # 计算P值（简化版本，基于卡方值）
            total_chi2 = chi2_a + chi2_b + chi2_c + chi2_d + chi2_e
            p_value_negative = 1.0 / (1.0 + chi2_d + chi2_e + 1e-9)  # D和E类为负面
            p_value_positive = 1.0 / (1.0 + chi2_a + chi2_b + 1e-9)  # A和B类为正面
            
            converted_item = {
                '词条': row['word'],
                'P值_负向': p_value_negative,
                'P值_正向': p_value_positive,
                '总文档数': total_docs,
                '卡方值_负向': chi2_d + chi2_e,  # D和E类合并为负面
                '卡方值_正向': chi2_a + chi2_b,  # A和B类合并为正面
                '卡方值_中性': chi2_c,          # C类为中性
                '主要类别': row['primary_category'],
                '纯度': row['purity']
            }
            
            converted_data.append(converted_item)
        
        converted_df = pd.DataFrame(converted_data)
        logger.info(f"✅ 数据格式转换完成，共 {len(converted_df)} 个词汇")
        
        return converted_df
    
    def run_purity_analysis(self, word_scores_df: pd.DataFrame) -> FeaturePurityAnalyzer:
        """运行特征纯度分析"""
        logger.info("🔬 开始特征纯度分析...")
        
        try:
            # 创建纯度分析器
            purity_analyzer = FeaturePurityAnalyzer(word_scores_df)
            
            # 执行纯度分析
            purity_analyzer.analyze_purity(p_value_threshold=0.1, min_doc_count=3)
            
            # 获取统计摘要
            stats = purity_analyzer.get_summary_stats()
            logger.info("📊 纯度分析统计摘要:")
            for key, value in stats.items():
                logger.info(f"   {key}: {value}")
            
            # 保存纯度分析报告
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = self.output_dir / f"feature_purity_report_{timestamp}.xlsx"
            purity_analyzer.save_purity_report(str(report_path))
            
            return purity_analyzer
            
        except Exception as e:
            logger.error(f"❌ 特征纯度分析过程中出现错误: {e}")
            return None
    
    def generate_summary_report(self, word_freq_df: pd.DataFrame, purity_analyzer: FeaturePurityAnalyzer):
        """生成综合分析报告"""
        logger.info("📋 生成综合分析报告...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = self.output_dir / f"integrated_analysis_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("整合分析系统综合报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 词频分析摘要
            f.write("📊 词频分析摘要:\n")
            f.write("-" * 30 + "\n")
            if not word_freq_df.empty:
                f.write(f"总词汇数: {len(word_freq_df)}\n")
                
                categories = ['A_EXCELLENT', 'B_GOOD', 'C_NEUTRAL', 'D_BAD', 'E_TERRIBLE']
                for category in categories:
                    count = len(word_freq_df[word_freq_df['primary_category'] == category])
                    f.write(f"{category}: {count} 个词汇\n")
                
                # 高纯度词汇统计
                high_purity = word_freq_df[word_freq_df['purity'] >= 0.9]
                f.write(f"高纯度词汇(≥0.9): {len(high_purity)} 个\n")
            else:
                f.write("词频分析未产生有效结果\n")
            
            # 纯度分析摘要
            f.write(f"\n🔬 特征纯度分析摘要:\n")
            f.write("-" * 30 + "\n")
            if purity_analyzer:
                stats = purity_analyzer.get_summary_stats()
                for key, value in stats.items():
                    f.write(f"{key}: {value}\n")
                
                # 显示高纯度负面关键词示例
                pure_negative = purity_analyzer.get_pure_keywords('负面', top_n=10)
                if not pure_negative.empty:
                    f.write(f"\n🔍 高纯度负面关键词示例 (前10个):\n")
                    for i, (_, row) in enumerate(pure_negative.iterrows(), 1):
                        f.write(f"{i:2d}. {row['词条']:<15} 纯度:{row['负面纯度']:.3f}\n")
            else:
                f.write("特征纯度分析未成功执行\n")
            
            f.write(f"\n✅ 分析完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        logger.info(f"📋 综合报告已保存: {report_file}")
    
    def run_complete_analysis(self):
        """运行完整的整合分析流程"""
        logger.info("🚀 开始完整的整合分析流程...")
        
        try:
            # 1. 词频分析
            word_freq_df = self.run_word_frequency_analysis()
            
            if word_freq_df.empty:
                logger.error("❌ 词频分析失败，无法继续")
                return
            
            # 2. 转换数据格式
            converted_df = self.convert_to_purity_format(word_freq_df)
            
            if converted_df.empty:
                logger.error("❌ 数据格式转换失败，无法进行纯度分析")
                purity_analyzer = None
            else:
                # 3. 特征纯度分析
                purity_analyzer = self.run_purity_analysis(converted_df)
            
            # 4. 生成综合报告
            self.generate_summary_report(word_freq_df, purity_analyzer)
            
            logger.info("🎉 完整的整合分析流程执行完成！")
            
            return word_freq_df, purity_analyzer
            
        except Exception as e:
            logger.error(f"❌ 整合分析流程中出现错误: {e}")
            return None, None

def main():
    """主函数"""
    print("🚀 整合分析系统")
    print("=" * 60)
    
    # 创建整合分析系统
    system = IntegratedAnalysisSystem(
        data_dir="E:/LLMData/analysis_output",
        output_dir="E:/LLMData"
    )
    
    # 运行完整分析
    word_freq_df, purity_analyzer = system.run_complete_analysis()
    
    if word_freq_df is not None and not word_freq_df.empty:
        print(f"\n✅ 分析成功完成！")
        print(f"📊 词频分析: {len(word_freq_df)} 个有效词汇")
        
        if purity_analyzer:
            stats = purity_analyzer.get_summary_stats()
            print(f"🔬 纯度分析: {stats.get('总特征数', 0)} 个特征")
        
        print(f"📁 所有结果文件已保存到: E:/LLMData/")
    else:
        print("❌ 分析失败，请检查数据源和配置")

if __name__ == "__main__":
    main()
