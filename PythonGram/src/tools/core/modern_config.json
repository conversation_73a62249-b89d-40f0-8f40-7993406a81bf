{"metadata": {"version": "1.0.0", "description": "Python学生C配置文件", "created_by": "ModernConfigManager", "created_at": "2025-08-26 10:50:12.855367", "last_updated": "2025-08-26 10:50:12.856365"}, "versioning": {"student_version": "1.0", "rule_version": "1.0", "code_version": "1.0", "version_format": "v{major}.{minor}", "auto_increment": false}, "path_mappings": [{"name": "supmicro4_F_mapping", "description": "supmicro4服务器F盘映射", "network_prefix": "\\\\supmicro4\\F\\", "local_prefix": "F:\\", "enabled": true}], "filters": [{"name": "HighConfidenceNegativeFilter", "priority": 1, "confidence_base": 0.95, "confidence_increment": 0.02, "confidence_max": 0.99, "enabled": true, "patterns": ["警示函", "业绩预亏", "亏损", "违规", "处罚", "风险提示", "停牌", "退市", "诉讼", "仲裁"], "negative_check_patterns": []}, {"name": "HighConfidencePositiveFilter", "priority": 2, "confidence_base": 0.9, "confidence_increment": 0.02, "confidence_max": 0.99, "enabled": true, "patterns": ["重大合同", "技术突破", "业绩增长", "盈利", "分红", "收购", "合作协议", "新产品发布"], "negative_check_patterns": ["亏损", "违规", "风险", "警示"]}, {"name": "HighConfidenceNeutralFilter", "priority": 3, "confidence_base": 0.99, "confidence_increment": 0.02, "confidence_max": 0.99, "enabled": true, "patterns": ["^.*股东大会.*$", "^.*工商变更.*$", "^.*董事会决议.*$", "^.*监事会决议.*$", "^.*年度报告.*$", "^.*季度报告.*$"], "negative_check_patterns": ["亏损", "违规", "风险", "警示"]}], "logging": {"level": "INFO", "format": "%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s", "file_enabled": false, "file_path": "python_student_c.log", "file_max_size": 10485760, "file_backup_count": 5}, "performance": {"enable_timing": true, "enable_caching": true, "max_content_length": 100000, "timeout_seconds": 30}, "pdf_extraction": {"preferred_library": "pdfminer", "fallback_libraries": ["PyPDF2", "pypdf"], "max_file_size_mb": 50, "encoding": "utf-8"}, "output_directories": {"base_output_dir": "E:\\LLMData\\", "test_results": "E:\\LLMData\\test_results\\", "analysis_output": "E:\\LLMData\\analysis_output\\", "logs": "E:\\LLMData\\logs\\", "processed_data": "E:\\LLMData\\processed_data\\", "reports": "E:\\LLMData\\reports\\"}, "rule_engine": {"engine_type": "business_rules", "enable_semantic_analysis": false, "enable_entity_recognition": false, "rule_cache_enabled": true, "rule_cache_size": 1000}}