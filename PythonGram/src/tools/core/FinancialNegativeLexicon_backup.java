package LLMAnalysis.Filter;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 金融负面情感词库 - Java版本
 * 基于原子信号分析结果的高精度负面情感识别
 * 源数据来自 financial_negative_lexicon.py
 */
public class FinancialNegativeLexicon {
    
    private Set<String> highPriorityWords;
    private Set<String> allNegativeWords;
    private Map<String, Double> intensityModifiers;
    private Map<String, Set<String>> categories;
    
    /**
     * 构造函数 - 从JSON文件加载词库
     * "D:\\LLMData\\PythonGram\\src\\tools\\core\\financial_negative_words.json"
     */
    public FinancialNegativeLexicon(String jsonFilePath) throws IOException {
        loadLexicon(jsonFilePath);
    }
    
    /**
     * 默认构造函数 - 使用内置词库
     */
    public FinancialNegativeLexicon() {
        initializeBuiltinLexicon();
    }
    
    /**
     * 从JSON文件加载词库
     */
    private void loadLexicon(String jsonFilePath) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(new File(jsonFilePath));
        
        // 加载高优先级词汇
        highPriorityWords = new HashSet<>();
        JsonNode highPriorityNode = root.get("high_priority_words");
        if (highPriorityNode.isArray()) {
            for (JsonNode word : highPriorityNode) {
                highPriorityWords.add(word.asText());
            }
        }
        
        // 加载所有负面词汇
        allNegativeWords = new HashSet<>();
        JsonNode allWordsNode = root.get("all_negative_words");
        if (allWordsNode.isArray()) {
            for (JsonNode word : allWordsNode) {
                allNegativeWords.add(word.asText());
            }
        }
        
        // 加载程度副词
        intensityModifiers = new HashMap<>();
        JsonNode modifiersNode = root.get("intensity_modifiers");
        if (modifiersNode.isObject()) {
            modifiersNode.fields().forEachRemaining(entry -> {
                intensityModifiers.put(entry.getKey(), entry.getValue().asDouble());
            });
        }
        
        System.out.println("✅ 词库加载完成: " + allNegativeWords.size() + " 个负面词汇");
    }
    
    /**
     * 初始化内置词库（如果没有JSON文件）
     */
    private void initializeBuiltinLexicon() {
        // 高优先级词汇（基于原子信号分析）
        highPriorityWords = new HashSet<>(Arrays.asList(
            "退市风险", "退市整理期", "面临退市风险", "同比由盈转亏", 
            "扣非亏损", "严重亏损", "违法违规", "监管函", "立案调查"
        ));
        
        // 所有负面词汇
        allNegativeWords = new HashSet<>(Arrays.asList(
            "退市风险", "退市整理期", "同比由盈转亏", "扣非亏损", "严重亏损",
            "业绩下滑", "收入下降", "利润下降", "经营困难", "财务困难",
            "违法违规", "监管函", "立案调查", "暴跌", "大跌", "崩盘"
        ));
        
        // 程度副词
        intensityModifiers = new HashMap<>();
        intensityModifiers.put("严重", 0.9);
        intensityModifiers.put("大幅", 0.8);
        intensityModifiers.put("显著", 0.7);
        intensityModifiers.put("持续", 0.8);
        intensityModifiers.put("急剧", 0.9);
        
        System.out.println("✅ 内置词库初始化完成");
    }
    
    /**
     * 计算文本的负面情感分数
     * 
     * @param text 输入文本
     * @return 负面分数 (0-1之间，越高越负面)
     */
    public double calculateNegativeScore(String text) {
        if (text == null || text.trim().isEmpty()) {
            return 0.0;
        }
        
        double negativeScore = 0.0;
        int wordCount = 0;
        
        // 检查高优先级词汇
        for (String word : highPriorityWords) {
            if (text.contains(word)) {
                negativeScore += 1.0; // 高优先级词汇权重为1.0
                wordCount++;
            }
        }
        
        // 检查其他负面词汇
        for (String word : allNegativeWords) {
            if (text.contains(word) && !highPriorityWords.contains(word)) {
                negativeScore += 0.6; // 普通负面词汇权重为0.6
                wordCount++;
            }
        }
        
        // 检查程度副词增强
        for (Map.Entry<String, Double> entry : intensityModifiers.entrySet()) {
            if (text.contains(entry.getKey())) {
                negativeScore *= (1 + entry.getValue() * 0.2); // 程度副词增强20%
            }
        }
        
        // 归一化分数
        if (wordCount > 0) {
            double normalizedScore = Math.min(1.0, negativeScore / (wordCount + 2));
            return normalizedScore;
        }
        
        return 0.0;
    }
    
    /**
     * 分类负面程度
     * 
     * @param text 输入文本
     * @return 分类结果
     */
    public ClassificationResult classifyNegativeLevel(String text) {
        double score = calculateNegativeScore(text);
        List<String> matchedWords = findMatchedWords(text);
        
        String level;
        if (score >= 0.8) {
            level = "E_TERRIBLE";
        } else if (score >= 0.6) {
            level = "D_BAD";
        } else if (score >= 0.4) {
            level = "C_NEGATIVE";
        } else if (score >= 0.2) {
            level = "B_SLIGHTLY_NEGATIVE";
        } else {
            level = "A_NEUTRAL";
        }
        
        return new ClassificationResult(level, score, matchedWords);
    }
    
    /**
     * 查找匹配的负面词汇
     */
    private List<String> findMatchedWords(String text) {
        return allNegativeWords.stream()
                .filter(text::contains)
                .collect(Collectors.toList());
    }
    
    /**
     * 检查是否包含高优先级负面词汇
     */
    public boolean containsHighPriorityNegative(String text) {
        return highPriorityWords.stream().anyMatch(text::contains);
    }
    
    /**
     * 获取所有匹配的高优先级词汇
     */
    public List<String> getMatchedHighPriorityWords(String text) {
        return highPriorityWords.stream()
                .filter(text::contains)
                .collect(Collectors.toList());
    }
    
    /**
     * 分类结果类
     */
    public static class ClassificationResult {
        private final String level;
        private final double score;
        private final List<String> matchedWords;
        
        public ClassificationResult(String level, double score, List<String> matchedWords) {
            this.level = level;
            this.score = score;
            this.matchedWords = matchedWords;
        }
        
        public String getLevel() { return level; }
        public double getScore() { return score; }
        public List<String> getMatchedWords() { return matchedWords; }
        
        @Override
        public String toString() {
            return String.format("ClassificationResult{level='%s', score=%.3f, matchedWords=%s}", 
                    level, score, matchedWords);
        }
    }
    
    /**
     * 演示和测试方法
     */
    public static void main(String[] args) {
        System.out.println("🔍 金融负面情感词库 - Java版本演示");
        System.out.println("=".repeat(60));
        
        try {
            // 尝试从JSON文件加载，如果失败则使用内置词库
            FinancialNegativeLexicon lexicon;
            try {
                lexicon = new FinancialNegativeLexicon("financial_negative_words.json");
            } catch (IOException e) {
                System.out.println("⚠️ JSON文件加载失败，使用内置词库");
                lexicon = new FinancialNegativeLexicon();
            }
            
            // 测试案例
            String[] testCases = {
                "公司股票进入退市整理期",
                "净利润同比由盈转亏",
                "业绩大幅下滑面临困难",
                "公司经营状况良好",
                "股东大会审议年度报告",
                "严重亏损导致财务困难",
                "收到监管函要求整改"
            };
            
            System.out.println("📊 负面情感分析测试:");
            System.out.printf("%-35s %-15s %-8s %s%n", "文本", "等级", "分数", "匹配词汇");
            System.out.println("-".repeat(80));
            
            for (String text : testCases) {
                ClassificationResult result = lexicon.classifyNegativeLevel(text);
                String wordsStr = result.getMatchedWords().size() > 3 ? 
                    String.join(", ", result.getMatchedWords().subList(0, 3)) + "..." :
                    String.join(", ", result.getMatchedWords());
                
                System.out.printf("%-35s %-15s %-8.3f %s%n", 
                    text.length() > 33 ? text.substring(0, 33) : text,
                    result.getLevel(), 
                    result.getScore(), 
                    wordsStr);
            }
            
            System.out.println("\n✅ Java版本金融负面情感词库测试完成！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试过程中出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
