#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中性文档过滤器构建器
基于用户的原始设想：词频集非中性 = excluded_negative + excluded_positive - definitely_neutral
目标：构建预过滤器，在LLM API调用前过滤掉100%确定的中性公告，减少API成本
"""

import pandas as pd
import jieba
import jieba.analyse
from collections import Counter
from pathlib import Path
import json
import re
from typing import Set, List, Dict
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NeutralFilterBuilder:
    """中性文档过滤器构建器"""
    
    def __init__(self, classification_output_dir: str = "D:/LLMData/classification_output"):
        self.classification_output_dir = Path(classification_output_dir)
        self.output_dir = Path("D:/LLMData/PythonGram/src/tools/data/processed")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 词频集
        self.word_freq_negative = Counter()  # 词频集1
        self.word_freq_positive = Counter()  # 词频集2  
        self.word_freq_neutral = Counter()   # 词频集3
        self.word_freq_non_neutral = Counter()  # 非中性词频集
        
        # 配置jieba
        self._setup_jieba()
        
        logger.info("NeutralFilterBuilder 初始化完成")
        logger.info(f"数据源目录: {self.classification_output_dir}")
    
    def _setup_jieba(self):
        """配置jieba分词"""
        # 添加金融专业词汇
        financial_words = [
            '业绩预警', '业绩预增', '业绩下滑', '净利润', '营业收入',
            '资产重组', '股权激励', '违规处罚', '诉讼仲裁', '风险提示',
            '退市风险', '债务违约', '高管变动', '独立董事', '股东大会'
        ]
        
        for word in financial_words:
            jieba.add_word(word, freq=1000, tag='n')
        
        logger.info("jieba金融词典配置完成")
    
    def load_csv_data(self) -> bool:
        """加载三个CSV文件的数据"""
        logger.info("开始加载CSV数据...")
        
        csv_files = {
            'excluded_negative.csv': 'negative',
            'excluded_positive.csv': 'positive', 
            'definitely_neutral.csv': 'neutral'
        }
        
        for csv_file, category in csv_files.items():
            csv_path = self.classification_output_dir / csv_file
            
            if not csv_path.exists():
                logger.error(f"CSV文件不存在: {csv_path}")
                return False
            
            try:
                df = pd.read_csv(csv_path, encoding='utf-8')
                logger.info(f"✅ 加载 {csv_file}: {len(df)} 条记录")
                
                # 分析词频
                word_freq = self._analyze_csv_word_frequency(df)
                
                if category == 'negative':
                    self.word_freq_negative = word_freq
                elif category == 'positive':
                    self.word_freq_positive = word_freq
                elif category == 'neutral':
                    self.word_freq_neutral = word_freq
                
                logger.info(f"  提取到 {len(word_freq)} 个词条")
                
            except Exception as e:
                logger.error(f"加载CSV文件失败 {csv_file}: {e}")
                return False
        
        logger.info("CSV数据加载完成")
        return True
    
    def _analyze_csv_word_frequency(self, df: pd.DataFrame) -> Counter:
        """分析单个CSV文件的词频"""
        word_counter = Counter()
        
        # 分析keyFactors和reasoning字段
        for _, row in df.iterrows():
            # 处理keyFactors
            key_factors = row.get('keyFactors', '')
            if pd.notna(key_factors):
                words = self._extract_keywords(str(key_factors))
                word_counter.update(words)
            
            # 处理reasoning
            reasoning = row.get('reasoning', '')
            if pd.notna(reasoning):
                words = self._extract_keywords(str(reasoning))
                word_counter.update(words)
        
        return word_counter
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        if not text or len(text.strip()) < 2:
            return []
        
        try:
            # 使用jieba提取关键词
            keywords = jieba.analyse.extract_tags(text, topK=20, withWeight=False)
            
            # 过滤有效词条
            valid_keywords = []
            for word in keywords:
                if self._is_valid_word(word):
                    valid_keywords.append(word)
            
            return valid_keywords
            
        except Exception as e:
            logger.debug(f"关键词提取失败: {e}")
            return []
    
    def _is_valid_word(self, word: str) -> bool:
        """判断词条是否有效"""
        if not word or len(word) < 2 or len(word) > 8:
            return False
        
        # 停用词
        stop_words = {
            '万元', '万股', '亿元', '关于', '公司', '公告', '通知', 
            '报告', '会议', '决议', '股东', '董事会', '监事会',
            '的', '了', '在', '是', '有', '和', '与', '及', '或'
        }
        
        if word in stop_words:
            return False
        
        # 纯数字或特殊字符
        if re.match(r'^[\d\W]+$', word):
            return False
        
        return True
    
    def calculate_non_neutral_wordset(self):
        """计算非中性词频集"""
        logger.info("开始计算非中性词频集...")
        logger.info("公式: 词频集非中性 = 词频集1(negative) + 词频集2(positive) - 词频集3(neutral)")
        
        # 执行集合运算
        # 步骤1: negative + positive
        combined_non_neutral = self.word_freq_negative + self.word_freq_positive
        logger.info(f"negative + positive = {len(combined_non_neutral)} 个词条")
        
        # 步骤2: 减去 neutral
        for word, freq in self.word_freq_neutral.items():
            if word in combined_non_neutral:
                # 减去中性词频，但保持非负
                combined_non_neutral[word] = max(0, combined_non_neutral[word] - freq)
                if combined_non_neutral[word] == 0:
                    del combined_non_neutral[word]
        
        self.word_freq_non_neutral = combined_non_neutral
        logger.info(f"减去neutral后 = {len(self.word_freq_non_neutral)} 个词条")
        
        # 显示统计信息
        print(f"\n📊 词频集统计:")
        print(f"  excluded_negative: {len(self.word_freq_negative)} 个词条")
        print(f"  excluded_positive: {len(self.word_freq_positive)} 个词条")
        print(f"  definitely_neutral: {len(self.word_freq_neutral)} 个词条")
        print(f"  非中性词频集: {len(self.word_freq_non_neutral)} 个词条")
    
    def get_top_non_neutral_words(self, top_n: int = 100) -> List[tuple]:
        """获取Top N非中性词汇"""
        logger.info(f"获取Top {top_n}非中性词汇...")
        
        # 按频次排序
        top_words = self.word_freq_non_neutral.most_common(top_n)
        
        logger.info(f"选出 {len(top_words)} 个高频非中性词汇")
        return top_words
    
    def generate_regex_rules(self, top_words: List[tuple]) -> Dict:
        """生成正则表达式规则"""
        logger.info("生成正则表达式规则...")
        
        # 按词频分组
        high_freq_words = [(word, freq) for word, freq in top_words if freq >= 10]
        medium_freq_words = [(word, freq) for word, freq in top_words if 5 <= freq < 10]
        low_freq_words = [(word, freq) for word, freq in top_words if 3 <= freq < 5]
        
        regex_rules = {
            'metadata': {
                'purpose': '中性文档预过滤器',
                'goal': '在LLM API调用前过滤100%确定的中性公告',
                'total_words': len(top_words),
                'high_confidence': len(high_freq_words),
                'medium_confidence': len(medium_freq_words),
                'low_confidence': len(low_freq_words)
            },
            'high_confidence_rules': [
                {
                    'word': word,
                    'pattern': f".*{re.escape(word)}.*",
                    'frequency': freq,
                    'confidence': 'HIGH',
                    'action': 'MARK_AS_NON_NEUTRAL'
                }
                for word, freq in high_freq_words
            ],
            'medium_confidence_rules': [
                {
                    'word': word,
                    'pattern': f".*{re.escape(word)}.*",
                    'frequency': freq,
                    'confidence': 'MEDIUM',
                    'action': 'MARK_AS_NON_NEUTRAL'
                }
                for word, freq in medium_freq_words
            ],
            'combined_pattern': {
                'high_confidence': '|'.join([re.escape(word) for word, _ in high_freq_words]),
                'medium_confidence': '|'.join([re.escape(word) for word, _ in medium_freq_words]),
                'all_words': '|'.join([re.escape(word) for word, _ in top_words])
            }
        }
        
        return regex_rules
    
    def save_results(self, top_words: List[tuple], regex_rules: Dict) -> str:
        """保存分析结果"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存完整结果
        results = {
            'metadata': {
                'analysis_time': timestamp,
                'purpose': '构建中性文档预过滤器',
                'method': '词频集非中性 = excluded_negative + excluded_positive - definitely_neutral',
                'target': '减少LLM API调用成本'
            },
            'word_frequency_stats': {
                'excluded_negative_count': len(self.word_freq_negative),
                'excluded_positive_count': len(self.word_freq_positive),
                'definitely_neutral_count': len(self.word_freq_neutral),
                'non_neutral_result_count': len(self.word_freq_non_neutral)
            },
            'top_non_neutral_words': [
                {'word': word, 'frequency': freq} 
                for word, freq in top_words
            ],
            'regex_rules': regex_rules
        }
        
        # 保存JSON结果
        json_path = self.output_dir / f"neutral_filter_rules_{timestamp}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 保存Excel结果
        excel_path = self.output_dir / f"neutral_filter_analysis_{timestamp}.xlsx"
        self._save_excel_results(excel_path, results, top_words)
        
        # 保存Java正则表达式文件
        java_regex_path = self.output_dir / f"java_regex_patterns_{timestamp}.txt"
        self._save_java_regex_patterns(java_regex_path, regex_rules)
        
        logger.info(f"结果已保存:")
        logger.info(f"  完整结果: {json_path}")
        logger.info(f"  Excel分析: {excel_path}")
        logger.info(f"  Java正则: {java_regex_path}")
        
        return str(json_path)
    
    def _save_excel_results(self, excel_path: Path, results: Dict, top_words: List[tuple]):
        """保存Excel分析结果"""
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # 1. 分析概览
            overview_data = {
                '指标': [
                    'excluded_negative词条数',
                    'excluded_positive词条数', 
                    'definitely_neutral词条数',
                    '非中性词频集词条数',
                    'Top100非中性词汇数'
                ],
                '数值': [
                    results['word_frequency_stats']['excluded_negative_count'],
                    results['word_frequency_stats']['excluded_positive_count'],
                    results['word_frequency_stats']['definitely_neutral_count'],
                    results['word_frequency_stats']['non_neutral_result_count'],
                    len(top_words)
                ]
            }
            pd.DataFrame(overview_data).to_excel(writer, sheet_name='分析概览', index=False)
            
            # 2. Top非中性词汇
            words_data = [
                {
                    '排名': i,
                    '词条': word,
                    '非中性频次': freq,
                    '正则表达式': f".*{re.escape(word)}.*",
                    '置信度': 'HIGH' if freq >= 10 else 'MEDIUM' if freq >= 5 else 'LOW'
                }
                for i, (word, freq) in enumerate(top_words, 1)
            ]
            pd.DataFrame(words_data).to_excel(writer, sheet_name='Top非中性词汇', index=False)
            
            # 3. 各词频集Top30
            for name, counter, sheet_name in [
                ('excluded_negative', self.word_freq_negative, 'negative_Top30'),
                ('excluded_positive', self.word_freq_positive, 'positive_Top30'),
                ('definitely_neutral', self.word_freq_neutral, 'neutral_Top30')
            ]:
                if counter:
                    data = [
                        {'词条': word, '频次': freq}
                        for word, freq in counter.most_common(30)
                    ]
                    pd.DataFrame(data).to_excel(writer, sheet_name=sheet_name, index=False)
    
    def _save_java_regex_patterns(self, java_path: Path, regex_rules: Dict):
        """保存Java正则表达式模式"""
        java_content = f"""// 中性文档预过滤器正则表达式
// 生成时间: {regex_rules['metadata'].get('generated_time', 'unknown')}
// 目的: 在LLM API调用前过滤100%确定的中性公告

// 高置信度模式 (频次>=10)
public static final String HIGH_CONFIDENCE_NON_NEUTRAL_PATTERN = 
    "({regex_rules['combined_pattern']['high_confidence']})";

// 中等置信度模式 (频次>=5)  
public static final String MEDIUM_CONFIDENCE_NON_NEUTRAL_PATTERN = 
    "({regex_rules['combined_pattern']['medium_confidence']})";

// 完整模式 (所有Top100词汇)
public static final String ALL_NON_NEUTRAL_PATTERN = 
    "({regex_rules['combined_pattern']['all_words']})";

// 使用示例:
// if (text.matches(".*" + HIGH_CONFIDENCE_NON_NEUTRAL_PATTERN + ".*")) {{
//     // 这个文档很可能是非中性的，需要LLM进一步分析
//     return "REQUIRES_LLM_ANALYSIS";
// }} else {{
//     // 这个文档可能是纯中性的，可以跳过LLM分析
//     return "LIKELY_NEUTRAL_SKIP_LLM";
// }}

// 高置信度词汇列表:
"""
        
        # 添加高置信度词汇列表
        high_conf_words = [rule['word'] for rule in regex_rules['high_confidence_rules']]
        for i, word in enumerate(high_conf_words):
            java_content += f"// {i+1:2d}. {word}\n"
        
        with open(java_path, 'w', encoding='utf-8') as f:
            f.write(java_content)
    
    def print_analysis_summary(self, top_words: List[tuple]):
        """打印分析摘要"""
        print("\n" + "="*80)
        print("🎯 中性文档预过滤器 - 分析摘要")
        print("="*80)
        
        print(f"\n📊 词频集统计:")
        print(f"  excluded_negative: {len(self.word_freq_negative)} 个词条")
        print(f"  excluded_positive: {len(self.word_freq_positive)} 个词条")
        print(f"  definitely_neutral: {len(self.word_freq_neutral)} 个词条")
        print(f"  非中性词频集: {len(self.word_freq_non_neutral)} 个词条")
        
        print(f"\n🏆 Top 20 非中性词汇:")
        for i, (word, freq) in enumerate(top_words[:20], 1):
            confidence = 'HIGH' if freq >= 10 else 'MEDIUM' if freq >= 5 else 'LOW'
            print(f"  {i:2d}. {word}: 频次={freq}, 置信度={confidence}")
        
        # 分析词汇类型
        word_types = {'业绩类': 0, '治理类': 0, '风险类': 0, '其他': 0}
        
        for word, freq in top_words:
            if any(keyword in word for keyword in ['业绩', '利润', '收入', '增长', '下降']):
                word_types['业绩类'] += 1
            elif any(keyword in word for keyword in ['董事', '股东', '会议', '选举', '议案']):
                word_types['治理类'] += 1
            elif any(keyword in word for keyword in ['风险', '违规', '处罚', '诉讼', '警示']):
                word_types['风险类'] += 1
            else:
                word_types['其他'] += 1
        
        print(f"\n📋 词汇类型分布:")
        for word_type, count in word_types.items():
            percentage = (count / len(top_words) * 100) if top_words else 0
            print(f"  {word_type}: {count} ({percentage:.1f}%)")
        
        print(f"\n💡 预期效果:")
        print(f"  - 包含这些词汇的文档将被标记为'需要LLM分析'")
        print(f"  - 不包含这些词汇的文档可能是纯中性公告，可跳过LLM")
        print(f"  - 预计可减少 30-50% 的LLM API调用量")
        
        print("\n" + "="*80)
    
    def run_complete_analysis(self) -> bool:
        """运行完整的中性过滤器构建分析"""
        logger.info("🚀 开始构建中性文档预过滤器...")
        
        try:
            # 1. 加载CSV数据
            if not self.load_csv_data():
                return False
            
            # 2. 计算非中性词频集
            self.calculate_non_neutral_wordset()
            
            # 3. 获取Top 100非中性词汇
            top_words = self.get_top_non_neutral_words(top_n=100)
            
            # 4. 生成正则表达式规则
            regex_rules = self.generate_regex_rules(top_words)
            
            # 5. 保存结果
            result_path = self.save_results(top_words, regex_rules)
            
            # 6. 打印摘要
            self.print_analysis_summary(top_words)
            
            logger.info(f"✅ 中性过滤器构建完成！")
            logger.info(f"📁 结果文件: {result_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 分析过程中发生错误: {e}")
            return False

def main():
    """主函数"""
    print("🎯 中性文档预过滤器构建器")
    print("目标: 构建预过滤器，减少LLM API调用成本")
    print("方法: 词频集非中性 = excluded_negative + excluded_positive - definitely_neutral")
    print("="*80)
    
    # 检查数据源
    classification_dir = Path("D:/LLMData/classification_output")
    required_files = ['excluded_negative.csv', 'excluded_positive.csv', 'definitely_neutral.csv']
    
    missing_files = []
    for file in required_files:
        if not (classification_dir / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必需的CSV文件:")
        for file in missing_files:
            print(f"  {classification_dir / file}")
        print(f"\n请确保这些文件存在后再运行分析")
        return
    
    # 创建分析器并运行
    builder = NeutralFilterBuilder()
    success = builder.run_complete_analysis()
    
    if success:
        print(f"\n🎉 预过滤器构建成功！")
        print(f"\n📝 下一步:")
        print(f"  1. 查看生成的Excel分析结果")
        print(f"  2. 将Java正则表达式集成到java_regex_filter中")
        print(f"  3. 更新legacy_db_rules的规则库")
        print(f"  4. 在生产环境中测试过滤效果")
    else:
        print(f"\n❌ 构建失败！请检查数据源和日志")

if __name__ == "__main__":
    main()
