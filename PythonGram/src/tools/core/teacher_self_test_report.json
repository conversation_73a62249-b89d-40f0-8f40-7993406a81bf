{"test_config": {"sample_size": 2000, "train_ratio": 0.8, "train_size": 1600, "test_size": 400}, "learned_patterns": {"NEUTRAL": {"sample_count": 1169, "top_keywords": ["其他", "股权变动", "万元", "高管变动", "股权激励", "亿元", "万股", "资产重组", "股权回购", "证券代码", "证券简称", "公告编号", "占总股本", "股权", "回购注销", "股股票", "使用不超过", "分红派息", "涉及", "股限制性股票"], "confidence": 1.0}, "POSITIVE": {"sample_count": 273, "top_keywords": ["万元", "亿元", "股权变动", "资产重组", "业绩预增", "股权激励", "同比增长", "股权回购", "万股", "预计净利润", "分红派息", "股权", "其他", "授予", "共计派发", "重大合同", "每股现金红利", "证券代码", "证券简称", "公告编号"], "confidence": 0.273}, "NEGATIVE": {"sample_count": 158, "top_keywords": ["万元", "业绩预警", "亿元", "违规处罚", "股权变动", "同比下降", "诉讼仲裁", "预计亏损", "其他", "资产重组", "证券代码", "证券简称", "公告编号", "同比减少", "预计净利润", "上年同期盈利", "回购注销", "占净资产", "公司预计", "净利润亏损"], "confidence": 0.158}}, "classifier": {"NEGATIVE": {"keywords": ["万元", "业绩预警", "亿元", "违规处罚", "股权变动", "同比下降", "诉讼仲裁", "预计亏损", "其他", "资产重组", "证券代码", "证券简称", "公告编号", "同比减少", "预计净利润", "上年同期盈利", "回购注销", "占净资产", "公司预计", "净利润亏损"], "confidence": 0.158}, "POSITIVE": {"keywords": ["万元", "亿元", "股权变动", "资产重组", "业绩预增", "股权激励", "同比增长", "股权回购", "万股", "预计净利润", "分红派息", "股权", "其他", "授予", "共计派发", "重大合同", "每股现金红利", "证券代码", "证券简称", "公告编号"], "confidence": 0.273}, "NEUTRAL": {"keywords": ["其他", "股权变动", "万元", "高管变动", "股权激励", "亿元", "万股", "资产重组", "股权回购", "证券代码", "证券简称", "公告编号", "占总股本", "股权", "回购注销", "股股票", "使用不超过", "分红派息", "涉及", "股限制性股票"], "confidence": 1.0}}, "evaluation": {"overall_accuracy": 0.7025, "category_accuracy": {"NEUTRAL": 1.0, "POSITIVE": 0.0, "NEGATIVE": 0.0}, "confusion_matrix": {"NEUTRAL": {"NEUTRAL": 281}, "POSITIVE": {"NEUTRAL": 70}, "NEGATIVE": {"NEUTRAL": 49}}, "total_samples": 400, "correct_predictions": 281, "predictions": [{"file_name": "9341201.json", "title": "9341201.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9340585.json", "title": "9340585.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9378893.json", "title": "9378893.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9362724.json", "title": "9362724.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9373494.json", "title": "9373494.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "1217368640.json", "title": "1217368640.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9334196.json", "title": "9334196.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9351085.json", "title": "9351085.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9366972.json", "title": "9366972.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9336056.json", "title": "9336056.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9349491.json", "title": "9349491.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9328997.json", "title": "9328997.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9355452.json", "title": "9355452.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9333021.json", "title": "9333021.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9396947.json", "title": "9396947.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9330325.json", "title": "9330325.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9350435.json", "title": "9350435.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9326468.json", "title": "9326468.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9359963.json", "title": "9359963.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9323751.json", "title": "9323751.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9362856.json", "title": "9362856.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9350540.json", "title": "9350540.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9359717.json", "title": "9359717.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9364447.json", "title": "9364447.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9396525.json", "title": "9396525.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9375650.json", "title": "9375650.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9328484.json", "title": "9328484.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9328635.json", "title": "9328635.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9363721.json", "title": "9363721.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9329332.json", "title": "9329332.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9348283.json", "title": "9348283.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9352930.json", "title": "9352930.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9350789.json", "title": "9350789.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9373840.json", "title": "9373840.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9346902.json", "title": "安信证券股份有限公司 \n\n关于无锡市金杨新材料股份有限公司 \n\n使用部分超募资金永久补充流动资金的核...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9337200.json", "title": "9337200.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9323515.json", "title": "9323515.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9375669.json", "title": "9375669.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9371447.json", "title": "9371447.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9355829.json", "title": "9355829.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9373136.json", "title": "9373136.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9359887.json", "title": "9359887.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9341829.json", "title": "9341829.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9334685.json", "title": "9334685.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9371220.json", "title": "9371220.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9395276.json", "title": "9395276.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9343602.json", "title": "9343602.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9358459.json", "title": "证券代码：603018                        证券简称：华设集团      ...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9338400.json", "title": "9338400.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9346981.json", "title": "9346981.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9393301.json", "title": "9393301.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9347752.json", "title": "9347752.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9351958.json", "title": "9351958.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9365744.json", "title": "9365744.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9373560.json", "title": "9373560.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9362959.json", "title": "9362959.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9380203.json", "title": "9380203.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9382200.json", "title": "9382200.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9393044.json", "title": "9393044.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9349629.json", "title": "9349629.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9332072.json", "title": "9332072.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9336233.json", "title": "9336233.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9353303.json", "title": "9353303.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9335647.json", "title": "9335647.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9348540.json", "title": "9348540.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9390601.json", "title": "9390601.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9367104.json", "title": "9367104.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9368246.json", "title": "9368246.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9394394.json", "title": "9394394.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9354783.json", "title": "9354783.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9396458.json", "title": "9396458.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9377878.json", "title": "9377878.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9330704.json", "title": "9330704.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9365686.json", "title": "9365686.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9380540.json", "title": "9380540.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9340175.json", "title": "9340175.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9375905.json", "title": "9375905.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9380120.json", "title": "9380120.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9375817.json", "title": "9375817.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9327546.json", "title": "9327546.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9364873.json", "title": "9364873.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9365268.json", "title": "9365268.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9347751.json", "title": "9347751.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9396411.json", "title": "9396411.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9368471.json", "title": "9368471.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9380337.json", "title": "9380337.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9380869.json", "title": "9380869.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9380186.json", "title": "9380186.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9362099.json", "title": "9362099.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9390505.json", "title": "9390505.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9369553.json", "title": "9369553.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9355583.json", "title": "9355583.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9375752.json", "title": "9375752.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9366677.json", "title": "9366677.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9354968.json", "title": "9354968.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9391088.json", "title": "9391088.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9343607.json", "title": "9343607.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9353580.json", "title": "9353580.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9368171.json", "title": "9368171.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9348752.json", "title": "9348752.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9358512.json", "title": "9358512.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9371313.json", "title": "9371313.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9346096.json", "title": "9346096.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9378017.json", "title": "9378017.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9363755.json", "title": "9363755.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9358535.json", "title": "9358535.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9360904.json", "title": "证券代码：300021             证券简称：大禹节水             公告编号...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9351138.json", "title": "9351138.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9366801.json", "title": "9366801.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9363023.json", "title": "9363023.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9367645.json", "title": "9367645.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9393971.json", "title": "9393971.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9396409.json", "title": "9396409.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9346517.json", "title": "证券代码：002751         证券简称：易尚退        公告编号：2023-063 ...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9349506.json", "title": "9349506.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9370072.json", "title": "9370072.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9396938.json", "title": "9396938.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9371568.json", "title": "9371568.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9336828.json", "title": "9336828.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9328588.json", "title": "证券代码：605167          证券简称：利柏特         公告编号：2023-03...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9371202.json", "title": "9371202.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9358454.json", "title": "9358454.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9355483.json", "title": "9355483.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9370132.json", "title": "9370132.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9329228.json", "title": "9329228.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9365882.json", "title": "9365882.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9348931.json", "title": "9348931.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9373838.json", "title": "9373838.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9394090.json", "title": "9394090.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9353345.json", "title": "9353345.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9394877.json", "title": "9394877.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9344554.json", "title": "9344554.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9339763.json", "title": "9339763.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9375994.json", "title": "9375994.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9355059.json", "title": "9355059.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9357371.json", "title": "9357371.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9373019.json", "title": "9373019.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9330558.json", "title": "9330558.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9342367.json", "title": "9342367.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9335190.json", "title": "9335190.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9354932.json", "title": "9354932.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9337214.json", "title": "9337214.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9370269.json", "title": "9370269.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9379305.json", "title": "9379305.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9380389.json", "title": "9380389.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9377994.json", "title": "9377994.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9358430.json", "title": "9358430.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9342364.json", "title": "9342364.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9363753.json", "title": "9363753.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9364751.json", "title": "9364751.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9377649.json", "title": "9377649.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9390348.json", "title": "9390348.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9393088.json", "title": "9393088.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9375678.json", "title": "9375678.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9350537.json", "title": "9350537.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9382311.json", "title": "9382311.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9355582.json", "title": "9355582.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9396185.json", "title": "9396185.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9366247.json", "title": "9366247.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9360584.json", "title": "9360584.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9376570.json", "title": "9376570.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9394903.json", "title": "9394903.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9341466.json", "title": "9341466.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9329373.json", "title": "9329373.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9365236.json", "title": "9365236.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9343342.json", "title": "9343342.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9378000.json", "title": "9378000.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9364883.json", "title": "9364883.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9360429.json", "title": "北京市金杜律师事务所 \n关于江苏益客食品集团股份有限公司 2023 年第三次临时股东大会 \n之法律意...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9356192.json", "title": "9356192.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9349093.json", "title": "9349093.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9337788.json", "title": "9337788.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9326130.json", "title": "江苏万林现代物流股份有限公司               独立董事关于出售资产暨关联交易事项的事前认...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9348594.json", "title": "9348594.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9339892.json", "title": "9339892.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9346543.json", "title": "9346543.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9352935.json", "title": "9352935.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9363911.json", "title": "9363911.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9377520.json", "title": "9377520.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9376813.json", "title": "9376813.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9396928.json", "title": "9396928.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9367843.json", "title": "9367843.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9328488.json", "title": "9328488.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9330669.json", "title": "9330669.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9334694.json", "title": "9334694.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9370882.json", "title": "9370882.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9362683.json", "title": "9362683.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9355197.json", "title": "9355197.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9365329.json", "title": "9365329.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9369039.json", "title": "9369039.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9368836.json", "title": "9368836.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9355566.json", "title": "9355566.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9357422.json", "title": "9357422.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9369258.json", "title": "9369258.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9341785.json", "title": "9341785.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9352122.json", "title": "9352122.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9374090.json", "title": "9374090.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9334582.json", "title": "证券代码：688516        证券简称：奥特维      公告编号：2023-055 \n\n无...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9374483.json", "title": "9374483.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9346050.json", "title": "9346050.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9371769.json", "title": "9371769.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9337281.json", "title": "9337281.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9329225.json", "title": "9329225.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9358869.json", "title": "9358869.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9367779.json", "title": "9367779.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9360742.json", "title": "9360742.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9339805.json", "title": "9339805.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9360841.json", "title": "9360841.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9374307.json", "title": "9374307.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9394518.json", "title": "9394518.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9343642.json", "title": "9343642.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9331599.json", "title": "9331599.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9335977.json", "title": "9335977.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9346684.json", "title": "上海市石门一路 288 号\n兴业太古汇香港兴业中心一座 26 层\n邮编：200041\n电话：（86-...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9363159.json", "title": "9363159.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9336135.json", "title": "9336135.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9349180.json", "title": "9349180.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9363017.json", "title": "9363017.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9366034.json", "title": "9366034.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9348196.json", "title": "9348196.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9353485.json", "title": "9353485.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9371675.json", "title": "9371675.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9351416.json", "title": "9351416.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9339364.json", "title": "9339364.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9376502.json", "title": "9376502.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9367632.json", "title": "9367632.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9340793.json", "title": "9340793.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9330805.json", "title": "9330805.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9375446.json", "title": "9375446.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9354526.json", "title": "9354526.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9365707.json", "title": "9365707.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9358350.json", "title": "9358350.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9339001.json", "title": "证券代码：688656        证券简称：浩欧博        公告编号：2023-027 \n...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9357554.json", "title": "9357554.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9336739.json", "title": "9336739.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9334303.json", "title": "9334303.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9328839.json", "title": "9328839.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9378014.json", "title": "9378014.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9331309.json", "title": "9331309.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9372002.json", "title": "9372002.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9368954.json", "title": "9368954.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9360747.json", "title": "9360747.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9352990.json", "title": "9352990.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9356489.json", "title": "9356489.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9361211.json", "title": "9361211.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9341720.json", "title": "9341720.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9379851.json", "title": "9379851.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9359841.json", "title": "9359841.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9366534.json", "title": "9366534.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9334676.json", "title": "9334676.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9396588.json", "title": "9396588.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9370295.json", "title": "9370295.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9369659.json", "title": "9369659.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9348409.json", "title": "9348409.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9336775.json", "title": "9336775.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9380548.json", "title": "9380548.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9339835.json", "title": "9339835.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9380739.json", "title": "9380739.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9358314.json", "title": "9358314.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9389948.json", "title": "9389948.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9368018.json", "title": "9368018.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9346467.json", "title": "9346467.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9339277.json", "title": "9339277.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9329827.json", "title": "9329827.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9330299.json", "title": "9330299.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9328068.json", "title": "9328068.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9340251.json", "title": "9340251.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9348788.json", "title": "9348788.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9355081.json", "title": "9355081.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9350559.json", "title": "9350559.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9374807.json", "title": "9374807.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9374453.json", "title": "9374453.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9366748.json", "title": "9366748.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9362554.json", "title": "9362554.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9335332.json", "title": "9335332.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9327908.json", "title": "9327908.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9325174.json", "title": "9325174.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9338090.json", "title": "9338090.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9334494.json", "title": "9334494.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9364237.json", "title": "9364237.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9377057.json", "title": "9377057.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9360978.json", "title": "9360978.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9338451.json", "title": "9338451.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9332601.json", "title": "9332601.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9353000.json", "title": "9353000.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9352099.json", "title": "9352099.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9324387.json", "title": "9324387.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9354752.json", "title": "9354752.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9346240.json", "title": "9346240.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9394276.json", "title": "9394276.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9325725.json", "title": "9325725.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9358701.json", "title": "9358701.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9333695.json", "title": "9333695.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9372270.json", "title": "9372270.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9344451.json", "title": "9344451.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9355742.json", "title": "9355742.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9355404.json", "title": "9355404.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9353288.json", "title": "9353288.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9377239.json", "title": "9377239.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9361611.json", "title": "9361611.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9342241.json", "title": "9342241.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9380433.json", "title": "9380433.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9378687.json", "title": "9378687.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9379405.json", "title": "9379405.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9357670.json", "title": "9357670.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9379484.json", "title": "9379484.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9355749.json", "title": "9355749.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9360658.json", "title": "9360658.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9356272.json", "title": "9356272.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9366044.json", "title": "9366044.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9348661.json", "title": "9348661.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9395794.json", "title": "9395794.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9390873.json", "title": "9390873.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9343738.json", "title": "9343738.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9331929.json", "title": "9331929.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9351991.json", "title": "9351991.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9340012.json", "title": "9340012.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9337251.json", "title": "9337251.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9358422.json", "title": "9358422.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9374565.json", "title": "江西赣锋锂业集团股份有限公司 \n\n独立董事对相关事项的事前认可意见 \n\n江西赣锋锂业集团股份有限公司...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9334961.json", "title": "9334961.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9341653.json", "title": "9341653.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9340832.json", "title": "9340832.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9339910.json", "title": "9339910.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9361505.json", "title": "9361505.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9343282.json", "title": "9343282.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9361705.json", "title": "9361705.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9335153.json", "title": "9335153.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9367653.json", "title": "9367653.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9396873.json", "title": "9396873.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9371446.json", "title": "9371446.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9372913.json", "title": "9372913.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9348933.json", "title": "9348933.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9324480.json", "title": "9324480.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9337340.json", "title": "9337340.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9372770.json", "title": "9372770.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9396170.json", "title": "9396170.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9326815.json", "title": "9326815.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9329736.json", "title": "9329736.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9330578.json", "title": "证券代码：000088    证券简称：盐田港     公告编号：2023-42 \n\n深圳市盐田港股...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9332216.json", "title": "9332216.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9372129.json", "title": "9372129.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9393970.json", "title": "9393970.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9396505.json", "title": "9396505.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9330291.json", "title": "9330291.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9346780.json", "title": "9346780.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9331001.json", "title": "9331001.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9328801.json", "title": "9328801.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9330359.json", "title": "9330359.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9364048.json", "title": "9364048.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9396968.json", "title": "9396968.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9332543.json", "title": "9332543.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9376094.json", "title": "9376094.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9370382.json", "title": "9370382.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9336853.json", "title": "9336853.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9356488.json", "title": "9356488.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9371600.json", "title": "9371600.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9370494.json", "title": "9370494.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9359496.json", "title": "9359496.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9348456.json", "title": "9348456.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9339584.json", "title": "9339584.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9333430.json", "title": "证券代码：603798         证券简称：康普顿         公告编号： 2023-02...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9357333.json", "title": "9357333.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9334172.json", "title": "9334172.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9379335.json", "title": "9379335.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9333116.json", "title": "9333116.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9365069.json", "title": "9365069.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9344289.json", "title": "9344289.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9341164.json", "title": "9341164.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9355416.json", "title": "9355416.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9366472.json", "title": "9366472.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9390899.json", "title": "9390899.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9359730.json", "title": "9359730.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9346839.json", "title": "9346839.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9351041.json", "title": "9351041.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9330875.json", "title": "证券代码：300272                          证券简称：开能健康    ...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9337927.json", "title": "9337927.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9353114.json", "title": "9353114.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "1217203784.json", "title": "1217203784.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9368145.json", "title": "9368145.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9325552.json", "title": "9325552.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9362623.json", "title": "证券代码：301029       证券简称：怡合达      公告编号：2023-080 \n\n东莞...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9340858.json", "title": "证券代码：300706          证券简称：阿石创        公告编号：2023-027...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9337006.json", "title": "9337006.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9325623.json", "title": "9325623.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9379047.json", "title": "9379047.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9333010.json", "title": "9333010.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9343174.json", "title": "9343174.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9382299.json", "title": "9382299.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9373265.json", "title": "9373265.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9346150.json", "title": "9346150.PDF...", "true_label": "NEGATIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9394649.json", "title": "9394649.PDF...", "true_label": "POSITIVE", "predicted_label": "NEUTRAL", "correct": false}, {"file_name": "9329786.json", "title": "9329786.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9337026.json", "title": "9337026.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9326027.json", "title": "9326027.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9339791.json", "title": "9339791.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9356436.json", "title": "9356436.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9360649.json", "title": "证券代码：600408                证券简称：安泰集团              ...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9330099.json", "title": "9330099.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}, {"file_name": "9333084.json", "title": "9333084.PDF...", "true_label": "NEUTRAL", "predicted_label": "NEUTRAL", "correct": true}]}}