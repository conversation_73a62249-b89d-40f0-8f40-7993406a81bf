{"verification_timestamp": "2025-08-25T18:42:28.757477", "analysis_output_dir": "E:\\\\LLMData\\\\analysis_output", "total_files_verified": 100, "verification_results": [{"enhanced_file": "1217203717_with_python_student_c.json", "original_file": "1217203717.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\1217203717_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "1217361913_with_python_student_c.json", "original_file": "1217361913.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\1217361913_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "600141_20230724_UXWE_with_python_student_c.json", "original_file": "600141_20230724_UXWE.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\600141_20230724_UXWE_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9322807_with_python_student_c.json", "original_file": "9322807.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9322807_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9323633_with_python_student_c.json", "original_file": "9323633.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9323633_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9324405_with_python_student_c.json", "original_file": "9324405.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9324405_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9324855_with_python_student_c.json", "original_file": "9324855.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9324855_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9326040_with_python_student_c.json", "original_file": "9326040.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9326040_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9327569_with_python_student_c.json", "original_file": "9327569.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9327569_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9327694_with_python_student_c.json", "original_file": "9327694.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9327694_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9327725_with_python_student_c.json", "original_file": "9327725.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9327725_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9328295_with_python_student_c.json", "original_file": "9328295.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9328295_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9328876_with_python_student_c.json", "original_file": "9328876.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9328876_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9329443_with_python_student_c.json", "original_file": "9329443.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9329443_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9329846_with_python_student_c.json", "original_file": "9329846.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9329846_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9330219_with_python_student_c.json", "original_file": "9330219.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9330219_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9330645_with_python_student_c.json", "original_file": "9330645.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9330645_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9331884_with_python_student_c.json", "original_file": "9331884.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9331884_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9332730_with_python_student_c.json", "original_file": "9332730.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9332730_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9333987_with_python_student_c.json", "original_file": "9333987.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9333987_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9334054_with_python_student_c.json", "original_file": "9334054.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9334054_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9334760_with_python_student_c.json", "original_file": "9334760.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9334760_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9336000_with_python_student_c.json", "original_file": "9336000.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9336000_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9336329_with_python_student_c.json", "original_file": "9336329.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9336329_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9337800_with_python_student_c.json", "original_file": "9337800.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9337800_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9339070_with_python_student_c.json", "original_file": "9339070.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9339070_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9339652_with_python_student_c.json", "original_file": "9339652.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9339652_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9339681_with_python_student_c.json", "original_file": "9339681.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9339681_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9339760_with_python_student_c.json", "original_file": "9339760.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9339760_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9339805_with_python_student_c.json", "original_file": "9339805.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9339805_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9340757_with_python_student_c.json", "original_file": "9340757.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9340757_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9341410_with_python_student_c.json", "original_file": "9341410.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9341410_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9341834_with_python_student_c.json", "original_file": "9341834.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9341834_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9341913_with_python_student_c.json", "original_file": "9341913.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9341913_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9343286_with_python_student_c.json", "original_file": "9343286.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9343286_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9343317_with_python_student_c.json", "original_file": "9343317.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9343317_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9343579_with_python_student_c.json", "original_file": "9343579.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9343579_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9344467_with_python_student_c.json", "original_file": "9344467.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9344467_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9345375_with_python_student_c.json", "original_file": "9345375.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9345375_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9345802_with_python_student_c.json", "original_file": "9345802.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9345802_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9346425_with_python_student_c.json", "original_file": "9346425.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9346425_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9347497_with_python_student_c.json", "original_file": "9347497.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9347497_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9348282_with_python_student_c.json", "original_file": "9348282.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9348282_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9349669_with_python_student_c.json", "original_file": "9349669.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9349669_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9350058_with_python_student_c.json", "original_file": "9350058.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9350058_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9350395_with_python_student_c.json", "original_file": "9350395.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9350395_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9350804_with_python_student_c.json", "original_file": "9350804.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9350804_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9350857_with_python_student_c.json", "original_file": "9350857.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9350857_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9351997_with_python_student_c.json", "original_file": "9351997.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9351997_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9354674_with_python_student_c.json", "original_file": "9354674.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9354674_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9355496_with_python_student_c.json", "original_file": "9355496.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9355496_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9355505_with_python_student_c.json", "original_file": "9355505.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9355505_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9355717_with_python_student_c.json", "original_file": "9355717.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9355717_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9356350_with_python_student_c.json", "original_file": "9356350.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9356350_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9358477_with_python_student_c.json", "original_file": "9358477.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9358477_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9363260_with_python_student_c.json", "original_file": "9363260.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9363260_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9364137_with_python_student_c.json", "original_file": "9364137.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9364137_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9364730_with_python_student_c.json", "original_file": "9364730.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9364730_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9365054_with_python_student_c.json", "original_file": "9365054.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9365054_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9365106_with_python_student_c.json", "original_file": "9365106.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9365106_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9365108_with_python_student_c.json", "original_file": "9365108.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9365108_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9365363_with_python_student_c.json", "original_file": "9365363.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9365363_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9365586_with_python_student_c.json", "original_file": "9365586.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9365586_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9365600_with_python_student_c.json", "original_file": "9365600.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9365600_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9365838_with_python_student_c.json", "original_file": "9365838.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9365838_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9366006_with_python_student_c.json", "original_file": "9366006.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9366006_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9366540_with_python_student_c.json", "original_file": "9366540.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9366540_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9366656_with_python_student_c.json", "original_file": "9366656.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9366656_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9366771_with_python_student_c.json", "original_file": "9366771.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9366771_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9366988_with_python_student_c.json", "original_file": "9366988.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9366988_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9369115_with_python_student_c.json", "original_file": "9369115.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9369115_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9369131_with_python_student_c.json", "original_file": "9369131.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9369131_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9369158_with_python_student_c.json", "original_file": "9369158.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9369158_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9369243_with_python_student_c.json", "original_file": "9369243.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9369243_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9369812_with_python_student_c.json", "original_file": "9369812.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9369812_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9370424_with_python_student_c.json", "original_file": "9370424.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9370424_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9370736_with_python_student_c.json", "original_file": "9370736.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9370736_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9371038_with_python_student_c.json", "original_file": "9371038.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9371038_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9371747_with_python_student_c.json", "original_file": "9371747.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9371747_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9371909_with_python_student_c.json", "original_file": "9371909.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9371909_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9373106_with_python_student_c.json", "original_file": "9373106.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9373106_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9373693_with_python_student_c.json", "original_file": "9373693.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9373693_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9375289_with_python_student_c.json", "original_file": "9375289.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9375289_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9375817_with_python_student_c.json", "original_file": "9375817.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9375817_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9376103_with_python_student_c.json", "original_file": "9376103.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9376103_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9377389_with_python_student_c.json", "original_file": "9377389.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9377389_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9379314_with_python_student_c.json", "original_file": "9379314.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9379314_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9380155_with_python_student_c.json", "original_file": "9380155.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9380155_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9380261_with_python_student_c.json", "original_file": "9380261.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9380261_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9381209_with_python_student_c.json", "original_file": "9381209.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9381209_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9381826_with_python_student_c.json", "original_file": "9381826.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9381826_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9382134_with_python_student_c.json", "original_file": "9382134.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9382134_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9382177_with_python_student_c.json", "original_file": "9382177.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9382177_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9382389_with_python_student_c.json", "original_file": "9382389.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9382389_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9390331_with_python_student_c.json", "original_file": "9390331.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9390331_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9390500_with_python_student_c.json", "original_file": "9390500.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9390500_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9391107_with_python_student_c.json", "original_file": "9391107.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9391107_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9395255_with_python_student_c.json", "original_file": "9395255.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9395255_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9396461_with_python_student_c.json", "original_file": "9396461.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9396461_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}, {"enhanced_file": "9396943_with_python_student_c.json", "original_file": "9396943.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9396943_with_python_student_c.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1.0", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "python_student_c"], "python_student_c_fields": ["decision", "confidence", "filter_name", "processing_time_ms", "stock_code", "stock_name", "company_name", "announcement_title", "header_confidence", "version", "student_name", "analysis_timestamp", "analysis_method"], "verification_status": "成功", "error_message": ""}], "summary": {"total_files": 100, "successful_files": 100, "partial_files": 0, "failed_files": 0, "error_files": 0, "has_decision_logic": 100, "has_python_student_c": 100, "versions": {"v1.0": 100}}}