#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索风险相关文档
主动寻找包含风险关键词的JSON文件，确保样本包含真正的风险信号
"""

import json
from pathlib import Path
from collections import defaultdict, Counter
import re

def find_risk_documents():
    """搜索风险相关文档"""
    print("🔍 搜索风险相关文档...")
    
    analysis_dir = Path('D:/LLMData/analysis_output')
    json_files = list(analysis_dir.glob('*.json'))
    
    # 定义风险关键词
    risk_keywords = {
        '业绩风险': ['亏损', '下降', '减少', '业绩预警', '业绩下滑', '净利润下降'],
        '违规风险': ['违规', '处罚', '罚款', '警告', '整改', '立案', '调查'],
        '诉讼风险': ['诉讼', '仲裁', '纠纷', '败诉', '赔偿', '法院'],
        '退市风险': ['退市', '暂停', '终止', 'ST', '*ST', '风险警示'],
        '债务风险': ['违约', '逾期', '债务', '担保', '资金链', '流动性'],
        '高管风险': ['辞职', '免职', '调查', '协助调查', '不能履职'],
        '市场风险': ['下跌', '波动', '不确定', '挑战', '困难', '压力']
    }
    
    # 搜索结果
    risk_documents = defaultdict(list)
    rating_distribution = defaultdict(int)
    
    print(f"📊 开始搜索 {len(json_files)} 个文件...")
    
    for i, json_file in enumerate(json_files):
        if i % 5000 == 0:
            print(f"进度: {i}/{len(json_files)}")
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 提取所有文本
            text_content = ""
            for field in ['title', 'summary', 'keyFactors', 'reasoning']:
                value = data.get(field, '')
                if isinstance(value, list):
                    text_content += ' '.join(str(item) for item in value if item)
                else:
                    text_content += str(value)
            
            # 检查是否包含风险关键词
            found_risks = []
            for risk_type, keywords in risk_keywords.items():
                for keyword in keywords:
                    if keyword in text_content:
                        found_risks.append((risk_type, keyword))
            
            if found_risks:
                rating_level = data.get('ratingLevel', '未知')
                rating_distribution[rating_level] += 1
                
                risk_documents[rating_level].append({
                    'file': json_file.name,
                    'title': data.get('title', '')[:100],
                    'ratingLevel': rating_level,
                    'risks': found_risks,
                    'keyFactors': data.get('keyFactors', [])[:3]
                })
        
        except Exception as e:
            continue
    
    # 输出结果
    print(f"\n📊 风险文档搜索结果:")
    total_risk_docs = sum(len(docs) for docs in risk_documents.values())
    print(f"总共找到 {total_risk_docs} 个包含风险关键词的文档")
    
    print(f"\n📋 按ratingLevel分布:")
    for rating, count in rating_distribution.items():
        percentage = (count / total_risk_docs * 100) if total_risk_docs > 0 else 0
        print(f"  {rating}: {count} ({percentage:.1f}%)")
    
    # 显示每个类别的示例
    for rating, docs in risk_documents.items():
        if docs:
            print(f"\n🔍 {rating} 类别风险文档示例 (前5个):")
            for i, doc in enumerate(docs[:5], 1):
                print(f"  {i}. {doc['file']}")
                print(f"     标题: {doc['title']}")
                print(f"     风险类型: {[risk[0] for risk in doc['risks']]}")
                print(f"     关键因素: {doc['keyFactors']}")
                print()
    
    return risk_documents

def analyze_risk_keywords():
    """分析风险关键词的实际分布"""
    print("\n🎯 分析风险关键词的实际分布...")
    
    analysis_dir = Path('D:/LLMData/analysis_output')
    json_files = list(analysis_dir.glob('*.json'))
    
    # 风险关键词统计
    risk_keyword_stats = Counter()
    rating_keyword_matrix = defaultdict(lambda: defaultdict(int))
    
    # 搜索前10000个文件
    sample_files = json_files[:10000]
    
    for json_file in sample_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            rating_level = data.get('ratingLevel', '未知')
            
            # 提取文本
            text_content = ""
            for field in ['title', 'summary', 'keyFactors', 'reasoning']:
                value = data.get(field, '')
                if isinstance(value, list):
                    text_content += ' '.join(str(item) for item in value if item)
                else:
                    text_content += str(value)
            
            # 统计关键词
            risk_words = [
                '亏损', '下降', '减少', '违规', '处罚', '风险', '警示',
                '诉讼', '退市', '暂停', '违约', '辞职', '调查', '困难'
            ]
            
            for word in risk_words:
                if word in text_content:
                    risk_keyword_stats[word] += 1
                    rating_keyword_matrix[rating_level][word] += 1
        
        except Exception as e:
            continue
    
    print(f"\n📊 风险关键词统计 (前10000个文件):")
    for word, count in risk_keyword_stats.most_common(10):
        print(f"  {word}: {count}")
    
    print(f"\n📊 风险词汇在不同评级中的分布:")
    for rating in ['差', '中', '好']:
        if rating in rating_keyword_matrix:
            print(f"\n  {rating} 类别:")
            rating_words = rating_keyword_matrix[rating]
            for word, count in sorted(rating_words.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"    {word}: {count}")

if __name__ == "__main__":
    risk_docs = find_risk_documents()
    analyze_risk_keywords()
