#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能Excel报告生成器

负责将分析结果（Pandas DataFrame）输出为格式精美的Excel文件。
"""
import pandas as pd
from pathlib import Path
from datetime import datetime
import logging
from typing import Dict

logger = logging.getLogger(__name__)

class SmartExcelGenerator:
    def __init__(self, output_dir: str = "D:/LLMData/PythonGram/src/tools/core/output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

    @staticmethod
    def _adjust_excel_column_width(worksheet):
        """静态辅助方法：自动调整Excel工作表的列宽"""
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value or '')) > max_length:
                        max_length = len(str(cell.value or ''))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)  # 限制最大宽度为50
            worksheet.column_dimensions[column_letter].width = adjusted_width

    def generate_analysis_report(self, scores_df: pd.DataFrame, processed_count: int, rating_dist: Dict[str, int]) -> str:
        """
        生成包含多个工作表的完整分析报告。
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = self.output_dir / f"word_frequency_analysis_{timestamp}.xlsx"

        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 写入主分析报告
                scores_df.to_excel(writer, sheet_name='关键词分析报告', index=False)
                worksheet1 = writer.sheets['关键词分析报告']
                self._adjust_excel_column_width(worksheet1)

                # 写入统计摘要
                summary_data = {
                    "统计项": ["总处理文档数"] + [f"{k}类文档数" for k in rating_dist.keys()],
                    "数值": [processed_count] + list(rating_dist.values())
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='统计摘要', index=False)
                worksheet2 = writer.sheets['统计摘要']
                self._adjust_excel_column_width(worksheet2)

            logger.info(f"✅ Excel报告已成功导出到: {output_path}")
            return str(output_path)
        except Exception as e:
            logger.error(f"❌ 导出Excel报告失败: {e}")
            raise