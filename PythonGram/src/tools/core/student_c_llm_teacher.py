#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学生C的LLM老师 - 专门指导学生C提高非中性公告识别率
"""

import json
import time
from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class LearningCase:
    """学习案例"""
    case_id: int
    title: str
    content_preview: str
    student_c_prediction: str
    llm_ground_truth: str
    is_correct: bool
    suggestions: List[str]
    timestamp: str

class LLMTeacherForStudentC:
    """专门指导学生C的LLM老师"""
    
    def __init__(self):
        self.learning_history: List[LearningCase] = []
        self.performance_stats = {
            'total_cases': 0,
            'correct_predictions': 0,
            'incorrect_predictions': 0,
            'neutral_missed': 0,  # 中性公告被误判为非中性
            'non_neutral_missed': 0,  # 非中性公告被误判为中性
            'improvement_suggestions': []
        }
    
    def analyze_case(self, title: str, content: str, student_c_prediction: str, 
                    llm_ground_truth: str) -> Dict:
        """分析单个案例并提供改进建议"""
        
        self.performance_stats['total_cases'] += 1
        
        # 判断学生C的预测是否正确
        is_correct = (student_c_prediction == llm_ground_truth)
        if is_correct:
            self.performance_stats['correct_predictions'] += 1
        else:
            self.performance_stats['incorrect_predictions'] += 1
            
            # 分析错误类型
            if llm_ground_truth == 'NEUTRAL' and student_c_prediction != 'NEUTRAL':
                self.performance_stats['neutral_missed'] += 1
            elif llm_ground_truth != 'NEUTRAL' and student_c_prediction == 'NEUTRAL':
                self.performance_stats['non_neutral_missed'] += 1
        
        # 生成改进建议
        suggestions = self._generate_suggestions(
            student_c_prediction, llm_ground_truth, content
        )
        
        # 创建学习案例
        learning_case = LearningCase(
            case_id=len(self.learning_history) + 1,
            title=title,
            content_preview=content[:100] + "..." if len(content) > 100 else content,
            student_c_prediction=student_c_prediction,
            llm_ground_truth=llm_ground_truth,
            is_correct=is_correct,
            suggestions=suggestions,
            timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
        )
        
        # 记录学习历史
        self.learning_history.append(learning_case)
        
        # 更新改进建议统计
        self.performance_stats['improvement_suggestions'].extend(suggestions)
        
        return {
            'case_id': learning_case.case_id,
            'is_correct': is_correct,
            'suggestions': suggestions,
            'error_type': self._classify_error_type(student_c_prediction, llm_ground_truth)
        }
    
    def _generate_suggestions(self, student_c_prediction: str, 
                             llm_ground_truth: str, 
                             content: str) -> List[str]:
        """生成具体的改进建议"""
        suggestions = []
        
        # 基于预测差异生成建议
        if student_c_prediction == 'UNCERTAIN' and llm_ground_truth in ['POSITIVE', 'NEGATIVE']:
            suggestions.append("学生C过于保守，建议降低不确定性阈值")
            suggestions.append("检查是否遗漏了关键的正则表达式规则")
            suggestions.append("考虑增加更多明确的正面/负面关键词匹配")
        
        elif student_c_prediction in ['POSITIVE', 'NEGATIVE'] and llm_ground_truth == 'NEUTRAL':
            suggestions.append("学生C过于激进，建议提高确定性阈值")
            suggestions.append("检查是否存在误判的正则表达式规则")
            suggestions.append("考虑增加中性公告的识别规则")
        
        elif student_c_prediction == 'NEUTRAL' and llm_ground_truth in ['POSITIVE', 'NEGATIVE']:
            suggestions.append("学生C遗漏了非中性特征，建议增强非中性识别能力")
            suggestions.append("检查是否过于依赖中性规则")
            suggestions.append("建议优先检查非中性关键词")
        
        # 基于内容特征生成具体建议
        content_lower = content.lower()
        
        # 负面关键词建议
        negative_keywords = ['风险', '下降', '亏损', '处罚', '纠纷', '违约', '警示函', '立案调查']
        if any(keyword in content_lower for keyword in negative_keywords) and student_c_prediction != 'NEGATIVE':
            suggestions.append("包含负面关键词，建议优先考虑负面判断")
            suggestions.append("检查负面规则是否过于严格")
        
        # 正面关键词建议
        positive_keywords = ['增长', '盈利', '中标', '合同', '专利', '创新', '预增']
        if any(keyword in content_lower for keyword in positive_keywords) and student_c_prediction != 'POSITIVE':
            suggestions.append("包含正面关键词，建议优先考虑正面判断")
            suggestions.append("检查正面规则是否过于严格")
        
        # 中性关键词建议
        neutral_keywords = ['通知', '召开', '聘任', '年度报告', '股东大会', '董事会']
        if any(keyword in content_lower for keyword in neutral_keywords) and student_c_prediction != 'NEUTRAL':
            suggestions.append("包含中性关键词，建议优先考虑中性判断")
            suggestions.append("检查中性规则是否过于宽松")
        
        return suggestions
    
    def _classify_error_type(self, student_c_prediction: str, llm_ground_truth: str) -> str:
        """分类错误类型"""
        if student_c_prediction == llm_ground_truth:
            return "正确"
        elif llm_ground_truth == 'NEUTRAL' and student_c_prediction != 'NEUTRAL':
            return "中性误判为非中性"
        elif llm_ground_truth != 'NEUTRAL' and student_c_prediction == 'NEUTRAL':
            return "非中性误判为中性"
        elif student_c_prediction == 'UNCERTAIN':
            return "过于保守"
        else:
            return "类别混淆"
    
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        total = self.performance_stats['total_cases']
        if total == 0:
            return {"error": "暂无数据"}
        
        accuracy = self.performance_stats['correct_predictions'] / total
        neutral_accuracy = 1 - (self.performance_stats['neutral_missed'] / total) if total > 0 else 0
        non_neutral_accuracy = 1 - (self.performance_stats['non_neutral_missed'] / total) if total > 0 else 0
        
        return {
            'total_cases': total,
            'overall_accuracy': f"{accuracy:.2%}",
            'correct_predictions': self.performance_stats['correct_predictions'],
            'incorrect_predictions': self.performance_stats['incorrect_predictions'],
            'neutral_accuracy': f"{neutral_accuracy:.2%}",
            'non_neutral_accuracy': f"{non_neutral_accuracy:.2%}",
            'neutral_missed': self.performance_stats['neutral_missed'],
            'non_neutral_missed': self.performance_stats['non_neutral_missed'],
            'improvement_suggestions_count': len(self.performance_stats['improvement_suggestions']),
            'recent_suggestions': self.performance_stats['improvement_suggestions'][-10:]  # 最近10条建议
        }
    
    def get_learning_summary(self) -> Dict:
        """获取学习总结"""
        if not self.learning_history:
            return {"error": "暂无学习历史"}
        
        # 按错误类型分组
        error_types = {}
        for case in self.learning_history:
            if not case.is_correct:
                error_type = self._classify_error_type(case.student_c_prediction, case.llm_ground_truth)
                if error_type not in error_types:
                    error_types[error_type] = []
                error_types[error_type].append(case)
        
        # 生成改进重点
        improvement_focus = []
        for error_type, cases in error_types.items():
            improvement_focus.append({
                'error_type': error_type,
                'count': len(cases),
                'percentage': f"{len(cases) / len(self.learning_history):.1%}",
                'example_case': cases[0].title if cases else "无"
            })
        
        return {
            'total_learning_cases': len(self.learning_history),
            'error_distribution': improvement_focus,
            'top_improvement_areas': sorted(improvement_focus, key=lambda x: x['count'], reverse=True)[:3]
        }
    
    def save_learning_data(self, output_path: str = "student_c_learning_data.json"):
        """保存学习数据"""
        try:
            # 转换dataclass为dict
            learning_data = {
                'performance_stats': self.performance_stats,
                'learning_history': [
                    {
                        'case_id': case.case_id,
                        'title': case.title,
                        'content_preview': case.content_preview,
                        'student_c_prediction': case.student_c_prediction,
                        'llm_ground_truth': case.llm_ground_truth,
                        'is_correct': case.is_correct,
                        'suggestions': case.suggestions,
                        'timestamp': case.timestamp
                    }
                    for case in self.learning_history
                ],
                'metadata': {
                    'generated_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'description': '学生C的LLM老师学习数据'
                }
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(learning_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 学习数据已保存到: {output_path}")
            return True
        except Exception as e:
            print(f"❌ 保存学习数据失败: {e}")
            return False

def main():
    """测试LLM老师"""
    teacher = LLMTeacherForStudentC()
    
    # 模拟一些学习案例
    test_cases = [
        {
            'title': '关于收到证监会警示函的公告',
            'content': '公司收到证监会警示函，提醒注意信息披露规范',
            'student_c_pred': 'UNCERTAIN',
            'llm_truth': 'NEGATIVE'
        },
        {
            'title': '2023年业绩预增公告',
            'content': '预计2023年净利润同比增长50%以上',
            'student_c_pred': 'NEUTRAL',
            'llm_truth': 'POSITIVE'
        },
        {
            'title': '关于召开股东大会的通知',
            'content': '定于2023年12月召开年度股东大会',
            'student_c_pred': 'NEUTRAL',
            'llm_truth': 'NEUTRAL'
        }
    ]
    
    print("🎓 测试学生C的LLM老师...")
    print("=" * 50)
    
    for case in test_cases:
        analysis = teacher.analyze_case(
            case['title'],
            case['content'],
            case['student_c_pred'],
            case['llm_truth']
        )
        
        print(f"\n📄 {case['title']}")
        print(f"  学生C预测: {case['student_c_pred']}")
        print(f"  LLM真实值: {case['llm_truth']}")
        print(f"  结果: {'✅ 正确' if analysis['is_correct'] else '❌ 错误'}")
        print(f"  错误类型: {analysis['error_type']}")
        
        if analysis['suggestions']:
            print(f"  改进建议:")
            for suggestion in analysis['suggestions']:
                print(f"    • {suggestion}")
    
    # 显示性能报告
    print(f"\n📊 性能报告:")
    perf_report = teacher.get_performance_report()
    for key, value in perf_report.items():
        if key not in ['recent_suggestions']:
            print(f"  {key}: {value}")
    
    # 显示学习总结
    print(f"\n📚 学习总结:")
    learning_summary = teacher.get_learning_summary()
    for key, value in learning_summary.items():
        if key == 'top_improvement_areas':
            print(f"  {key}:")
            for area in value:
                print(f"    • {area['error_type']}: {area['count']}次 ({area['percentage']})")
        else:
            print(f"  {key}: {value}")
    
    # 保存学习数据
    teacher.save_learning_data()

if __name__ == "__main__":
    main()










