#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强词频分析器
整合严格过滤词频分析器和特征纯度分析器
"""

import json
import logging
import pandas as pd
import re
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)

class StrictWordFrequencyAnalyzer:
    """严格过滤的词频分析器"""
    
    def __init__(self, data_dir: str = "E:/LLMData/analysis_output"):
        """
        初始化分析器
        
        Args:
            data_dir: LLM分析结果目录
        """
        self.data_dir = Path(data_dir)
        
        # 评级映射
        self.rating_mapping = {
            '优': 'A_EXCELLENT',
            '好': 'B_GOOD', 
            '中': 'C_NEUTRAL',
            '差': 'D_BAD',
            '劣': 'E_TERRIBLE'
        }
        
        logger.info("📊 严格过滤词频分析器初始化完成")
    
    def load_and_classify_data(self) -> Dict[str, List[Dict]]:
        """加载并按ABCDE分类数据"""
        json_files = list(self.data_dir.glob("*.json"))
        logger.info(f"📁 找到 {len(json_files)} 个JSON文件")
        
        classified_data = defaultdict(list)
        failed_count = 0
        
        for file_path in json_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 检查必要字段
                rating = data.get('ratingLevel', '')
                if rating in self.rating_mapping:
                    # 提取文本内容，TODO 如果content == “” 应该读取PDF文件，注意路径映射 ，并回写到 'content' 字段
                    content = data.get('content', '')
                    title = content[:100] if content else data.get('fileName', '')
                    
                    item = {
                        'file_name': file_path.name,
                        'title': title,
                        'content': content,
                        'rating': rating,
                        'summary': data.get('summary', ''),
                        'keyFactors': data.get('keyFactors', []),
                        'riskFactors': data.get('riskFactors', []),
                        'eventTags': data.get('eventTags', []),
                        'reasoning': data.get('reasoning', '')
                    }
                    
                    mapped_rating = self.rating_mapping[rating]
                    classified_data[mapped_rating].append(item)
                
            except Exception as e:
                failed_count += 1
                logger.debug(f"加载文件失败 {file_path}: {e}")
        
        # 显示分类统计
        logger.info("📊 数据分类统计:")
        total_loaded = 0
        for rating, items in classified_data.items():
            count = len(items)
            total_loaded += count
            logger.info(f"   {rating}: {count} 个文档")
        
        logger.info(f"✅ 成功加载 {total_loaded} 条数据，失败 {failed_count} 条")
        return dict(classified_data)
    
    def extract_strict_words(self, text: str) -> List[str]:
        """
        严格提取词汇：2-10个汉字字符，无数字无标点
        """
        if not text:
            return []
        
        # 提取2-10个连续汉字的词汇
        chinese_words = re.findall(r'[\u4e00-\u9fff]{2,10}', text)
        
        # 严格过滤
        valid_words = []
        for word in chinese_words:
            if self._is_strictly_valid(word):
                valid_words.append(word)
        
        return list(set(valid_words))  # 去重
    
    def _is_strictly_valid(self, word: str) -> bool:
        """严格验证词汇有效性"""
        # 长度检查
        if len(word) < 2 or len(word) > 10:
            return False
        
        # 必须全部是汉字
        if not re.match(r'^[\u4e00-\u9fff]+$', word):
            return False
        
        # 排除停用词
        stop_words = {
            # 基础停用词
            '公司', '股份', '有限', '公告', '通知', '决议', '报告', '说明',
            '关于', '根据', '按照', '依据', '为了', '由于', '因为', '所以',
            '但是', '然而', '同时', '此外', '另外', '其中', '包括', '具体',
            '主要', '重要', '相关', '有关', '进行', '实施', '执行', '完成',
            
            # 代词和连词
            '我们', '他们', '这个', '那个', '什么', '怎么', '如何', '可以',
            '应该', '需要', '必须', '已经', '正在', '将要', '可能', '或者',
            '以及', '而且', '并且', '不过', '只是', '仅仅', '如果', '虽然',
            
            # 时间和数量词
            '今年', '去年', '明年', '本年', '上年', '下年', '当年', '全年',
            '今日', '昨日', '明日', '当日', '本月', '上月', '下月', '当月',
            '一些', '很多', '大量', '少量', '全部', '部分', '整体', '个别',
            
            # 通用商业词汇
            '发展', '建设', '管理', '运营', '经营', '投资', '融资', '收购',
            '合作', '协议', '合同', '项目', '业务', '产品', '服务', '市场',
            '客户', '供应商', '股东', '董事', '监事', '员工', '人员', '团队',
            
            # 形容词和副词
            '良好', '优秀', '一般', '较好', '很好', '非常', '特别', '尤其',
            '比较', '相对', '绝对', '基本', '主要', '次要', '重要', '关键'
        }
        
        if word in stop_words:
            return False
        
        # 排除单字重复
        if len(word) == 2 and word[0] == word[1]:
            return False
        
        # 排除明显的非商业词汇
        non_business = {
            '吃饭', '睡觉', '走路', '跑步', '游戏', '电影', '音乐', '旅游',
            '天气', '季节', '春天', '夏天', '秋天', '冬天', '颜色', '红色',
            '动物', '植物', '食物', '衣服', '房子', '汽车', '手机', '电脑'
        }
        
        if word in non_business:
            return False
        
        return True
    
    def analyze_word_frequency(self, classified_data: Dict[str, List[Dict]]) -> pd.DataFrame:
        """分析词频并计算统计指标"""
        word_stats = defaultdict(lambda: {
            'A_count': 0, 'B_count': 0, 'C_count': 0, 'D_count': 0, 'E_count': 0,
            'A_docs': set(), 'B_docs': set(), 'C_docs': set(), 'D_docs': set(), 'E_docs': set()
        })
        
        # 统计每个词汇在各类别中的出现情况
        for rating, items in classified_data.items():
            rating_key = rating.split('_')[0]  # A, B, C, D, E
            
            for i, item in enumerate(items):
                doc_id = f"{rating}_{i}"
                
                # 提取完整文本
                text_parts = [
                    item.get('title', ''),
                    item.get('content', ''),
                    item.get('summary', ''),
                    item.get('reasoning', '')
                ]
                
                # 处理列表字段
                for field in ['keyFactors', 'riskFactors', 'eventTags']:
                    field_data = item.get(field, [])
                    if isinstance(field_data, list):
                        text_parts.extend([str(x) for x in field_data])
                    else:
                        text_parts.append(str(field_data))
                
                full_text = ' '.join([t for t in text_parts if t])
                
                # 提取词汇
                words = self.extract_strict_words(full_text)
                
                # 统计词汇
                for word in words:
                    word_stats[word][f'{rating_key}_count'] += 1
                    word_stats[word][f'{rating_key}_docs'].add(doc_id)
        
        # 转换为DataFrame
        analysis_data = []
        for word, stats in word_stats.items():
            # 计算文档数
            a_docs = len(stats['A_docs'])
            b_docs = len(stats['B_docs'])
            c_docs = len(stats['C_docs'])
            d_docs = len(stats['D_docs'])
            e_docs = len(stats['E_docs'])
            total_docs = a_docs + b_docs + c_docs + d_docs + e_docs
            
            # 过滤：至少在3个文档中出现
            if total_docs < 3:
                continue
            
            # 计算比例
            a_ratio = a_docs / total_docs if total_docs > 0 else 0
            b_ratio = b_docs / total_docs if total_docs > 0 else 0
            c_ratio = c_docs / total_docs if total_docs > 0 else 0
            d_ratio = d_docs / total_docs if total_docs > 0 else 0
            e_ratio = e_docs / total_docs if total_docs > 0 else 0
            
            # 计算纯度（最高比例）
            max_ratio = max(a_ratio, b_ratio, c_ratio, d_ratio, e_ratio)
            
            # 确定主要类别
            if max_ratio == a_ratio:
                primary_category = 'A_EXCELLENT'
            elif max_ratio == b_ratio:
                primary_category = 'B_GOOD'
            elif max_ratio == c_ratio:
                primary_category = 'C_NEUTRAL'
            elif max_ratio == d_ratio:
                primary_category = 'D_BAD'
            else:
                primary_category = 'E_TERRIBLE'
            
            analysis_data.append({
                'word': word,
                'primary_category': primary_category,
                'purity': max_ratio,
                'total_docs': total_docs,
                'A_docs': a_docs,
                'B_docs': b_docs,
                'C_docs': c_docs,
                'D_docs': d_docs,
                'E_docs': e_docs,
                'A_ratio': a_ratio,
                'B_ratio': b_ratio,
                'C_ratio': c_ratio,
                'D_ratio': d_ratio,
                'E_ratio': e_ratio,
                'A_count': stats['A_count'],
                'B_count': stats['B_count'],
                'C_count': stats['C_count'],
                'D_count': stats['D_count'],
                'E_count': stats['E_count']
            })
        
        df = pd.DataFrame(analysis_data)
        
        # 按纯度和总文档数排序
        if not df.empty:
            df = df.sort_values(['purity', 'total_docs'], ascending=[False, False])
        
        logger.info(f"📝 分析了 {len(df)} 个有效词汇")
        return df
    
    def save_to_excel(self, df: pd.DataFrame, output_file: str):
        """保存到Excel文件，按类别分工作表"""
        if df.empty:
            logger.warning("⚠️ 没有数据可保存")
            return
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 总览工作表
            df.to_excel(writer, sheet_name='总览', index=False)
            
            # 按主要类别分别保存
            categories = ['A_EXCELLENT', 'B_GOOD', 'C_NEUTRAL', 'D_BAD', 'E_TERRIBLE']
            
            for category in categories:
                category_df = df[df['primary_category'] == category].head(500)  # 每类最多500个
                if not category_df.empty:
                    sheet_name = category.split('_')[1]  # EXCELLENT, GOOD, etc.
                    category_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        logger.info(f"💾 Excel文件已保存: {output_file}")
    
    def save_readable_format(self, df: pd.DataFrame, output_file: str):
        """保存为可读格式"""
        if df.empty:
            logger.warning("⚠️ 没有数据可保存")
            return
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("严格过滤词频分析结果\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总词汇数: {len(df)}\n\n")
            
            # 按类别统计
            categories = ['A_EXCELLENT', 'B_GOOD', 'C_NEUTRAL', 'D_BAD', 'E_TERRIBLE']
            
            for category in categories:
                category_df = df[df['primary_category'] == category]
                f.write(f"\n{category} ({len(category_df)} 个词汇)\n")
                f.write("-" * 40 + "\n")
                
                # 显示前20个高纯度词汇
                top_words = category_df.head(20)
                for i, (_, row) in enumerate(top_words.iterrows(), 1):
                    f.write(f"{i:2d}. {row['word']:<12} "
                           f"纯度:{row['purity']:.3f} "
                           f"文档:{row['total_docs']:3d} "
                           f"A:{row['A_docs']:2d} B:{row['B_docs']:2d} C:{row['C_docs']:2d} "
                           f"D:{row['D_docs']:2d} E:{row['E_docs']:2d}\n")
        
        logger.info(f"📄 可读格式文件已保存: {output_file}")
    
    def run_complete_analysis(self) -> Tuple[pd.DataFrame, str, str]:
        """运行完整分析"""
        logger.info("🚀 开始严格过滤词频分析...")
        
        # 1. 加载并分类数据
        classified_data = self.load_and_classify_data()
        
        if not classified_data:
            logger.error("❌ 没有可用数据")
            return pd.DataFrame(), "", ""
        
        # 2. 分析词频
        df = self.analyze_word_frequency(classified_data)
        
        if df.empty:
            logger.error("❌ 词频分析失败")
            return pd.DataFrame(), "", ""
        
        # 3. 生成输出文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        excel_file = f"word_frequency_analysis_strict_{timestamp}.xlsx"
        readable_file = f"word_frequency_analysis_readable_{timestamp}.txt"
        
        # 4. 保存结果
        self.save_to_excel(df, excel_file)
        self.save_readable_format(df, readable_file)
        
        logger.info("✅ 严格过滤词频分析完成")
        return df, excel_file, readable_file
