#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于真实公告数据的词库优化器
使用E:\LLMData\PythonGram\src\tools\output\word_frequency_analysis_20250826_191856.xlsx
中的最小粒度单词来优化负面情感词库
"""

import pandas as pd
import json
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict, Counter

class RealDataLexiconOptimizer:
    """基于真实数据的词库优化器"""
    
    def __init__(self, excel_file_path: str = None):
        """
        初始化优化器
        
        Args:
            excel_file_path: Excel文件路径，如果为None则尝试多个可能的路径
        """
        self.excel_file_path = excel_file_path
        self.word_data = None
        self.optimized_lexicon = {
            'E_TERRIBLE': set(),
            'D_BAD': set(), 
            'A_EXCELLENT': set(),
            'B_GOOD': set(),
            'C_NEUTRAL': set()
        }
        
        # 尝试加载数据
        self.load_real_data()
    
    def load_real_data(self):
        """加载真实的词频分析数据"""
        possible_paths = [
            "E:/LLMData/PythonGram/src/tools/output/word_frequency_analysis_20250826_191856.xlsx",
            "output/word_frequency_analysis_20250825_094407.xlsx",
            "word_frequency_analysis_strict_20250826_194933.xlsx",
            "word_frequency_analysis_strict_20250826_144924.xlsx",
            # 添加generate_excel_output.py生成的文件
            "word_frequency_analysis_results_20250826_191856.xlsx",
            "word_frequency_analysis_results.xlsx"
        ]

        for path in possible_paths:
            try:
                if Path(path).exists():
                    print(f"📁 尝试加载: {path}")
                    # 尝试读取不同的工作表，使用openpyxl引擎
                    excel_file = pd.ExcelFile(path, engine='openpyxl')
                    print(f"📋 工作表: {excel_file.sheet_names}")

                    # 优先读取包含最多词汇的工作表
                    best_sheet = None
                    max_rows = 0

                    for sheet_name in excel_file.sheet_names:
                        try:
                            df = pd.read_excel(path, sheet_name=sheet_name, engine='openpyxl')
                            # 检查是否包含词汇相关的列
                            if len(df) > max_rows and any(col in df.columns for col in ['词条', 'word', '词汇']):
                                max_rows = len(df)
                                best_sheet = sheet_name
                                self.word_data = df
                        except Exception as e:
                            print(f"  ⚠️ 读取工作表 {sheet_name} 失败: {e}")
                            continue

                    if best_sheet:
                        self.excel_file_path = path
                        print(f"✅ 成功加载数据: {len(self.word_data)} 行 (工作表: {best_sheet})")
                        print(f"📊 数据列: {list(self.word_data.columns)}")
                        return

            except Exception as e:
                print(f"⚠️ 加载失败 {path}: {e}")
                continue

        print("❌ 无法找到有效的数据文件")

    def load_from_generate_excel_output(self, excel_path: str) -> bool:
        """
        专门加载generate_excel_output.py生成的Excel文件
        这种文件包含多个工作表：负面词条Top100, 正面词条Top100, 中性词条Top100等
        """
        try:
            excel_file = pd.ExcelFile(excel_path)
            print(f"📋 发现工作表: {excel_file.sheet_names}")

            # 合并所有相关工作表的数据
            all_words = []

            # 读取负面词条
            if '负面词条Top100' in excel_file.sheet_names:
                df_negative = pd.read_excel(excel_path, sheet_name='负面词条Top100')
                for _, row in df_negative.iterrows():
                    all_words.append({
                        'word': row['词条'],
                        'primary_category': 'E_TERRIBLE',
                        'frequency': row['频次'],
                        'purity': 0.9,  # 假设负面词条纯度较高
                        'total_docs': row['频次']  # 使用频次作为文档数的近似
                    })

            # 读取正面词条
            if '正面词条Top100' in excel_file.sheet_names:
                df_positive = pd.read_excel(excel_path, sheet_name='正面词条Top100')
                for _, row in df_positive.iterrows():
                    all_words.append({
                        'word': row['词条'],
                        'primary_category': 'A_EXCELLENT',
                        'frequency': row['频次'],
                        'purity': 0.9,
                        'total_docs': row['频次']
                    })

            # 读取中性词条
            if '中性词条Top100' in excel_file.sheet_names:
                df_neutral = pd.read_excel(excel_path, sheet_name='中性词条Top100')
                for _, row in df_neutral.iterrows():
                    all_words.append({
                        'word': row['词条'],
                        'primary_category': 'C_NEUTRAL',
                        'frequency': row['频次'],
                        'purity': 0.8,
                        'total_docs': row['频次']
                    })

            # 转换为DataFrame
            self.word_data = pd.DataFrame(all_words)
            print(f"✅ 成功合并数据: {len(self.word_data)} 个词条")
            return True

        except Exception as e:
            print(f"❌ 加载generate_excel_output格式失败: {e}")
            return False

    def analyze_real_data_patterns(self) -> Dict:
        """分析真实数据中的词汇模式"""
        if self.word_data is None:
            print("❌ 没有可用的数据")
            return {}
        
        print("🔍 分析真实数据中的词汇模式...")
        
        analysis_result = {
            'total_words': len(self.word_data),
            'category_distribution': {},
            'high_purity_words': {},
            'atomic_candidates': {},
            'length_distribution': {}
        }
        
        # 检查数据结构
        if 'primary_category' in self.word_data.columns:
            category_col = 'primary_category'
        elif 'category' in self.word_data.columns:
            category_col = 'category'
        else:
            print("⚠️ 未找到类别列，使用第一列作为类别")
            category_col = self.word_data.columns[0]
        
        if 'purity' in self.word_data.columns:
            purity_col = 'purity'
        elif 'confidence' in self.word_data.columns:
            purity_col = 'confidence'
        else:
            print("⚠️ 未找到纯度列，使用第二列作为纯度")
            purity_col = self.word_data.columns[1]
        
        # 分析类别分布
        category_counts = self.word_data[category_col].value_counts()
        analysis_result['category_distribution'] = category_counts.to_dict()
        
        # 分析高纯度词汇
        for category in ['E_TERRIBLE', 'D_BAD', 'A_EXCELLENT', 'B_GOOD', 'C_NEUTRAL']:
            if category in category_counts.index:
                category_data = self.word_data[self.word_data[category_col] == category]
                
                # 高纯度词汇 (纯度 >= 0.8)
                if purity_col in category_data.columns:
                    high_purity = category_data[category_data[purity_col] >= 0.8]
                    analysis_result['high_purity_words'][category] = len(high_purity)
                    
                    # 提取原子候选词汇 (长度2-4，高纯度)
                    if 'word' in category_data.columns:
                        atomic_candidates = high_purity[
                            (high_purity['word'].str.len() >= 2) & 
                            (high_purity['word'].str.len() <= 4)
                        ]['word'].tolist()
                        analysis_result['atomic_candidates'][category] = atomic_candidates[:20]  # 前20个
        
        # 分析词汇长度分布
        if 'word' in self.word_data.columns:
            length_dist = self.word_data['word'].str.len().value_counts().sort_index()
            analysis_result['length_distribution'] = length_dist.to_dict()
        
        return analysis_result
    
    def extract_atomic_signals_from_real_data(self, min_purity: float = 0.8, min_docs: int = 3) -> Dict:
        """从真实数据中提取原子信号"""
        if self.word_data is None:
            return {}
        
        print(f"⚡ 从真实数据中提取原子信号 (纯度≥{min_purity}, 文档≥{min_docs})")
        
        # 确定列名 - 适配真实数据格式
        word_col = '词条' if '词条' in self.word_data.columns else 'word'

        # 基于统计指标确定类别
        # 使用卡方值来判断词汇的倾向性
        negative_chi2_col = '卡方值_负向' if '卡方值_负向' in self.word_data.columns else 'chi2_negative'
        positive_chi2_col = '卡方值_正向' if '卡方值_正向' in self.word_data.columns else 'chi2_positive'
        neutral_chi2_col = '卡方值_中性' if '卡方值_中性' in self.word_data.columns else 'chi2_neutral'
        docs_col = '总文档数' if '总文档数' in self.word_data.columns else 'total_docs'
        
        atomic_signals = {
            'E_TERRIBLE': {'单字符': [], '双字符': [], '三字符': [], '四字符': [], '长词汇': []},
            'D_BAD': {'单字符': [], '双字符': [], '三字符': [], '四字符': [], '长词汇': []},
            'A_EXCELLENT': {'单字符': [], '双字符': [], '三字符': [], '四字符': [], '长词汇': []},
            'B_GOOD': {'单字符': [], '双字符': [], '三字符': [], '四字符': [], '长词汇': []},
            'C_NEUTRAL': {'单字符': [], '双字符': [], '三字符': [], '四字符': [], '长词汇': []}
        }

        # 遍历每个词汇，基于统计指标分类
        for _, row in self.word_data.iterrows():
            word = str(row[word_col])

            # 只处理中文词汇
            if not re.match(r'^[\u4e00-\u9fff]+$', word):
                continue

            # 获取统计指标
            total_docs = row.get(docs_col, 0)
            if total_docs < min_docs:
                continue

            negative_chi2 = row.get(negative_chi2_col, 0)
            positive_chi2 = row.get(positive_chi2_col, 0)
            neutral_chi2 = row.get(neutral_chi2_col, 0)

            # 计算纯度
            total_chi2 = negative_chi2 + positive_chi2 + neutral_chi2 + 1e-9
            negative_purity = negative_chi2 / total_chi2
            positive_purity = positive_chi2 / total_chi2
            neutral_purity = neutral_chi2 / total_chi2

            # 确定类别 - 基于最高卡方值
            max_chi2 = max(negative_chi2, positive_chi2, neutral_chi2)

            # 只保留高纯度的词汇
            max_purity = max(negative_purity, positive_purity, neutral_purity)
            if max_purity < min_purity:
                continue

            # 分类逻辑
            if max_chi2 == negative_chi2:
                # 负面词汇，进一步细分
                if negative_chi2 >= 10:  # 高负面信号
                    category = 'E_TERRIBLE'
                else:  # 中等负面信号
                    category = 'D_BAD'
            elif max_chi2 == positive_chi2:
                # 正面词汇，进一步细分
                if positive_chi2 >= 10:  # 高正面信号
                    category = 'A_EXCELLENT'
                else:  # 中等正面信号
                    category = 'B_GOOD'
            else:
                # 中性词汇
                category = 'C_NEUTRAL'

            # 按长度分组
            word_len = len(word)
            if word_len == 1:
                length_key = '单字符'
            elif word_len == 2:
                length_key = '双字符'
            elif word_len == 3:
                length_key = '三字符'
            elif word_len == 4:
                length_key = '四字符'
            else:
                length_key = '长词汇'

            atomic_signals[category][length_key].append({
                'word': word,
                'purity': max_purity,
                'chi2': max_chi2,
                'docs': total_docs
            })

        # 排序并限制数量
        for category in atomic_signals:
            for length_key in atomic_signals[category]:
                # 按卡方值排序
                atomic_signals[category][length_key].sort(key=lambda x: x['chi2'], reverse=True)
                # 只保留词汇，限制数量
                atomic_signals[category][length_key] = [
                    item['word'] for item in atomic_signals[category][length_key][:50]
                ]
        
        return atomic_signals
    
    def create_optimized_lexicon(self, focus_on_recall: bool = True) -> Dict:
        """
        创建优化的词库
        
        Args:
            focus_on_recall: 是否专注于召回率 (宁可错杀不可放过)
        """
        print("🎯 创建基于真实数据的优化词库...")
        
        if self.word_data is None:
            print("❌ 没有真实数据，使用默认词库")
            return self._create_default_lexicon()
        
        # 从真实数据提取原子信号
        atomic_signals = self.extract_atomic_signals_from_real_data(
            min_purity=0.7 if focus_on_recall else 0.8,  # 召回优先时降低纯度要求
            min_docs=2 if focus_on_recall else 3         # 召回优先时降低文档数要求
        )
        
        optimized_lexicon = {
            "version": "2.3.0",  # 🎯 真实数据优化版本
            "description": "基于真实公告数据的优化负面情感词库",
            "data_source": self.excel_file_path,
            "optimization_strategy": "高召回率" if focus_on_recall else "高精度",
            "categories": {}
        }
        
        # 为每个类别构建词库
        for category, signals in atomic_signals.items():
            category_lexicon = {
                "priority_level": self._get_priority_level(category),
                "atomic_signals": {},
                "total_signals": 0
            }
            
            # 按长度组织原子信号
            for length_key, words in signals.items():
                if words:  # 只包含非空的分组
                    category_lexicon["atomic_signals"][length_key] = words
                    category_lexicon["total_signals"] += len(words)
            
            optimized_lexicon["categories"][category] = category_lexicon
        
        # 添加统计信息
        optimized_lexicon["statistics"] = {
            "total_categories": len(atomic_signals),
            "total_signals": sum(len(words) for signals in atomic_signals.values() 
                                for words in signals.values()),
            "category_distribution": {
                category: sum(len(words) for words in signals.values())
                for category, signals in atomic_signals.items()
            }
        }
        
        return optimized_lexicon
    
    def _get_priority_level(self, category: str) -> int:
        """获取类别优先级"""
        priority_map = {
            'E_TERRIBLE': 5,
            'D_BAD': 4,
            'A_EXCELLENT': 3,
            'B_GOOD': 2,
            'C_NEUTRAL': 1
        }
        return priority_map.get(category, 1)
    
    def _create_default_lexicon(self) -> Dict:
        """创建默认词库（当没有真实数据时）"""
        return {
            "version": "2.3.0",
            "description": "默认负面情感词库",
            "categories": {
                "E_TERRIBLE": {
                    "atomic_signals": {
                        "双字符": ["退市", "违规", "亏损", "处罚", "调查"],
                        "三字符": ["严重亏损", "违法行为", "监管函"],
                        "四字符": ["退市风险", "立案调查"]
                    }
                },
                "D_BAD": {
                    "atomic_signals": {
                        "双字符": ["下降", "减少", "困难", "风险", "压力"],
                        "三字符": ["业绩下滑", "收入下降", "利润下降"]
                    }
                }
            }
        }
    
    def save_optimized_lexicon(self, lexicon: Dict, output_file: str = "optimized_financial_lexicon.json"):
        """保存优化后的词库"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(lexicon, f, ensure_ascii=False, indent=2)
        
        print(f"💾 优化词库已保存: {output_file}")
        
        # 显示统计信息
        if "statistics" in lexicon:
            stats = lexicon["statistics"]
            print(f"📊 词库统计:")
            print(f"   总类别数: {stats['total_categories']}")
            print(f"   总信号数: {stats['total_signals']}")
            print(f"   类别分布:")
            for category, count in stats["category_distribution"].items():
                print(f"     {category}: {count} 个信号")
        
        return output_file
    
    def compare_with_original(self, original_lexicon_file: str = "financial_negative_words.json"):
        """与原始词库对比"""
        if not Path(original_lexicon_file).exists():
            print(f"⚠️ 原始词库文件不存在: {original_lexicon_file}")
            return
        
        try:
            with open(original_lexicon_file, 'r', encoding='utf-8') as f:
                original = json.load(f)
            
            print(f"📊 词库对比:")
            print(f"   原始版本: {original.get('version', 'unknown')}")
            print(f"   优化版本: 2.3.0")
            
            if "all_negative_words" in original:
                original_count = len(original["all_negative_words"])
                print(f"   原始词汇数: {original_count}")
            
        except Exception as e:
            print(f"❌ 对比失败: {e}")

def main():
    """主函数"""
    print("🚀 基于真实公告数据的词库优化器")
    print("="*60)
    
    # 创建优化器
    optimizer = RealDataLexiconOptimizer()
    
    if optimizer.word_data is not None:
        # 分析真实数据模式
        analysis = optimizer.analyze_real_data_patterns()
        
        print(f"\n📊 真实数据分析结果:")
        print(f"   总词汇数: {analysis['total_words']}")
        print(f"   类别分布: {analysis['category_distribution']}")
        print(f"   高纯度词汇: {analysis['high_purity_words']}")
        
        # 创建优化词库
        optimized_lexicon = optimizer.create_optimized_lexicon(focus_on_recall=True)
        
        # 保存优化词库
        output_file = optimizer.save_optimized_lexicon(optimized_lexicon)
        
        # 与原始词库对比
        optimizer.compare_with_original()
        
        print(f"\n✅ 基于真实数据的词库优化完成！")
        print(f"📁 输出文件: {output_file}")
        print(f"🎯 优化策略: 高召回率 (宁可错杀不可放过)")
        
    else:
        print("❌ 无法加载真实数据，请检查文件路径")
        print("💡 建议：")
        print("   1. 确认 E:/LLMData/PythonGram/src/tools/output/ 目录存在")
        print("   2. 确认 word_frequency_analysis_*.xlsx 文件存在")
        print("   3. 检查文件权限")

if __name__ == "__main__":
    main()
