#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能规则分类器
基于学习到的规则进行智能分类，支持多层决策和置信度优化
"""

import re
import time
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ClassificationResult:
    """分类结果"""
    decision: str  # NEGATIVE, POSITIVE, NEUTRAL, UNCERTAIN
    confidence: float
    matched_patterns: List[str]
    filter_name: str
    processing_time: float
    rule_trace: List[str]
    
    # 兼容性属性
    @property
    def result(self):
        return type('Result', (), {'name': self.decision})()

class IntelligentRuleClassifier:
    """智能规则分类器 - 基于风险优先级的多层决策"""
    
    def __init__(self, rules: List[Dict]):
        """
        初始化智能规则分类器
        
        Args:
            rules: 从Python老师学到的规则列表
        """
        self.rules = rules
        self.compiled_rules = self._compile_rules(rules)
        
        # 分类统计
        self.stats = {
            'total_classifications': 0,
            'negative_classifications': 0,
            'positive_classifications': 0,
            'neutral_classifications': 0,
            'uncertain_classifications': 0
        }
        
        logger.info(f"🧠 智能规则分类器初始化完成")
        logger.info(f"📋 加载规则: {len(self.compiled_rules)} 条")
        
        # 显示核心规则
        core_rules = [r for r in self.compiled_rules if r.get('is_core_keyword', False)]
        logger.info(f"🎯 核心关键词规则: {len(core_rules)} 条")
        for rule in core_rules[:5]:
            logger.info(f"   - {rule['word']}: {rule['confidence']:.3f}")
    
    def _compile_rules(self, rules: List[Dict]) -> List[Dict]:
        """编译规则"""
        compiled_rules = []
        
        for rule in rules:
            try:
                pattern = re.compile(rule['pattern'], re.IGNORECASE)
                compiled_rule = {
                    'pattern': pattern,
                    'type': rule['type'],
                    'confidence': rule['confidence'],
                    'word': rule.get('word', ''),
                    'is_core_keyword': rule.get('is_core_keyword', False),
                    'priority': rule.get('priority', 'medium'),
                    'description': rule.get('description', '')
                }
                compiled_rules.append(compiled_rule)
            except re.error as e:
                logger.warning(f"⚠️ 规则编译失败: {rule.get('pattern', 'unknown')} - {e}")
                continue
        
        # 按优先级和置信度排序
        def rule_priority_key(rule):
            priority_weights = {
                'very_high': 4,
                'high': 3,
                'medium': 2,
                'low': 1
            }
            priority_weight = priority_weights.get(rule['priority'], 2)
            return (priority_weight, rule['confidence'])
        
        compiled_rules.sort(key=rule_priority_key, reverse=True)
        
        return compiled_rules
    
    def classify(self, title: str, content: str = "") -> ClassificationResult:
        """
        智能分类 - 基于风险优先级的多层决策
        
        Args:
            title: 文档标题
            content: 文档内容
            
        Returns:
            分类结果
        """
        import time
        start_time = time.time()
        
        self.stats['total_classifications'] += 1
        
        full_text = f"{title} {content}".strip()
        if not full_text:
            return self._create_result('UNCERTAIN', 0.0, [], ['文本为空'])
        
        classification_trace = []
        matched_rules = []
        
        # 第一层：寻找匹配的规则
        for rule in self.compiled_rules:
            if rule['pattern'].search(full_text):
                matched_rules.append(rule)
                classification_trace.append(f"匹配规则: {rule['word']} (置信度: {rule['confidence']:.3f})")
        
        if not matched_rules:
            classification_trace.append("未找到匹配规则")
            return self._create_result('UNCERTAIN', 0.0, [], classification_trace, start_time)
        
        # 第二层：风险优先级决策
        decision, confidence, final_patterns = self._apply_risk_priority_decision(matched_rules, classification_trace)
        
        # 更新统计
        if decision == 'NEGATIVE':
            self.stats['negative_classifications'] += 1
        elif decision == 'POSITIVE':
            self.stats['positive_classifications'] += 1
        elif decision == 'NEUTRAL':
            self.stats['neutral_classifications'] += 1
        else:
            self.stats['uncertain_classifications'] += 1
        
        return self._create_result(decision, confidence, final_patterns, classification_trace, start_time)
    
    def _apply_risk_priority_decision(self, matched_rules: List[Dict], trace: List[str]) -> Tuple[str, float, List[str]]:
        """
        应用风险优先级决策逻辑
        P0安全性 > P1可靠性 > P2效率
        """
        # P0 - 安全性：优先识别负面（绝不能遗漏）
        negative_rules = [r for r in matched_rules if r['type'] == 'negative']
        
        if negative_rules:
            # 选择置信度最高的负面规则
            best_negative = max(negative_rules, key=lambda x: x['confidence'])
            
            # 核心关键词规则：直接判定为负面
            if best_negative.get('is_core_keyword', False):
                trace.append(f"P0安全性: 核心负面关键词 '{best_negative['word']}' - 直接判定为负面")
                return 'NEGATIVE', max(0.8, best_negative['confidence']), [best_negative['word']]
            
            # 高置信度规则：判定为负面
            elif best_negative['confidence'] >= 0.7:
                trace.append(f"P0安全性: 高置信度负面规则 '{best_negative['word']}' ({best_negative['confidence']:.3f})")
                return 'NEGATIVE', best_negative['confidence'], [best_negative['word']]
            
            # 中等置信度：需要进一步验证
            elif best_negative['confidence'] >= 0.5:
                # 检查是否有其他负面信号支持
                supporting_rules = [r for r in negative_rules if r != best_negative and r['confidence'] >= 0.4]
                
                if supporting_rules:
                    combined_confidence = min(0.9, best_negative['confidence'] + 0.1 * len(supporting_rules))
                    trace.append(f"P0安全性: 多个负面信号支持 - 组合置信度 {combined_confidence:.3f}")
                    patterns = [best_negative['word']] + [r['word'] for r in supporting_rules[:2]]
                    return 'NEGATIVE', combined_confidence, patterns
                else:
                    trace.append(f"P0安全性: 中等置信度负面规则，但缺乏支持信号")
                    return 'UNCERTAIN', best_negative['confidence'], [best_negative['word']]
        
        # P1 - 可靠性：识别正面和中性（确保不误判）
        positive_rules = [r for r in matched_rules if r['type'] == 'positive']
        neutral_rules = [r for r in matched_rules if r['type'] == 'neutral']
        
        # 正面识别
        if positive_rules:
            best_positive = max(positive_rules, key=lambda x: x['confidence'])
            if best_positive['confidence'] >= 0.8:
                trace.append(f"P1可靠性: 高置信度正面规则 '{best_positive['word']}'")
                return 'POSITIVE', best_positive['confidence'], [best_positive['word']]
            elif best_positive['confidence'] >= 0.6:
                trace.append(f"P1可靠性: 中等置信度正面规则，需要验证")
                return 'UNCERTAIN', best_positive['confidence'], [best_positive['word']]
        
        # 中性识别（最保守）
        if neutral_rules:
            best_neutral = max(neutral_rules, key=lambda x: x['confidence'])
            if best_neutral['confidence'] >= 0.9:
                trace.append(f"P1可靠性: 极高置信度中性规则 '{best_neutral['word']}'")
                return 'NEUTRAL', best_neutral['confidence'], [best_neutral['word']]
            else:
                trace.append(f"P1可靠性: 中性规则置信度不足，标记为不确定")
                return 'UNCERTAIN', best_neutral['confidence'], [best_neutral['word']]
        
        # P2 - 效率：无法确定的情况
        trace.append("P2效率: 无法确定分类，标记为不确定")
        return 'UNCERTAIN', 0.0, []
    
    def _create_result(self, decision: str, confidence: float, patterns: List[str], 
                      trace: List[str], start_time: float = None) -> ClassificationResult:
        """创建分类结果"""
        processing_time = time.time() - start_time if start_time else 0.001
        
        return ClassificationResult(
            decision=decision,
            confidence=confidence,
            matched_patterns=patterns,
            filter_name="IntelligentRuleClassifier",
            processing_time=processing_time,
            rule_trace=trace
        )
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        total = self.stats['total_classifications']
        if total == 0:
            return {"message": "暂无分类数据"}
        
        return {
            "总分类次数": total,
            "负面分类": f"{self.stats['negative_classifications']} ({self.stats['negative_classifications']/total*100:.1f}%)",
            "正面分类": f"{self.stats['positive_classifications']} ({self.stats['positive_classifications']/total*100:.1f}%)",
            "中性分类": f"{self.stats['neutral_classifications']} ({self.stats['neutral_classifications']/total*100:.1f}%)",
            "不确定分类": f"{self.stats['uncertain_classifications']} ({self.stats['uncertain_classifications']/total*100:.1f}%)",
            "确定性比例": f"{(total - self.stats['uncertain_classifications'])/total*100:.1f}%"
        }

def test_intelligent_classifier():
    """测试智能分类器"""
    print("🧪 测试智能规则分类器")
    print("="*50)
    
    # 创建模拟规则
    test_rules = [
        {
            'pattern': r'.*亏损.*',
            'type': 'negative',
            'confidence': 0.85,
            'word': '亏损',
            'is_core_keyword': True,
            'priority': 'very_high'
        },
        {
            'pattern': r'.*违规.*',
            'type': 'negative', 
            'confidence': 0.90,
            'word': '违规',
            'is_core_keyword': True,
            'priority': 'very_high'
        },
        {
            'pattern': r'.*增长.*',
            'type': 'positive',
            'confidence': 0.75,
            'word': '增长',
            'priority': 'high'
        },
        {
            'pattern': r'.*股东大会.*',
            'type': 'neutral',
            'confidence': 0.95,
            'word': '股东大会',
            'priority': 'high'
        }
    ]
    
    # 创建分类器
    classifier = IntelligentRuleClassifier(test_rules)
    
    # 测试案例
    test_cases = [
        ("业绩预警", "公司预计2023年度净利润亏损5000万元", "NEGATIVE"),
        ("违规处罚", "公司因信息披露违规被交易所处罚", "NEGATIVE"),
        ("业绩增长", "公司净利润同比增长25%", "POSITIVE"),
        ("股东大会", "公司将于3月15日召开年度股东大会", "NEUTRAL"),
        ("一般公告", "公司发布一般性业务公告", "UNCERTAIN")
    ]
    
    print(f"\n🔍 智能分类测试:")
    correct_count = 0
    
    for title, content, expected in test_cases:
        result = classifier.classify(title, content)
        is_correct = result.decision == expected
        if is_correct:
            correct_count += 1
        
        print(f"\n📄 {title}:")
        print(f"   预期: {expected}")
        print(f"   结果: {result.decision} ({'✅' if is_correct else '❌'})")
        print(f"   置信度: {result.confidence:.3f}")
        print(f"   匹配模式: {result.matched_patterns}")
        if result.rule_trace:
            print(f"   决策轨迹: {result.rule_trace[-1]}")  # 显示最后一条轨迹
    
    accuracy = correct_count / len(test_cases)
    print(f"\n📊 测试结果:")
    print(f"   准确率: {accuracy:.1%} ({correct_count}/{len(test_cases)})")
    
    # 显示性能统计
    stats = classifier.get_performance_stats()
    print(f"\n📈 性能统计:")
    for key, value in stats.items():
        print(f"   {key}: {value}")

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    test_intelligent_classifier()
