#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多引擎NLP处理器
集成jieba、LAC、pkuseg、SnowNLP等多个开源分词工具
实现投票机制和结果融合，提升分词和关键词提取的准确性
"""

import logging
from typing import List, Dict, Tuple, Optional, Any
from collections import defaultdict, Counter
import re
import numpy as np

logger = logging.getLogger(__name__)

class MultiEngineNLP:
    """多引擎NLP处理器"""
    
    def __init__(self):
        self.engines = {}
        self.engine_weights = {}
        self.stop_words = self._load_stop_words()
        
        # 初始化各个引擎
        self._init_engines()
    
    def _load_stop_words(self) -> set:
        """加载停用词"""
        return {
            '万元', '万股', '亿元', '关于', '公司', '公告', '通知', 
            '报告', '会议', '决议', '股东', '董事会', '监事会',
            '事件标签', '其他', '进行', '相关', '情况', '事项',
            '的', '了', '在', '是', '有', '和', '与', '及', '或',
            '将', '已', '为', '由', '从', '向', '对', '按', '根据'
        }
    
    def _init_engines(self):
        """初始化所有可用的NLP引擎"""
        # 1. jieba引擎（已有）
        self._init_jieba_engine()
        
        # 2. LAC引擎（百度）
        self._init_lac_engine()
        
        # 3. pkuseg引擎（北大）
        self._init_pkuseg_engine()
        
        # 4. SnowNLP引擎（情感分析）
        self._init_snownlp_engine()
        
        # 5. HanLP引擎（如果可用）
        self._init_hanlp_engine()
        
        logger.info(f"NLP引擎初始化完成，可用引擎: {list(self.engines.keys())}")
    
    def _init_jieba_engine(self):
        """初始化jieba引擎"""
        try:
            from jieba_word_analyzer import JiebaWordAnalyzer
            self.engines['jieba'] = JiebaWordAnalyzer()
            self.engine_weights['jieba'] = 1.0  # 基准权重
            logger.info("✅ jieba引擎加载成功")
        except ImportError as e:
            logger.warning(f"⚠️ jieba引擎加载失败: {e}")
    
    def _init_lac_engine(self):
        """初始化LAC引擎"""
        try:
            from LAC import LAC
            self.engines['lac'] = LAC(mode='seg')
            self.engine_weights['lac'] = 1.2  # LAC在命名实体识别上更强
            logger.info("✅ LAC引擎加载成功")
        except ImportError:
            logger.info("ℹ️ LAC引擎未安装，跳过加载")
            logger.info("   安装命令: pip install lac")
        except Exception as e:
            logger.info(f"ℹ️ LAC引擎加载失败（兼容性问题）: {e}")
            logger.info("   LAC可能与当前Python版本不兼容，跳过加载")
    
    def _init_pkuseg_engine(self):
        """初始化pkuseg引擎"""
        try:
            import pkuseg
            # 尝试使用金融领域模型，如果没有则使用默认模型
            try:
                self.engines['pkuseg'] = pkuseg.pkuseg(model_name='finance')
                logger.info("✅ pkuseg引擎加载成功（金融模型）")
            except:
                self.engines['pkuseg'] = pkuseg.pkuseg()
                logger.info("✅ pkuseg引擎加载成功（默认模型）")

            self.engine_weights['pkuseg'] = 1.1  # pkuseg在正式文档上表现好
        except ImportError:
            logger.info("ℹ️ pkuseg引擎未安装，跳过加载")
            logger.info("   安装命令: pip install pkuseg")
        except Exception as e:
            logger.info(f"ℹ️ pkuseg引擎加载失败: {e}")
    
    def _init_snownlp_engine(self):
        """初始化SnowNLP引擎"""
        try:
            from snownlp import SnowNLP
            self.engines['snownlp'] = SnowNLP
            self.engine_weights['snownlp'] = 0.8  # 主要用于情感分析，分词权重较低
            logger.info("✅ SnowNLP引擎加载成功")
        except ImportError:
            logger.info("ℹ️ SnowNLP引擎未安装，跳过加载")
            logger.info("   安装命令: pip install snownlp")
        except Exception as e:
            logger.info(f"ℹ️ SnowNLP引擎加载失败: {e}")
    
    def _init_hanlp_engine(self):
        """初始化HanLP引擎"""
        try:
            import hanlp
            # 使用轻量级模型
            self.engines['hanlp'] = hanlp.load(hanlp.pretrained.tok.COARSE_ELECTRA_SMALL_ZH)
            self.engine_weights['hanlp'] = 1.3  # HanLP功能最全面
            logger.info("✅ HanLP引擎加载成功")
        except ImportError:
            logger.info("ℹ️ HanLP引擎未安装，跳过加载")
            logger.info("   安装命令: pip install hanlp")
        except Exception as e:
            logger.info(f"ℹ️ HanLP引擎加载失败: {e}")
    
    def segment_text_consensus(self, text: str) -> List[str]:
        """使用多引擎投票机制进行分词"""
        if not text or len(text.strip()) < 2:
            return []
        
        # 收集各引擎的分词结果
        engine_results = {}
        
        # jieba分词
        if 'jieba' in self.engines:
            try:
                import jieba
                engine_results['jieba'] = list(jieba.cut(text))
            except Exception as e:
                logger.debug(f"jieba分词失败: {e}")
        
        # LAC分词
        if 'lac' in self.engines:
            try:
                lac_result = self.engines['lac'].run(text)
                engine_results['lac'] = lac_result
            except Exception as e:
                logger.debug(f"LAC分词失败: {e}")
        
        # pkuseg分词
        if 'pkuseg' in self.engines:
            try:
                pkuseg_result = self.engines['pkuseg'].cut(text)
                engine_results['pkuseg'] = list(pkuseg_result)
            except Exception as e:
                logger.debug(f"pkuseg分词失败: {e}")
        
        # HanLP分词
        if 'hanlp' in self.engines:
            try:
                hanlp_result = self.engines['hanlp'](text)
                engine_results['hanlp'] = hanlp_result
            except Exception as e:
                logger.debug(f"HanLP分词失败: {e}")
        
        # 投票融合结果
        return self._merge_segmentation_results(engine_results)
    
    def _merge_segmentation_results(self, engine_results: Dict[str, List[str]]) -> List[str]:
        """融合多引擎分词结果"""
        if not engine_results:
            return []
        
        # 统计每个词条被多少个引擎识别
        word_votes = defaultdict(float)
        
        for engine, words in engine_results.items():
            engine_weight = self.engine_weights.get(engine, 1.0)
            for word in words:
                if self._is_valid_word(word):
                    word_votes[word] += engine_weight
        
        # 设置投票阈值：至少被一半的引擎识别
        min_votes = len(engine_results) * 0.5
        
        # 筛选高票词条
        consensus_words = [
            word for word, votes in word_votes.items()
            if votes >= min_votes
        ]
        
        return consensus_words
    
    def extract_keywords_enhanced(self, text: str, top_k: int = 20) -> List[Tuple[str, float]]:
        """增强的关键词提取"""
        if not text or len(text.strip()) < 2:
            return []
        
        all_keywords = defaultdict(list)
        
        # jieba关键词提取
        if 'jieba' in self.engines:
            try:
                jieba_keywords = self.engines['jieba'].analyze_text_quality(text)
                for word, info in jieba_keywords[:top_k]:
                    weight = info.get('combined_weight', 0) if isinstance(info, dict) else 1.0
                    all_keywords[word].append(('jieba', weight * self.engine_weights['jieba']))
            except Exception as e:
                logger.debug(f"jieba关键词提取失败: {e}")
        
        # LAC命名实体识别
        if 'lac' in self.engines:
            try:
                # LAC的词性标注模式
                lac_engine = self.engines.get('lac_pos')
                if not lac_engine:
                    from LAC import LAC
                    lac_engine = LAC(mode='lac')  # 词法分析模式
                    self.engines['lac_pos'] = lac_engine
                
                lac_result = lac_engine.run(text)
                words, pos_tags = lac_result
                
                # 筛选重要词性的词条
                important_pos = {'n', 'nr', 'ns', 'nt', 'nz', 'v', 'vn', 'a', 'ad'}
                for word, pos in zip(words, pos_tags):
                    if pos in important_pos and self._is_valid_word(word):
                        all_keywords[word].append(('lac', 1.0 * self.engine_weights['lac']))
            except Exception as e:
                logger.debug(f"LAC关键词提取失败: {e}")
        
        # SnowNLP情感分析增强
        if 'snownlp' in self.engines:
            try:
                snow = self.engines['snownlp'](text)
                # 提取情感相关的关键词
                sentiment_score = snow.sentiments
                
                # 基于情感分数调整词条权重
                words = self.segment_text_consensus(text)
                for word in words:
                    if self._is_valid_word(word):
                        # 情感极性越强，权重越高
                        sentiment_weight = abs(sentiment_score - 0.5) * 2
                        all_keywords[word].append(('snownlp', sentiment_weight * self.engine_weights['snownlp']))
            except Exception as e:
                logger.debug(f"SnowNLP分析失败: {e}")
        
        # 融合所有引擎的结果
        final_keywords = []
        for word, engine_results in all_keywords.items():
            if self._is_valid_word(word):
                # 计算加权平均分数
                total_weight = sum(weight for _, weight in engine_results)
                engine_count = len(engine_results)
                
                # 引擎一致性加权
                consensus_bonus = min(engine_count / len(self.engines), 1.0)
                final_weight = (total_weight / engine_count) * (1 + consensus_bonus)
                
                final_keywords.append((word, final_weight))
        
        # 按权重排序并返回Top K
        final_keywords.sort(key=lambda x: x[1], reverse=True)
        return final_keywords[:top_k]
    
    def _is_valid_word(self, word: str) -> bool:
        """判断词条是否有效"""
        if not word or not isinstance(word, str):
            return False
        
        word = word.strip()
        
        # 长度检查
        if len(word) < 2 or len(word) > 10:
            return False
        
        # 停用词检查
        if word in self.stop_words:
            return False
        
        # 纯数字或特殊字符检查
        if re.match(r'^[\d\W]+$', word):
            return False
        
        # 单字符重复检查
        if len(set(word)) == 1:
            return False
        
        return True
    
    def analyze_sentiment_enhanced(self, text: str) -> Dict[str, float]:
        """增强的情感分析"""
        sentiment_results = {}
        
        # SnowNLP情感分析
        if 'snownlp' in self.engines:
            try:
                snow = self.engines['snownlp'](text)
                sentiment_results['snownlp'] = snow.sentiments
            except Exception as e:
                logger.debug(f"SnowNLP情感分析失败: {e}")
        
        # 基于词典的情感分析
        sentiment_results['dictionary'] = self._dictionary_sentiment_analysis(text)
        
        # 融合情感分析结果
        if sentiment_results:
            avg_sentiment = np.mean(list(sentiment_results.values()))
            sentiment_results['consensus'] = avg_sentiment
        
        return sentiment_results
    
    def _dictionary_sentiment_analysis(self, text: str) -> float:
        """基于词典的情感分析"""
        # 简单的情感词典
        positive_words = {
            '增长', '上升', '提升', '改善', '优化', '突破', '成功', '盈利',
            '增加', '扩大', '发展', '进步', '创新', '领先', '优秀', '良好'
        }
        
        negative_words = {
            '下降', '减少', '亏损', '风险', '问题', '困难', '挑战', '压力',
            '违规', '处罚', '诉讼', '纠纷', '警示', '警告', '退市', '暂停'
        }
        
        # 分词
        words = self.segment_text_consensus(text)
        
        # 计算情感分数
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)
        total_sentiment_words = positive_count + negative_count
        
        if total_sentiment_words == 0:
            return 0.5  # 中性
        
        return positive_count / total_sentiment_words
    
    def extract_named_entities(self, text: str) -> Dict[str, List[str]]:
        """提取命名实体"""
        entities = {
            'organizations': [],  # 机构名
            'persons': [],        # 人名
            'money': [],          # 金额
            'dates': [],          # 日期
            'locations': []       # 地点
        }
        
        # LAC命名实体识别
        if 'lac' in self.engines:
            try:
                # 使用LAC的命名实体识别模式
                lac_ner = self.engines.get('lac_ner')
                if not lac_ner:
                    from LAC import LAC
                    lac_ner = LAC(mode='lac')
                    self.engines['lac_ner'] = lac_ner
                
                lac_result = lac_ner.run(text)
                words, pos_tags = lac_result
                
                # 根据词性标注提取实体
                for word, pos in zip(words, pos_tags):
                    if pos == 'ORG':
                        entities['organizations'].append(word)
                    elif pos == 'PER':
                        entities['persons'].append(word)
                    elif pos == 'LOC':
                        entities['locations'].append(word)
                    elif re.match(r'.*[万亿千百十]元.*', word):
                        entities['money'].append(word)
                    elif re.match(r'.*\d{4}年.*', word):
                        entities['dates'].append(word)
            
            except Exception as e:
                logger.debug(f"LAC命名实体识别失败: {e}")
        
        # 使用正则表达式补充提取
        entities.update(self._regex_entity_extraction(text))
        
        return entities
    
    def _regex_entity_extraction(self, text: str) -> Dict[str, List[str]]:
        """使用正则表达式提取实体"""
        entities = {
            'money': [],
            'dates': [],
            'percentages': []
        }
        
        # 金额提取
        money_patterns = [
            r'\d+(?:\.\d+)?[万亿千百十]*元',
            r'\d+(?:\.\d+)?万股',
            r'\d+(?:\.\d+)?亿股'
        ]
        
        for pattern in money_patterns:
            matches = re.findall(pattern, text)
            entities['money'].extend(matches)
        
        # 日期提取
        date_patterns = [
            r'\d{4}年\d{1,2}月\d{1,2}日',
            r'\d{4}年\d{1,2}月',
            r'\d{4}年度'
        ]
        
        for pattern in date_patterns:
            matches = re.findall(pattern, text)
            entities['dates'].extend(matches)
        
        # 百分比提取
        percentage_pattern = r'\d+(?:\.\d+)?%'
        entities['percentages'] = re.findall(percentage_pattern, text)
        
        return entities
    
    def get_engine_status(self) -> Dict[str, Dict]:
        """获取引擎状态信息"""
        status = {}
        
        for engine_name in ['jieba', 'lac', 'pkuseg', 'snownlp', 'hanlp']:
            if engine_name in self.engines:
                status[engine_name] = {
                    'status': 'loaded',
                    'weight': self.engine_weights.get(engine_name, 1.0),
                    'description': self._get_engine_description(engine_name)
                }
            else:
                status[engine_name] = {
                    'status': 'not_available',
                    'weight': 0.0,
                    'description': self._get_engine_description(engine_name)
                }
        
        return status
    
    def _get_engine_description(self, engine_name: str) -> str:
        """获取引擎描述"""
        descriptions = {
            'jieba': 'Python中文分词库，支持TextRank和TF-IDF',
            'lac': '百度开源词法分析工具，擅长命名实体识别',
            'pkuseg': '北京大学开源分词工具，支持多领域模型',
            'snownlp': '中文情感分析库，专门针对情感极性判断',
            'hanlp': '自然语言处理工具包，功能最全面'
        }
        return descriptions.get(engine_name, '未知引擎')
    
    def benchmark_engines(self, test_texts: List[str]) -> Dict[str, Dict]:
        """对比各引擎的性能"""
        benchmark_results = {}
        
        for engine_name in self.engines.keys():
            benchmark_results[engine_name] = {
                'total_keywords': 0,
                'unique_keywords': set(),
                'avg_processing_time': 0,
                'error_count': 0
            }
        
        import time
        
        for text in test_texts:
            for engine_name in self.engines.keys():
                start_time = time.time()
                try:
                    if engine_name == 'jieba':
                        keywords = self.engines[engine_name].analyze_text_quality(text)
                        benchmark_results[engine_name]['total_keywords'] += len(keywords)
                        benchmark_results[engine_name]['unique_keywords'].update([w for w, _ in keywords])
                    else:
                        # 其他引擎的简单测试
                        keywords = self.extract_keywords_enhanced(text, top_k=10)
                        benchmark_results[engine_name]['total_keywords'] += len(keywords)
                        benchmark_results[engine_name]['unique_keywords'].update([w for w, _ in keywords])
                    
                    processing_time = time.time() - start_time
                    benchmark_results[engine_name]['avg_processing_time'] += processing_time
                    
                except Exception as e:
                    benchmark_results[engine_name]['error_count'] += 1
                    logger.debug(f"{engine_name}引擎测试失败: {e}")
        
        # 计算平均值
        for engine_name in benchmark_results.keys():
            if len(test_texts) > 0:
                benchmark_results[engine_name]['avg_processing_time'] /= len(test_texts)
                benchmark_results[engine_name]['unique_keywords'] = len(benchmark_results[engine_name]['unique_keywords'])
        
        return benchmark_results

def test_multi_engine_nlp():
    """测试多引擎NLP处理器"""
    print("🧪 测试多引擎NLP处理器...")
    
    nlp = MultiEngineNLP()
    
    # 测试文本
    test_texts = [
        "公司预计2023年度净利润为亏损5,000万元至8,000万元，比上年同期下降150%至200%",
        "公司召开临时股东大会，审议董事会工作报告等议案",
        "公司拟向特定对象发行不超过1,000万股股票，募集资金不超过2亿元"
    ]
    
    print(f"\n📊 引擎状态:")
    engine_status = nlp.get_engine_status()
    for engine, status in engine_status.items():
        status_icon = "✅" if status['status'] == 'loaded' else "❌"
        print(f"  {status_icon} {engine}: {status['description']}")
    
    print(f"\n🔍 关键词提取测试:")
    for i, text in enumerate(test_texts, 1):
        print(f"\n测试文本 {i}: {text[:50]}...")
        
        # 多引擎关键词提取
        keywords = nlp.extract_keywords_enhanced(text, top_k=10)
        print("关键词:")
        for word, weight in keywords:
            print(f"  {word}: {weight:.3f}")
        
        # 命名实体识别
        entities = nlp.extract_named_entities(text)
        print("命名实体:")
        for entity_type, entity_list in entities.items():
            if entity_list:
                print(f"  {entity_type}: {entity_list}")
    
    # 性能基准测试
    print(f"\n⚡ 性能基准测试:")
    benchmark = nlp.benchmark_engines(test_texts)
    for engine, results in benchmark.items():
        if engine in nlp.engines:
            print(f"  {engine}: 平均耗时={results['avg_processing_time']:.3f}s, "
                  f"关键词数={results['total_keywords']}, "
                  f"错误数={results['error_count']}")

if __name__ == "__main__":
    test_multi_engine_nlp()
