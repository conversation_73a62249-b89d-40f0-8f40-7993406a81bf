#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
低召回率案例分析器
收集漏检案例，分析特征上下文，提取漏掉的词汇
"""

import re
import jieba
import pandas as pd
from pathlib import Path
from typing import List, Dict, Set
from collections import Counter
from datetime import datetime

class LowRecallCaseAnalyzer:
    """低召回率案例分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.missed_cases = []
        self.context_features = []
        self.missed_words = set()
        
        # 版本号升级
        self.version = "2.4.0"  # 🔍 低召回率分析版本
        
        print(f"🔍 低召回率案例分析器 v{self.version} 初始化完成")
    
    def collect_missed_cases(self) -> List[Dict]:
        """收集漏检案例"""
        print("📋 收集已知的漏检案例...")
        
        # 基于测试结果的漏检案例
        missed_cases = [
            {
                "text": "订单减少客户流失",
                "expected": "NEGATIVE",
                "actual": "NEUTRAL",
                "reason": "缺少基础负面词汇",
                "context_type": "商业经营"
            },
            {
                "text": "公司经营遇到困难", 
                "expected": "NEGATIVE",
                "actual": "NEUTRAL",
                "reason": "困难词汇缺失",
                "context_type": "经营状况"
            },
            {
                "text": "利润出现减少",
                "expected": "NEGATIVE", 
                "actual": "NEUTRAL",
                "reason": "减少动词缺失或分类错误",
                "context_type": "财务表现"
            },
            {
                "text": "市场竞争加剧压力增大",
                "expected": "NEGATIVE",
                "actual": "POSITIVE",  # 被误判为正面
                "reason": "压力、加剧等词汇处理不当",
                "context_type": "市场环境"
            },
            {
                "text": "技术创新取得突破",
                "expected": "POSITIVE",
                "actual": "NEUTRAL",
                "reason": "正面词汇识别不足",
                "context_type": "技术发展"
            },
            {
                "text": "签署重大合作协议",
                "expected": "POSITIVE", 
                "actual": "NEUTRAL",
                "reason": "正面词汇识别不足",
                "context_type": "商业合作"
            }
        ]
        
        self.missed_cases = missed_cases
        print(f"✅ 收集了 {len(missed_cases)} 个漏检案例")
        return missed_cases
    
    def extract_context_features(self, cases: List[Dict]) -> List[Dict]:
        """提取上下文特征"""
        print("🔍 提取上下文特征...")
        
        context_features = []
        
        for case in cases:
            text = case["text"]
            
            # 使用jieba分词
            words = list(jieba.cut(text))
            
            # 提取特征
            features = {
                "original_text": text,
                "expected_category": case["expected"],
                "actual_category": case["actual"],
                "context_type": case["context_type"],
                "reason": case["reason"],
                "jieba_words": words,
                "word_count": len(words),
                "char_count": len(text),
                "contains_numbers": bool(re.search(r'\d', text)),
                "key_patterns": self._extract_key_patterns(text),
                "potential_signals": self._identify_potential_signals(words)
            }
            
            context_features.append(features)
        
        self.context_features = context_features
        print(f"✅ 提取了 {len(context_features)} 个案例的特征")
        return context_features
    
    def _extract_key_patterns(self, text: str) -> List[str]:
        """提取关键模式"""
        patterns = []
        
        # 动词+名词模式
        verb_noun_pattern = re.findall(r'(减少|增加|下降|上升|流失|获得|提升|恶化)([^，。！？]*)', text)
        if verb_noun_pattern:
            patterns.extend([f"{verb}{noun}" for verb, noun in verb_noun_pattern])
        
        # 形容词+名词模式  
        adj_noun_pattern = re.findall(r'(严重|重大|显著|轻微|巨大)([^，。！？]*)', text)
        if adj_noun_pattern:
            patterns.extend([f"{adj}{noun}" for adj, noun in adj_noun_pattern])
        
        # 公司+动词模式
        company_verb_pattern = re.findall(r'(公司|企业|集团)([^，。！？]*)(面临|遇到|实现|获得|签署)', text)
        if company_verb_pattern:
            patterns.extend([f"公司{middle}{verb}" for company, middle, verb in company_verb_pattern])
        
        return patterns
    
    def _identify_potential_signals(self, words: List[str]) -> Dict:
        """识别潜在信号词汇"""
        potential_signals = {
            "negative_candidates": [],
            "positive_candidates": [],
            "neutral_candidates": []
        }
        
        # 预定义的信号词汇模式
        negative_patterns = {
            "动词": ["减少", "下降", "流失", "恶化", "缺失", "不足", "降低", "萎缩"],
            "形容词": ["困难", "严峻", "恶劣", "疲软", "低迷", "不佳", "糟糕"],
            "名词": ["风险", "危机", "问题", "挑战", "压力", "损失", "亏损"]
        }
        
        positive_patterns = {
            "动词": ["增长", "提升", "改善", "优化", "突破", "获得", "实现", "签署"],
            "形容词": ["优秀", "卓越", "显著", "重大", "成功", "良好"],
            "名词": ["机遇", "收益", "利润", "成果", "突破", "合作", "协议"]
        }
        
        for word in words:
            # 检查负面信号
            for category, patterns in negative_patterns.items():
                if word in patterns:
                    potential_signals["negative_candidates"].append({
                        "word": word,
                        "category": category,
                        "pattern_type": "negative"
                    })
            
            # 检查正面信号
            for category, patterns in positive_patterns.items():
                if word in patterns:
                    potential_signals["positive_candidates"].append({
                        "word": word,
                        "category": category, 
                        "pattern_type": "positive"
                    })
        
        return potential_signals
    
    def analyze_missed_words(self) -> Dict:
        """分析漏掉的词汇"""
        print("📊 分析漏掉的词汇...")
        
        all_missed_words = []
        word_frequency = Counter()
        category_distribution = {}
        
        for feature in self.context_features:
            jieba_words = feature["jieba_words"]
            expected_category = feature["expected_category"]
            
            # 统计词频
            for word in jieba_words:
                if len(word) >= 2 and re.match(r'^[\u4e00-\u9fff]+$', word):  # 只考虑2字以上的中文词汇
                    word_frequency[word] += 1
                    all_missed_words.append(word)
                    
                    if expected_category not in category_distribution:
                        category_distribution[expected_category] = Counter()
                    category_distribution[expected_category][word] += 1
        
        analysis_result = {
            "total_missed_words": len(set(all_missed_words)),
            "word_frequency": dict(word_frequency.most_common(20)),
            "category_distribution": {
                category: dict(counter.most_common(10)) 
                for category, counter in category_distribution.items()
            },
            "high_frequency_candidates": [
                word for word, freq in word_frequency.most_common(10) if freq >= 2
            ]
        }
        
        print(f"✅ 发现 {analysis_result['total_missed_words']} 个潜在漏掉的词汇")
        return analysis_result
    
    def save_context_analysis(self, output_file: str = "low_recall_context_analysis.txt") -> str:
        """保存上下文分析结果到txt文件"""
        print(f"💾 保存上下文分析到: {output_file}")
        
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("低召回率案例上下文分析报告\n")
            f.write("="*60 + "\n")
            f.write(f"生成时间: {timestamp}\n")
            f.write(f"分析器版本: v{self.version}\n")
            f.write(f"分析案例数: {len(self.missed_cases)}\n\n")
            
            # 1. 漏检案例详情
            f.write("📋 漏检案例详情:\n")
            f.write("-"*40 + "\n")
            for i, case in enumerate(self.missed_cases, 1):
                f.write(f"{i}. 文本: {case['text']}\n")
                f.write(f"   预期: {case['expected']} | 实际: {case['actual']}\n")
                f.write(f"   原因: {case['reason']}\n")
                f.write(f"   类型: {case['context_type']}\n\n")
            
            # 2. 上下文特征分析
            f.write("🔍 上下文特征分析:\n")
            f.write("-"*40 + "\n")
            for i, feature in enumerate(self.context_features, 1):
                f.write(f"{i}. 原文: {feature['original_text']}\n")
                f.write(f"   分词结果: {' | '.join(feature['jieba_words'])}\n")
                f.write(f"   关键模式: {feature['key_patterns']}\n")
                f.write(f"   潜在信号: {feature['potential_signals']}\n")
                f.write(f"   上下文类型: {feature['context_type']}\n\n")
            
            # 3. 词汇分析结果
            analysis_result = self.analyze_missed_words()
            f.write("📊 漏掉词汇分析:\n")
            f.write("-"*40 + "\n")
            f.write(f"总词汇数: {analysis_result['total_missed_words']}\n\n")
            
            f.write("高频词汇 (出现2次以上):\n")
            for word in analysis_result['high_frequency_candidates']:
                freq = analysis_result['word_frequency'][word]
                f.write(f"  - {word} (出现{freq}次)\n")
            f.write("\n")
            
            f.write("按类别分布:\n")
            for category, words in analysis_result['category_distribution'].items():
                f.write(f"  {category}类漏掉的词汇:\n")
                for word, freq in words.items():
                    f.write(f"    - {word} ({freq}次)\n")
                f.write("\n")
            
            # 4. 建议补充的词汇
            f.write("💡 建议补充的高优先级词汇:\n")
            f.write("-"*40 + "\n")
            
            # 基于分析结果提取建议
            negative_suggestions = []
            positive_suggestions = []
            
            for feature in self.context_features:
                if feature['expected_category'] == 'NEGATIVE':
                    for signal in feature['potential_signals']['negative_candidates']:
                        negative_suggestions.append(signal['word'])
                elif feature['expected_category'] == 'POSITIVE':
                    for signal in feature['potential_signals']['positive_candidates']:
                        positive_suggestions.append(signal['word'])
            
            f.write("负面词汇建议:\n")
            for word in set(negative_suggestions):
                f.write(f"  - {word}\n")
            
            f.write("\n正面词汇建议:\n")
            for word in set(positive_suggestions):
                f.write(f"  - {word}\n")
            
            f.write(f"\n✅ 分析完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"✅ 上下文分析已保存: {output_file}")
        return output_file
    
    def run_complete_analysis(self) -> str:
        """运行完整的低召回率分析"""
        print("🚀 开始完整的低召回率案例分析...")
        
        # 1. 收集漏检案例
        cases = self.collect_missed_cases()
        
        # 2. 提取上下文特征
        features = self.extract_context_features(cases)
        
        # 3. 保存分析结果
        output_file = self.save_context_analysis()
        
        print(f"🎉 低召回率分析完成！")
        print(f"📁 分析报告: {output_file}")
        print(f"💡 下一步: 使用分词软件进一步分析报告中的词汇")
        
        return output_file

def main():
    """主函数"""
    print("🔍 低召回率案例分析器")
    print("="*60)
    
    # 创建分析器
    analyzer = LowRecallCaseAnalyzer()
    
    # 运行完整分析
    output_file = analyzer.run_complete_analysis()
    
    print(f"\n📋 分析总结:")
    print(f"   ✅ 收集了 {len(analyzer.missed_cases)} 个漏检案例")
    print(f"   ✅ 提取了 {len(analyzer.context_features)} 个特征集")
    print(f"   ✅ 生成了详细的上下文分析报告")
    print(f"   📁 报告文件: {output_file}")
    
    print(f"\n💡 建议的后续步骤:")
    print(f"   1. 查看 {output_file} 了解详细分析")
    print(f"   2. 使用jieba等分词工具进一步分析")
    print(f"   3. 基于分析结果补充词库")
    print(f"   4. 重新测试优化效果")

if __name__ == "__main__":
    main()
