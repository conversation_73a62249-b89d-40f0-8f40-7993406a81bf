#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查JSON文件结构
分析decisionLogic字段和分类逻辑
"""

import json
from pathlib import Path
import random
from collections import defaultdict

def check_json_structure():
    """检查JSON文件结构"""
    print("🔍 检查JSON文件结构...")
    
    analysis_dir = Path('D:/LLMData/analysis_output')
    json_files = list(analysis_dir.glob('*.json'))
    
    print(f"📊 总共找到 {len(json_files)} 个JSON文件")
    
    # 随机选择10个文件进行详细检查
    sample_files = random.sample(json_files, min(10, len(json_files)))
    
    decision_logic_stats = defaultdict(int)
    rating_level_stats = defaultdict(int)
    
    for i, file in enumerate(sample_files, 1):
        try:
            with open(file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f'\n=== 文件 {i}: {file.name} ===')
            
            # 检查decisionLogic
            if 'decisionLogic' in data:
                decision_logic = data['decisionLogic']
                print(f'decisionLogic存在，包含引擎: {list(decision_logic.keys())}')
                
                # 分析各引擎的决策
                for engine, decisions in decision_logic.items():
                    if decisions:
                        latest_version = max(decisions.keys())
                        decision = decisions[latest_version].get('decision', 'UNKNOWN')
                        confidence = decisions[latest_version].get('confidence', 'N/A')
                        print(f'  {engine}.{latest_version}: {decision} (置信度: {confidence})')
                        decision_logic_stats[decision] += 1
            else:
                print('❌ 无decisionLogic字段')
            
            # 检查传统字段
            if 'ratingLevel' in data:
                rating = data['ratingLevel']
                print(f'ratingLevel: {rating}')
                rating_level_stats[rating] += 1
            
            # 检查title和keyFactors
            if 'title' in data:
                title = data['title'][:80] + '...' if len(data['title']) > 80 else data['title']
                print(f'title: {title}')
            
            if 'keyFactors' in data:
                key_factors = data['keyFactors']
                if isinstance(key_factors, list):
                    print(f'keyFactors: {key_factors[:3]}...' if len(key_factors) > 3 else f'keyFactors: {key_factors}')
                else:
                    print(f'keyFactors: {str(key_factors)[:100]}...')
            
        except Exception as e:
            print(f'❌ 读取文件失败: {e}')
    
    # 统计结果
    print(f'\n📊 decisionLogic决策统计:')
    for decision, count in decision_logic_stats.items():
        print(f'  {decision}: {count}')
    
    print(f'\n📊 ratingLevel统计:')
    for rating, count in rating_level_stats.items():
        print(f'  {rating}: {count}')

def analyze_sample_distribution():
    """分析样本分布"""
    print("\n🎯 分析样本分布...")
    
    analysis_dir = Path('D:/LLMData/analysis_output')
    json_files = list(analysis_dir.glob('*.json'))
    
    # 分析前1000个文件的分布
    sample_files = json_files[:1000]
    
    classification_stats = defaultdict(int)
    title_keywords = defaultdict(int)
    
    for file in sample_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 分类统计
            category = determine_category(data)
            classification_stats[category] += 1
            
            # 标题关键词统计
            title = data.get('title', '')
            if '业绩' in title:
                title_keywords['业绩相关'] += 1
            if '风险' in title:
                title_keywords['风险相关'] += 1
            if '董事' in title or '股东' in title:
                title_keywords['治理相关'] += 1
            if '重组' in title:
                title_keywords['重组相关'] += 1
            if '违规' in title or '处罚' in title:
                title_keywords['违规相关'] += 1
            
        except Exception as e:
            print(f'处理文件失败: {e}')
    
    print(f'\n📊 前1000个文件的分类分布:')
    total = sum(classification_stats.values())
    for category, count in classification_stats.items():
        percentage = (count / total * 100) if total > 0 else 0
        print(f'  {category}: {count} ({percentage:.1f}%)')
    
    print(f'\n📊 标题关键词分布:')
    for keyword, count in title_keywords.items():
        percentage = (count / total * 100) if total > 0 else 0
        print(f'  {keyword}: {count} ({percentage:.1f}%)')

def determine_category(data):
    """确定数据类别（复制分析器的逻辑）"""
    decision_logic = data.get('decisionLogic', {})
    
    # 优先级：java_regex_filter > python_backtest_rules > legacy_db_rules
    for engine in ['java_regex_filter', 'python_backtest_rules', 'legacy_db_rules']:
        if engine in decision_logic:
            engine_decisions = decision_logic[engine]
            
            if engine_decisions:
                latest_version = max(engine_decisions.keys())
                decision = engine_decisions[latest_version].get('decision', 'UNCERTAIN')
                
                if decision == 'NEGATIVE':
                    return 'negative'
                elif decision == 'POSITIVE':
                    return 'positive'
                elif decision == 'NEUTRAL':
                    return 'neutral'
    
    return 'uncertain'

if __name__ == "__main__":
    check_json_structure()
    analyze_sample_distribution()
