#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金融负面情感词库
基于原子信号分析结果 + 开源词库整合
"""

class FinancialNegativeLexicon:
    """金融负面情感词库"""
    
    def __init__(self):
        """初始化词库"""
        # 基于您的原子信号分析发现的高纯度负面词汇 + 细粒度拆解
        self.atomic_negative_words = {
            # E类 - 极差 (纯度1.0) - 细粒度拆解
            "退市类": [
                # 核心原子信号 - 最高优先级
                "退市", "摘牌",
                # 相关原子信号
                "终止上市", "暂停转让", "整理期",
                # 保留少数无法被原子信号覆盖的特殊表达
                "交易期满将被终止上市"
            ],

            "亏损类": [
                # 核心原子信号 - 最高优先级
                "亏损", "转亏", "扣非",
                # 相关原子信号
                "巨亏", "大亏", "预亏", "首亏",
                # 保留关键组合表达
                "由盈转亏"
            ],

            "违规类": [
                # 核心原子信号 - 最高优先级
                "违规", "违法", "处罚", "立案", "调查",
                # 相关原子信号
                "罚款", "警示", "监管", "违反", "涉嫌", "整改",
                # 保留关键专业术语
                "监管函", "行政处罚", "内幕交易"
            ],

            "经营类": [
                # 原始词汇
                "经营状况不佳", "财务困难", "资金链断裂", "债务违约",
                "经营危机", "业务停滞",
                # 细粒度拆解
                "困难", "危机", "停滞", "断裂", "违约", "逾期", "欠款", "债务",
                "资金链", "现金流", "周转", "紧张", "恶化", "不佳"
            ],

            # D类 - 不良 (纯度0.8+) - 细粒度拆解
            "下降类": [
                # 原始词汇
                "业绩下滑", "收入下降", "利润下降", "同比下降", "环比下降",
                "大幅下降", "显著下降", "持续下降",
                # 细粒度拆解
                "下降", "下滑", "下跌", "减少", "降低", "缩减", "萎缩", "衰减",
                "同比", "环比", "大幅", "显著", "持续", "连续", "急剧"
            ],

            "风险类": [
                # 原始词汇
                "经营风险", "财务风险", "市场风险", "信用风险", "流动性风险",
                "投资风险", "政策风险", "汇率风险",
                # 细粒度拆解
                "风险", "隐患", "威胁", "挑战", "压力", "不确定", "波动", "变化",
                "影响", "冲击", "损失", "损害", "不利", "负面"
            ],

            "困难类": [
                # 原始词汇
                "经营困难", "财务困难", "资金困难", "市场困难", "竞争困难",
                "发展困难", "盈利困难",
                # 细粒度拆解
                "困难", "艰难", "严峻", "复杂", "不利", "恶劣", "疲软", "低迷",
                "萧条", "衰退", "收缩", "紧缩", "调整", "重组"
            ]
        }
        
        # 开源词库补充 (来自BosonNLP、大连理工等) - 细粒度扩展
        self.opensource_negative_words = {
            "市场表现": [
                # 原始词汇
                "暴跌", "大跌", "跳水", "崩盘", "闪崩", "重挫", "急跌",
                "连跌", "深跌", "狂跌", "惨跌", "血崩",
                # 细粒度拆解
                "跌", "跌停", "跌幅", "跌破", "下跌", "重跌", "补跌", "探底",
                "破位", "失守", "创低", "新低", "低位", "底部", "熊市"
            ],

            "财务状况": [
                # 原始词汇
                "资不抵债", "入不敷出", "捉襟见肘", "财务恶化", "现金流紧张",
                "偿债能力不足", "财务造假", "会计差错",
                # 细粒度拆解
                "造假", "差错", "错报", "漏报", "瞒报", "虚报", "不实", "失真",
                "紧张", "短缺", "不足", "缺口", "缺失", "恶化", "恶劣"
            ],

            "业务经营": [
                # 原始词汇
                "业务萎缩", "市场萎缩", "订单减少", "客户流失", "竞争激烈",
                "盈利能力下降", "毛利率下降", "成本上升",
                # 细粒度拆解
                "萎缩", "收缩", "减少", "流失", "丢失", "失去", "损失", "缺失",
                "上升", "上涨", "增加", "激烈", "加剧", "恶性", "无序"
            ],

            "公司治理": [
                # 原始词汇
                "内控缺失", "治理混乱", "管理不善", "决策失误", "战略失败",
                "高管离职", "董事会分歧", "股东纠纷",
                # 细粒度拆解
                "缺失", "混乱", "不善", "失误", "失败", "失控", "失职", "失当",
                "离职", "辞职", "辞任", "分歧", "纠纷", "争议", "冲突", "矛盾"
            ],

            "外部环境": [
                # 原始词汇
                "政策不利", "监管趋严", "行业低迷", "需求疲软", "供给过剩",
                "贸易摩擦", "汇率波动", "原材料涨价",
                # 细粒度拆解
                "不利", "趋严", "严格", "严厉", "低迷", "疲软", "过剩", "过量",
                "摩擦", "波动", "震荡", "涨价", "上涨", "飙升", "暴涨"
            ],

            # 新增细粒度类别
            "单字负面词": [
                "跌", "降", "减", "亏", "损", "欠", "缺", "少", "弱", "差",
                "坏", "劣", "糟", "惨", "败", "输", "输", "负", "倒", "破"
            ],

            "双字负面词": [
                "下跌", "下降", "减少", "亏损", "损失", "缺少", "不足", "疲软",
                "恶化", "困难", "危机", "风险", "问题", "麻烦", "失败", "错误"
            ],

            "程度加强词": [
                "严重", "重大", "巨大", "大幅", "显著", "明显", "急剧", "快速",
                "持续", "连续", "反复", "频繁", "不断", "一直", "始终", "长期"
            ]
        }
        
        # 程度副词 (用于增强负面程度)
        self.intensity_modifiers = {
            "极度": 1.0, "严重": 0.9, "大幅": 0.8, "显著": 0.7, "明显": 0.6,
            "持续": 0.8, "连续": 0.7, "反复": 0.6, "频繁": 0.5,
            "急剧": 0.9, "快速": 0.7, "迅速": 0.6, "突然": 0.8
        }
    
    def get_all_negative_words(self) -> list:
        """获取所有负面词汇"""
        all_words = []
        
        # 添加原子信号词汇
        for category, words in self.atomic_negative_words.items():
            all_words.extend(words)
        
        # 添加开源词库词汇
        for category, words in self.opensource_negative_words.items():
            all_words.extend(words)
        
        return list(set(all_words))  # 去重
    
    def get_high_priority_words(self) -> list:
        """获取高优先级负面词汇 (基于原子信号分析)"""
        high_priority = []
        
        # E类词汇 - 最高优先级
        for category, words in self.atomic_negative_words.items():
            if any(keyword in category for keyword in ["退市", "亏损", "违规"]):
                high_priority.extend(words)
        
        return high_priority
    
    def calculate_negative_score(self, text: str) -> float:
        """
        计算文本的负面情感分数
        
        Args:
            text: 输入文本
            
        Returns:
            负面分数 (0-1之间，越高越负面)
        """
        if not text:
            return 0.0
        
        negative_score = 0.0
        word_count = 0
        
        # 检查高优先级词汇
        high_priority_words = self.get_high_priority_words()
        for word in high_priority_words:
            if word in text:
                negative_score += 1.0  # 高优先级词汇权重为1.0
                word_count += 1
        
        # 检查其他负面词汇
        all_negative_words = self.get_all_negative_words()
        for word in all_negative_words:
            if word in text and word not in high_priority_words:
                negative_score += 0.6  # 普通负面词汇权重为0.6
                word_count += 1
        
        # 检查程度副词增强
        for modifier, weight in self.intensity_modifiers.items():
            if modifier in text:
                negative_score *= (1 + weight * 0.2)  # 程度副词增强20%
        
        # 归一化分数
        if word_count > 0:
            normalized_score = min(1.0, negative_score / (word_count + 2))
            return normalized_score
        
        return 0.0
    
    def classify_negative_level(self, text: str, aggressive_mode: bool = True) -> tuple:
        """
        分类负面程度

        Args:
            text: 输入文本
            aggressive_mode: 激进模式，降低阈值，宁可错杀不可放过

        Returns:
            (level, score, matched_words)
        """
        score = self.calculate_negative_score(text)
        matched_words = []

        # 找出匹配的负面词汇
        all_words = self.get_all_negative_words()
        for word in all_words:
            if word in text:
                matched_words.append(word)

        # 激进模式：大幅降低阈值
        if aggressive_mode:
            # 只要有任何负面词汇就标记为非中性
            if len(matched_words) > 0:
                # 检查是否有高优先级词汇
                high_priority_matches = [w for w in matched_words if w in self.get_high_priority_words()]

                if high_priority_matches:
                    level = "E_TERRIBLE"  # 有高优先级词汇直接判定为极差
                elif score >= 0.3:
                    level = "D_BAD"       # 分数较高判定为不良
                elif score >= 0.1:
                    level = "C_NEGATIVE"  # 有负面词汇就判定为负面
                else:
                    level = "B_SLIGHTLY_NEGATIVE"  # 轻微负面
            else:
                level = "A_NEUTRAL"
        else:
            # 保守模式：原有阈值
            if score >= 0.8:
                level = "E_TERRIBLE"
            elif score >= 0.6:
                level = "D_BAD"
            elif score >= 0.4:
                level = "C_NEGATIVE"
            elif score >= 0.2:
                level = "B_SLIGHTLY_NEGATIVE"
            else:
                level = "A_NEUTRAL"

        return level, score, matched_words

    def fine_grained_detection(self, text: str) -> dict:
        """
        细粒度负面信号检测
        专门用于高召回率检测，宁可错杀不可放过

        Args:
            text: 输入文本

        Returns:
            检测结果字典
        """
        result = {
            'is_negative': False,
            'confidence': 0.0,
            'signals': [],
            'categories': [],
            'single_chars': [],
            'double_chars': [],
            'phrases': [],
            'recommendation': 'NEUTRAL'
        }

        # 1. 检测单字负面信号
        single_negative = self.opensource_negative_words.get("单字负面词", [])
        found_singles = [char for char in single_negative if char in text]
        result['single_chars'] = found_singles

        # 2. 检测双字负面信号
        double_negative = self.opensource_negative_words.get("双字负面词", [])
        found_doubles = [word for word in double_negative if word in text]
        result['double_chars'] = found_doubles

        # 3. 检测短语负面信号
        all_phrases = []
        for category, words in self.atomic_negative_words.items():
            phrase_words = [w for w in words if len(w) >= 3]
            all_phrases.extend(phrase_words)

        found_phrases = [phrase for phrase in all_phrases if phrase in text]
        result['phrases'] = found_phrases

        # 4. 综合判断
        all_signals = found_singles + found_doubles + found_phrases
        result['signals'] = all_signals

        if found_phrases:  # 有短语信号，高置信度
            result['is_negative'] = True
            result['confidence'] = 0.9
            result['recommendation'] = 'FILTER_OUT'
        elif len(found_doubles) >= 2:  # 有2个以上双字信号
            result['is_negative'] = True
            result['confidence'] = 0.7
            result['recommendation'] = 'FILTER_OUT'
        elif len(found_doubles) >= 1:  # 有1个双字信号
            result['is_negative'] = True
            result['confidence'] = 0.5
            result['recommendation'] = 'REVIEW_NEEDED'
        elif len(found_singles) >= 3:  # 有3个以上单字信号
            result['is_negative'] = True
            result['confidence'] = 0.4
            result['recommendation'] = 'REVIEW_NEEDED'
        elif len(found_singles) >= 1:  # 有单字信号
            result['is_negative'] = True
            result['confidence'] = 0.2
            result['recommendation'] = 'POSSIBLE_NEGATIVE'

        return result

    def export_for_java(self, output_file: str = "financial_negative_words.json"):
        """导出为Java可用的JSON格式"""
        import json
        
        java_format = {
            "version": "2.2.0",  # 🎯 原子信号优化版本
            "description": "基于原子信号分析的金融负面情感词库 - 原子信号优化版",
            "high_priority_words": self.get_high_priority_words(),
            "all_negative_words": self.get_all_negative_words(),
            "categories": {
                "atomic_signals": self.atomic_negative_words,
                "opensource_words": self.opensource_negative_words
            },
            "intensity_modifiers": self.intensity_modifiers
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(java_format, f, ensure_ascii=False, indent=2)
        
        print(f"✅ Java格式词库已导出: {output_file}")
        return output_file

def main():
    """演示词库使用"""
    print("🔍 金融负面情感词库演示")
    print("="*60)
    
    # 创建词库
    lexicon = FinancialNegativeLexicon()
    
    # 测试案例 - 验证原子信号优化效果
    test_cases = [
        # 原子信号测试 - 验证"退市"能捕获所有变体
        "公司股票进入退市整理期",
        "即将退市的风险提示",
        "被强制退市处理",
        "主动申请退市",

        # 原子信号测试 - 验证"亏损"能捕获所有变体
        "净利润同比由盈转亏",
        "公司出现严重亏损",
        "预计全年亏损",
        "扣非净利润亏损",

        # 原子信号测试 - 验证"违规"能捕获所有变体
        "收到证监会立案调查通知",
        "因违规被处罚",
        "涉嫌信息披露违法",

        # 边界测试
        "业绩大幅下滑面临困难",
        "公司经营状况良好",
        "股东大会审议年度报告",
        "收入减少利润下降",
        "公司发布年报"
    ]

    print("📊 标准负面情感分析测试:")
    print(f"{'文本':<35} {'等级':<15} {'分数':<8} {'匹配词汇'}")
    print("-"*80)

    for text in test_cases:
        level, score, words = lexicon.classify_negative_level(text, aggressive_mode=False)
        words_str = ", ".join(words[:3]) + ("..." if len(words) > 3 else "")
        print(f"{text[:33]:<35} {level:<15} {score:<8.3f} {words_str}")

    print(f"\n📊 激进模式分析测试 (宁可错杀不可放过):")
    print(f"{'文本':<35} {'等级':<15} {'分数':<8} {'匹配词汇'}")
    print("-"*80)

    for text in test_cases:
        level, score, words = lexicon.classify_negative_level(text, aggressive_mode=True)
        words_str = ", ".join(words[:3]) + ("..." if len(words) > 3 else "")
        print(f"{text[:33]:<35} {level:<15} {score:<8.3f} {words_str}")

    print(f"\n🔍 细粒度检测测试:")
    print(f"{'文本':<35} {'建议':<15} {'置信度':<8} {'信号类型'}")
    print("-"*80)

    for text in test_cases:
        result = lexicon.fine_grained_detection(text)
        signal_types = []
        if result['single_chars']: signal_types.append(f"单字:{len(result['single_chars'])}")
        if result['double_chars']: signal_types.append(f"双字:{len(result['double_chars'])}")
        if result['phrases']: signal_types.append(f"短语:{len(result['phrases'])}")
        signal_str = ", ".join(signal_types) if signal_types else "无"

        print(f"{text[:33]:<35} {result['recommendation']:<15} {result['confidence']:<8.3f} {signal_str}")
    
    # 统计信息
    all_words = lexicon.get_all_negative_words()
    high_priority = lexicon.get_high_priority_words()
    
    print(f"\n📈 词库统计:")
    print(f"   总负面词汇数: {len(all_words)}")
    print(f"   高优先级词汇: {len(high_priority)}")
    print(f"   原子信号类别: {len(lexicon.atomic_negative_words)}")
    print(f"   开源词库类别: {len(lexicon.opensource_negative_words)}")
    
    # 导出Java格式
    java_file = lexicon.export_for_java()
    
    print(f"\n✅ 金融负面情感词库准备就绪！")
    print(f"📁 Java格式文件: {java_file}")

if __name__ == "__main__":
    main()
