#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV数据转换脚本
将classification_output下的CSV文件转换为词条分析器需要的LLM格式
"""

import pandas as pd
import json
import os
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CSVToLLMConverter:
    def __init__(self, csv_dir: str = "D:\\LLMData\\classification_output"):
        self.csv_dir = Path(csv_dir)
        self.output_dir = Path("converted_data")
        self.output_dir.mkdir(exist_ok=True)
        
    def load_csv_files(self):
        """加载所有CSV文件"""
        csv_files = {
            'neutral': self.csv_dir / 'definitely_neutral.csv',
            'negative': self.csv_dir / 'excluded_negative.csv',
            'positive': self.csv_dir / 'excluded_positive.csv'
        }
        
        data = {}
        for category, file_path in csv_files.items():
            if file_path.exists():
                logger.info(f"📥 加载 {category} 数据: {file_path}")
                try:
                    df = pd.read_csv(file_path, encoding='utf-8')
                    data[category] = df
                    logger.info(f"✅ 成功加载 {len(df)} 条 {category} 数据")
                except Exception as e:
                    logger.error(f"❌ 加载 {category} 数据失败: {e}")
                    # 尝试其他编码
                    try:
                        df = pd.read_csv(file_path, encoding='gbk')
                        data[category] = df
                        logger.info(f"✅ 使用GBK编码成功加载 {len(df)} 条 {category} 数据")
                    except Exception as e2:
                        logger.error(f"❌ GBK编码也失败: {e2}")
                        data[category] = pd.DataFrame()
            else:
                logger.warning(f"⚠️ 文件不存在: {file_path}")
                data[category] = pd.DataFrame()
        
        return data
    
    def convert_to_llm_format(self, data: dict):
        """转换为LLM格式"""
        llm_records = []
        
        for category, df in data.items():
            if df.empty:
                continue
                
            logger.info(f"🔄 转换 {category} 数据...")
            
            for idx, row in df.iterrows():
                try:
                    # 创建LLM格式记录
                    llm_record = {
                        'id': f"{category}_{idx}",
                        'ratingLevel': self._map_rating_level(row.get('ratingLevel', '中')),
                        'keyFactors': self._extract_key_factors(row),
                        'reasoning': self._extract_reasoning(row),
                        'title': row.get('title', ''),
                        'summary': row.get('summary', ''),
                        'sentimentScore': row.get('sentimentScore', 0.0),
                        'confidence': row.get('confidence', 0.0),
                        'source_category': category
                    }
                    
                    llm_records.append(llm_record)
                    
                except Exception as e:
                    logger.warning(f"⚠️ 转换第 {idx} 行数据失败: {e}")
                    continue
        
        logger.info(f"✅ 成功转换 {len(llm_records)} 条记录")
        return llm_records
    
    def _map_rating_level(self, level: str) -> str:
        """映射评级等级"""
        if pd.isna(level):
            return '中'
        
        level_str = str(level).strip()
        
        # 标准化评级等级
        if level_str in ['优', '好', '中', '差', '劣']:
            return level_str
        elif level_str in ['POSITIVE', 'positive']:
            return '好'
        elif level_str in ['NEGATIVE', 'negative']:
            return '差'
        elif level_str in ['NEUTRAL', 'neutral']:
            return '中'
        else:
            return '中'  # 默认中性
    
    def _extract_key_factors(self, row) -> list:
        """提取关键因素"""
        key_factors = row.get('keyFactors', '')
        
        if pd.isna(key_factors) or key_factors == '':
            return []
        
        # 处理keyFactors字段
        if isinstance(key_factors, str):
            # 按逗号分割
            factors = [factor.strip() for factor in key_factors.split(',') if factor.strip()]
            return factors
        else:
            return []
    
    def _extract_reasoning(self, row) -> str:
        """提取推理过程"""
        reasoning = row.get('reasoning', '')
        
        if pd.isna(reasoning):
            return ''
        
        # 组合多个字段作为推理内容
        reasoning_parts = []
        
        # 添加标题
        title = row.get('title', '')
        if title and not pd.isna(title):
            reasoning_parts.append(f"标题: {title}")
        
        # 添加摘要
        summary = row.get('summary', '')
        if summary and not pd.isna(summary):
            reasoning_parts.append(f"摘要: {summary}")
        
        # 添加keyFactors
        key_factors = row.get('keyFactors', '')
        if key_factors and not pd.isna(key_factors):
            reasoning_parts.append(f"关键因素: {key_factors}")
        
        # 添加事件标签
        event_tags = row.get('eventTags', '')
        if event_tags and not pd.isna(event_tags):
            reasoning_parts.append(f"事件标签: {event_tags}")
        
        return ' | '.join(reasoning_parts)
    
    def save_llm_format(self, llm_records: list, filename: str = "llm_analysis_data.json"):
        """保存为LLM格式文件"""
        output_path = self.output_dir / filename
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(llm_records, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ LLM格式数据已保存到: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"❌ 保存LLM格式数据失败: {e}")
            return None
    
    def generate_sample_data(self, llm_records: list, sample_size: int = 100):
        """生成样本数据用于测试"""
        if len(llm_records) <= sample_size:
            sample_records = llm_records
        else:
            # 按类别分层采样，保持原始比例
            sample_records = []
            categories = {}
            
            for record in llm_records:
                level = record['ratingLevel']
                if level not in categories:
                    categories[level] = []
                categories[level].append(record)
            
            # 按原始比例采样
            total_records = len(llm_records)
            for level, records in categories.items():
                original_ratio = len(records) / total_records
                target_count = int(sample_size * original_ratio)
                
                if target_count > 0:
                    if len(records) <= target_count:
                        sample_records.extend(records)
                    else:
                        # 随机采样，这里简化为取前N个
                        sample_records.extend(records[:target_count])
            
            # 如果采样数量不足，补充到目标数量
            if len(sample_records) < sample_size:
                remaining = sample_size - len(sample_records)
                # 从剩余记录中随机补充
                remaining_records = [r for r in llm_records if r not in sample_records]
                if remaining_records:
                    sample_records.extend(remaining_records[:remaining])
        
        sample_path = self.output_dir / "sample_llm_data.json"
        
        try:
            with open(sample_path, 'w', encoding='utf-8') as f:
                json.dump(sample_records, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 样本数据已保存到: {sample_path} ({len(sample_records)} 条)")
            return str(sample_path)
            
        except Exception as e:
            logger.error(f"❌ 保存样本数据失败: {e}")
            return None
    
    def analyze_data_distribution(self, llm_records: list):
        """分析数据分布"""
        logger.info("📊 数据分布分析:")
        logger.info("=" * 50)
        
        # 评级分布
        rating_counts = {}
        category_counts = {}
        
        for record in llm_records:
            rating = record['ratingLevel']
            category = record['source_category']
            
            rating_counts[rating] = rating_counts.get(rating, 0) + 1
            category_counts[category] = category_counts.get(category, 0) + 1
        
        # 显示评级分布
        logger.info("评级分布:")
        for rating, count in sorted(rating_counts.items()):
            percentage = (count * 100.0) / len(llm_records)
            logger.info(f"  {rating}: {count} ({percentage:.1f}%)")
        
        # 显示类别分布
        logger.info("\n来源类别分布:")
        for category, count in sorted(category_counts.items()):
            percentage = (count * 100.0) / len(llm_records)
            logger.info(f"  {category}: {count} ({percentage:.1f}%)")
        
        # 统计词条数量
        total_key_factors = 0
        total_reasoning_length = 0
        
        for record in llm_records:
            total_key_factors += len(record['keyFactors'])
            total_reasoning_length += len(record['reasoning'])
        
        avg_key_factors = total_key_factors / len(llm_records) if llm_records else 0
        avg_reasoning_length = total_reasoning_length / len(llm_records) if llm_records else 0
        
        logger.info(f"\n平均关键因素数量: {avg_key_factors:.1f}")
        logger.info(f"平均推理长度: {avg_reasoning_length:.1f} 字符")
        
        return {
            'rating_distribution': rating_counts,
            'category_distribution': category_counts,
            'avg_key_factors': avg_key_factors,
            'avg_reasoning_length': avg_reasoning_length
        }
    
    def run_conversion(self):
        """运行完整的转换流程"""
        logger.info("🚀 开始CSV到LLM格式转换...")
        
        # 1. 加载CSV文件
        data = self.load_csv_files()
        
        # 2. 转换为LLM格式
        llm_records = self.convert_to_llm_format(data)
        
        if not llm_records:
            logger.error("❌ 没有成功转换任何数据")
            return None
        
        # 3. 分析数据分布
        distribution = self.analyze_data_distribution(llm_records)
        
        # 4. 保存完整数据
        full_data_path = self.save_llm_format(llm_records)
        
        # 5. 生成样本数据
        sample_path = self.generate_sample_data(llm_records, 200)
        
        logger.info("🎉 转换完成！")
        logger.info(f"📁 输出目录: {self.output_dir}")
        logger.info(f"📊 完整数据: {full_data_path}")
        logger.info(f"🧪 样本数据: {sample_path}")
        
        return {
            'full_data_path': full_data_path,
            'sample_path': sample_path,
            'total_records': len(llm_records),
            'distribution': distribution
        }

def main():
    """主函数"""
    converter = CSVToLLMConverter()
    result = converter.run_conversion()
    
    if result:
        print(f"\n🎯 转换结果:")
        print(f"   总记录数: {result['total_records']}")
        print(f"   完整数据: {result['full_data_path']}")
        print(f"   样本数据: {result['sample_path']}")
        
        print(f"\n💡 下一步:")
        print(f"   1. 使用样本数据测试词条分析器")
        print(f"   2. 确认数据质量后使用完整数据")
        print(f"   3. 运行词条信息增益分析")
    else:
        print("❌ 转换失败，请检查错误信息")

if __name__ == "__main__":
    main()
