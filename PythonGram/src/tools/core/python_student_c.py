#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python学生C - 基于Java Filter系统的规则引擎
复制Java HighConfidenceNegativeFilter, HighConfidencePositiveFilter, HighConfidenceNeutralFilter的逻辑
增强版：集成抬头识别功能
"""

import re
import time
from enum import Enum
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

# 导入抬头识别器
try:
    from student_c_header_analyzer import HeaderAnalyzer
except ImportError:
    print("⚠️ 无法导入HeaderAnalyzer，将使用基础功能")
    HeaderAnalyzer = None

class FilterResult(Enum):
    """过滤结果枚举（对应Java IAnnouncementFilter.FilterResult）"""
    POSITIVE = ("正面", "明显正面公告")
    NEGATIVE = ("负面", "明显负面公告") 
    NEUTRAL = ("中性", "明确中性公告")
    UNCERTAIN = ("不确定", "需要LLM进一步分析")
    
    def __init__(self, label: str, description: str):
        self.label = label
        self.description = description
    
    def is_certain(self) -> bool:
        return self != FilterResult.UNCERTAIN
    
    def needs_llm_analysis(self) -> bool:
        return self == FilterResult.UNCERTAIN

@dataclass
class ClassificationResult:
    """分类结果（对应Java ClassificationResult）"""
    result: FilterResult
    filter_name: str
    confidence: float
    processing_time_ms: int
    # 新增：抬头识别信息
    stock_code: str = ""
    stock_name: str = ""
    company_name: str = ""
    announcement_title: str = ""
    header_confidence: float = 0.0

class PythonStudentC:
    """Python学生C - 规则应用引擎"""
    
    def __init__(self):
        self.filters = []
        self._init_filters()
        # 初始化抬头识别器
        self.header_analyzer = HeaderAnalyzer() if HeaderAnalyzer else None
        
    def _init_filters(self):
        """初始化过滤器（按优先级排序）"""
        self.filters = [
            HighConfidenceNegativeFilter(),  # 优先级1
            HighConfidencePositiveFilter(),  # 优先级2  
            HighConfidenceNeutralFilter()    # 优先级3
        ]
        self.filters.sort(key=lambda f: f.get_priority())
    
    def _extract_header_info(self, content: str) -> Dict:
        """提取公告抬头信息"""
        if not content:
            return {
                'stock_code': '',
                'stock_name': '',
                'company_name': '',
                'announcement_title': '',
                'header_confidence': 0.0
            }
        
        # 检查抬头识别器是否可用
        if not self.header_analyzer:
            return {
                'stock_code': '',
                'stock_name': '',
                'company_name': '',
                'announcement_title': '',
                'header_confidence': 0.0
            }
        
        try:
            header_info = self.header_analyzer.analyze_header(content)
            return {
                'stock_code': header_info.stock_code,
                'stock_name': header_info.stock_name,
                'company_name': header_info.company_full_name,
                'announcement_title': header_info.announcement_title,
                'header_confidence': header_info.confidence
            }
        except Exception as e:
            print(f"⚠️ 抬头识别失败: {e}")
            return {
                'stock_code': '',
                'stock_name': '',
                'company_name': '',
                'announcement_title': '',
                'header_confidence': 0.0
            }
    
    def classify(self, title: str, content: str = "") -> ClassificationResult:
        """分类公告（对应Java PreClassifier.classify）"""
        start_time = time.time()
        
        # 提取抬头信息
        header_info = self._extract_header_info(content)
        
        # 尝试分类
        for filter_obj in self.filters:
            result = filter_obj.classify(title, content)
            
            if result != FilterResult.UNCERTAIN:
                confidence = filter_obj.get_confidence(title, content)
                processing_time = int((time.time() - start_time) * 1000)
                
                return ClassificationResult(
                    result=result,
                    filter_name=filter_obj.get_filter_name(),
                    confidence=confidence,
                    processing_time_ms=processing_time,
                    stock_code=header_info['stock_code'],
                    stock_name=header_info['stock_name'],
                    company_name=header_info['company_name'],
                    announcement_title=header_info['announcement_title'],
                    header_confidence=header_info['header_confidence']
                )
        
        # 所有过滤器都无法确定
        processing_time = int((time.time() - start_time) * 1000)
        return ClassificationResult(
            result=FilterResult.UNCERTAIN,
            filter_name="NoFilter",
            confidence=0.0,
            processing_time_ms=processing_time,
            stock_code=header_info['stock_code'],
            stock_name=header_info['stock_name'],
            company_name=header_info['company_name'],
            announcement_title=header_info['announcement_title'],
            header_confidence=header_info['header_confidence']
        )
    
    def analyze_llm_json(self, json_data: Dict) -> Dict:
        """分析LLM API返回的JSON数据，并添加PythonStudentC的决策逻辑"""
        start_time = time.time()
        
        try:
            # 提取基本信息
            title = json_data.get('title', '')
            content = json_data.get('content', '')
            summary = json_data.get('summary', '')
            
            # 🔥 如果没有content，尝试从PDF文件读取
            if not content:
                content = self._extract_content_from_pdf(json_data)
            
            # 如果仍然没有content，尝试从其他字段获取
            if not content:
                content = json_data.get('text', '') or json_data.get('announcement', '')
            
            # 使用学生C进行分类
            classification_result = self.classify(title, content)
            
            # 构建精简的决策逻辑数据结构
            decision_logic = {
                "PythonStudentC": {
                    "v1": {
                        "decision": classification_result.result.name,
                        "confidence": classification_result.confidence,
                        "stock_code": classification_result.stock_code,
                        "stock_name": classification_result.stock_name,
                        "announcement_title": classification_result.announcement_title,
                        "analysis_timestamp": time.strftime('%Y-%m-%d %H:%M:%S')
                    }
                }
            }
            
            # 如果原JSON中已有decisionLogic，则合并
            if 'decisionLogic' in json_data:
                json_data['decisionLogic'].update(decision_logic)
            else:
                json_data['decisionLogic'] = decision_logic
            
            # 添加处理时间信息
            total_processing_time = int((time.time() - start_time) * 1000)
            json_data['pythonStudentCProcessingTime'] = total_processing_time
            
            print(f"✅ PythonStudentC分析完成，处理时间: {total_processing_time}ms")
            
            return json_data
            
        except Exception as e:
            print(f"❌ PythonStudentC分析失败: {e}")
            # 返回原始数据，添加错误信息
            if 'decisionLogic' not in json_data:
                json_data['decisionLogic'] = {}
            
            json_data['decisionLogic']['PythonStudentC'] = {
                "v1": {
                    "decision": "ERROR",
                    "confidence": 0.0,
                    "stock_code": "",
                    "stock_name": "",
                    "announcement_title": "",
                    "analysis_timestamp": time.strftime('%Y-%m-%d %H:%M:%S')
                }
            }
            
            return json_data
    
    def _extract_content_from_pdf(self, json_data: Dict) -> str:
        """从PDF文件提取内容"""
        try:
            # 尝试从sourceFile.pdfPath获取PDF路径
            source_file = json_data.get('sourceFile', {})
            pdf_path = source_file.get('pdfPath', '')
            
            if not pdf_path:
                print("⚠️ 未找到PDF路径")
                return ""
            
            # 尝试读取PDF内容
            pdf_content = self._read_pdf_content(pdf_path)
            
            if pdf_content:
                print(f"✅ 成功从PDF读取内容，长度: {len(pdf_content)} 字符")
                return pdf_content
            else:
                print("⚠️ PDF内容读取失败")
                return ""
                
        except Exception as e:
            print(f"⚠️ PDF内容提取失败: {e}")
            return ""
    
    def _read_pdf_content(self, pdf_path: str) -> str:
        """读取PDF文件内容"""
        try:
            # 🔥 路径转换：将网络路径转换为本地路径
            local_pdf_path = self._convert_network_path_to_local(pdf_path)
            print(f"🌐 网络路径: {pdf_path}")
            print(f"✅ 映射到本地路径: {local_pdf_path}")
            
            # 尝试使用PyPDF2
            try:
                import PyPDF2
                with open(local_pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    text = ""
                    for page in pdf_reader.pages:
                        text += page.extract_text() + "\n"
                    
                    # 🔥 添加调试信息
                    print(f"📖 PyPDF2读取成功")
                    print(f"📊 读取内容长度: {len(text)} 字符")
                    print(f"📄 内容预览: {text[:200]}...")
                    
                    return text.strip()
            except ImportError:
                print("⚠️ PyPDF2未安装，尝试其他方法")
            
            # 尝试使用pdfminer
            try:
                from pdfminer.high_level import extract_text
                text = extract_text(local_pdf_path)
                
                # 🔥 添加调试信息
                print(f"📖 pdfminer读取成功")
                print(f"📊 读取内容长度: {len(text)} 字符")
                print(f"📄 内容预览: {text[:200]}...")
                
                return text.strip()
            except ImportError:
                print("⚠️ pdfminer未安装")
            
            # 尝试使用pypdf
            try:
                import pypdf
                with open(local_pdf_path, 'rb') as file:
                    pdf_reader = pypdf.PdfReader(file)
                    text = ""
                    for page in pdf_reader.pages:
                        text += page.extract_text() + "\n"
                    
                    # 🔥 添加调试信息
                    print(f"📖 pypdf读取成功")
                    print(f"📊 读取内容长度: {len(text)} 字符")
                    print(f"📄 内容预览: {text[:200]}...")
                    
                    return text.strip()
            except ImportError:
                print("⚠️ pypdf未安装")
            
            print("❌ 所有PDF读取库都不可用")
            return ""
            
        except Exception as e:
            print(f"❌ PDF读取失败: {e}")
            return ""
    
    def _convert_network_path_to_local(self, network_path: str) -> str:
        """将网络路径转换为本地路径"""
        try:
            # 检查是否是网络路径
            if network_path.startswith('\\\\'):
                # 网络路径格式：\\server\share\path\to\file
                # 转换为本地路径：F:\path\to\file
                
                # 移除开头的双反斜杠
                path_without_prefix = network_path[2:]
                
                # 分割路径部分
                parts = path_without_prefix.split('\\')
                
                if len(parts) >= 3:
                    # 假设第一个部分是服务器名，第二个部分是共享名
                    # 我们直接映射到F盘
                    drive_letter = "F:"
                    file_path = "\\".join(parts[2:])  # 跳过服务器名和共享名
                    
                    local_path = f"{drive_letter}\\{file_path}"
                    return local_path
                else:
                    print(f"⚠️ 网络路径格式异常: {network_path}")
                    return network_path
            else:
                # 已经是本地路径，直接返回
                return network_path
                
        except Exception as e:
            print(f"⚠️ 路径转换失败: {e}")
            return network_path
    
    def analyze_llm_json_file(self, json_file_path: str, in_place: bool = True) -> bool:
        """分析LLM JSON文件并保存结果"""
        try:
            import json
            import os
            
            # 读取JSON文件
            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # 分析数据
            result_data = self.analyze_llm_json(json_data)
            
            # 原地覆盖保存
            with open(json_file_path, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 分析结果已原地覆盖保存: {os.path.basename(json_file_path)}")
            return True
            
        except Exception as e:
            print(f"❌ 文件分析失败: {e}")
            return False

class BaseFilter:
    """基础过滤器类"""
    
    def __init__(self):
        self.patterns = []
        self._init_patterns()
    
    def _init_patterns(self):
        """初始化正则表达式模式"""
        pass
    
    def classify(self, title: str, content: str) -> FilterResult:
        """分类方法"""
        return FilterResult.UNCERTAIN
    
    def get_confidence(self, title: str, content: str) -> float:
        """获取置信度"""
        return 0.0
    
    def get_filter_name(self) -> str:
        """获取过滤器名称"""
        return self.__class__.__name__
    
    def get_priority(self) -> int:
        """获取优先级"""
        return 999

class HighConfidenceNegativeFilter(BaseFilter):
    """高置信度负面过滤器（对应Java版本）"""
    
    def _init_patterns(self):
        """基于Java版本的负面模式"""
        negative_patterns = [
            r"警示函",
            r"业绩预亏",
            r"亏损",
            r"违规",
            r"处罚",
            r"风险提示",
            r"停牌",
            r"退市",
            r"诉讼",
            r"仲裁"
        ]
        self.patterns = [re.compile(pattern, re.IGNORECASE) for pattern in negative_patterns]
    
    def classify(self, title: str, content: str) -> FilterResult:
        full_text = f"{title} {content}"
        
        for pattern in self.patterns:
            if pattern.search(full_text):
                return FilterResult.NEGATIVE
        
        return FilterResult.UNCERTAIN
    
    def get_confidence(self, title: str, content: str) -> float:
        full_text = f"{title} {content}"
        match_count = sum(1 for pattern in self.patterns if pattern.search(full_text))
        
        if match_count == 0:
            return 0.0
        
        # 基础置信度0.95，每多一个匹配增加0.02，最高0.99
        base_confidence = 0.95
        additional_confidence = min(match_count * 0.02, 0.04)
        return min(base_confidence + additional_confidence, 0.99)
    
    def get_priority(self) -> int:
        return 1

class HighConfidencePositiveFilter(BaseFilter):
    """高置信度正面过滤器（对应Java版本）"""
    
    def _init_patterns(self):
        """基于Java版本的正面模式"""
        positive_patterns = [
            r"重大合同",
            r"技术突破",
            r"业绩增长",
            r"盈利",
            r"分红",
            r"收购",
            r"合作协议",
            r"新产品发布"
        ]
        
        # 负面检查模式
        negative_check_patterns = [
            r"亏损",
            r"违规",
            r"风险",
            r"警示"
        ]
        
        self.patterns = [re.compile(pattern, re.IGNORECASE) for pattern in positive_patterns]
        self.negative_check_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in negative_check_patterns]
    
    def classify(self, title: str, content: str) -> FilterResult:
        full_text = f"{title} {content}"
        
        # 首先检查是否包含负面词汇
        for pattern in self.negative_check_patterns:
            if pattern.search(full_text):
                return FilterResult.UNCERTAIN
        
        # 检查正面模式
        for pattern in self.patterns:
            if pattern.search(full_text):
                return FilterResult.POSITIVE
        
        return FilterResult.UNCERTAIN
    
    def get_confidence(self, title: str, content: str) -> float:
        full_text = f"{title} {content}"
        match_count = sum(1 for pattern in self.patterns if pattern.search(full_text))
        
        if match_count == 0:
            return 0.0
        
        # 基础置信度0.90，每多一个匹配增加0.02，最高0.98
        base_confidence = 0.90
        additional_confidence = min(match_count * 0.02, 0.08)
        return min(base_confidence + additional_confidence, 0.98)
    
    def get_priority(self) -> int:
        return 2

class HighConfidenceNeutralFilter(BaseFilter):
    """高置信度中性过滤器（对应Java版本）"""
    
    def _init_patterns(self):
        """基于Java版本的中性模式"""
        neutral_patterns = [
            r"^.*股东大会.*$",
            r"^.*工商变更.*$",
            r"^.*董事会决议.*$",
            r"^.*监事会决议.*$",
            r"^.*年度报告.*$",
            r"^.*季度报告.*$"
        ]
        
        negative_check_patterns = [
            r"亏损",
            r"违规",
            r"风险",
            r"警示"
        ]
        
        self.patterns = [re.compile(pattern, re.IGNORECASE) for pattern in neutral_patterns]
        self.negative_check_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in negative_check_patterns]
    
    def classify(self, title: str, content: str) -> FilterResult:
        full_text = f"{title} {content}"
        
        # 首先检查是否包含负面词汇
        for pattern in self.negative_check_patterns:
            if pattern.search(full_text):
                return FilterResult.UNCERTAIN
        
        # 检查中性模式（要求完全匹配标题）
        for pattern in self.patterns:
            if pattern.match(title):
                return FilterResult.NEUTRAL
        
        return FilterResult.UNCERTAIN
    
    def get_confidence(self, title: str, content: str) -> float:
        # 检查是否有完全匹配的模式
        for pattern in self.patterns:
            if pattern.match(title):
                return 0.99  # 完全匹配，返回最高置信度
        
        return 0.0
    
    def get_priority(self) -> int:
        return 3