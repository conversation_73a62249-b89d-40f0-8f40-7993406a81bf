#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python老师学习系统 V2
从LLM分析结果中学习规则模式，支持师生协同学习
"""

import json
import logging
import pandas as pd
from pathlib import Path
from collections import Counter, defaultdict
from typing import Dict, List, Optional
import re

# 导入新的核心模块
try:
    from negative_purity_analyzer import NegativePurityAnalyzer
except ImportError:
    # 这是一个兜底，理想情况下不应该发生
    NegativePurityAnalyzer = None

logger = logging.getLogger(__name__)

class PythonTeacherLearningV2:
    """Python老师学习系统V2 - 从LLM结果中学习规则模式"""
    
    def __init__(self, data_dir: str, output_dir: str):
        """
        初始化Python老师学习系统
        
        Args:
            data_dir: LLM分析结果目录
            output_dir: 输出目录
        """
        self.data_dir = Path(data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 评级映射
        self.rating_mapping = {
            '优': 'POSITIVE',
            '好': 'POSITIVE', 
            '中': 'NEUTRAL',
            '差': 'NEGATIVE',
            '劣': 'NEGATIVE'
        }
        
        # 存储JSON文件列表（用于外部指定）
        self.json_files = []
        
        logger.info(f"🧠 Python老师学习系统V2初始化完成")
        logger.info(f"📁 数据目录: {self.data_dir}")
        logger.info(f"📁 输出目录: {self.output_dir}")
    
    def load_llm_data(self) -> List[Dict]:
        """加载LLM分析数据"""
        if self.json_files:
            # 使用外部指定的文件列表
            json_files = [Path(f) for f in self.json_files]
        else:
            # 扫描数据目录
            json_files = list(self.data_dir.glob("*.json"))
        
        logger.info(f"📄 找到 {len(json_files)} 个JSON文件")
        
        llm_data = []
        failed_count = 0
        
        for file_path in json_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 检查必要字段
                if 'ratingLevel' in data and data.get('ratingLevel'):
                    # 从content中提取标题（如果没有title字段）
                    title = data.get('title', '')
                    if not title:
                        content = data.get('content', '')
                        title = content[:100] if content else data.get('fileName', '')
                    
                    llm_data.append({
                        'file_name': file_path.name,
                        'title': title,
                        'content': data.get('content', ''),
                        'rating': data.get('ratingLevel', ''),
                        'summary': data.get('summary', ''),
                        'keyFactors': data.get('keyFactors', []),
                        'riskFactors': data.get('riskFactors', []),
                        'eventTags': data.get('eventTags', []),
                        'reasoning': data.get('reasoning', '')
                    })
                
            except Exception as e:
                failed_count += 1
                logger.debug(f"加载文件失败 {file_path}: {e}")
        
        logger.info(f"✅ 成功加载 {len(llm_data)} 条数据，失败 {failed_count} 条")
        return llm_data
    
    def analyze_by_rating(self, llm_data: List[Dict]) -> Dict:
        """按评级分析数据"""
        rating_analysis = defaultdict(list)
        
        for item in llm_data:
            rating = item['rating']
            if rating in self.rating_mapping:
                mapped_rating = self.rating_mapping[rating]
                rating_analysis[mapped_rating].append(item)
        
        logger.info(f"📊 评级分布:")
        for rating, items in rating_analysis.items():
            logger.info(f"  {rating}: {len(items)} 个样本")
        
        return dict(rating_analysis)
    
    def run_complete_analysis(self) -> pd.DataFrame:
        """运行完整的分析流程 - 集成负面纯度分析"""
        logger.info("🚀 开始完整的Python老师学习分析...")

        # 1. 加载LLM数据
        llm_data = self.load_llm_data()
        if not llm_data:
            logger.error("❌ 没有可用的LLM数据")
            return pd.DataFrame()

        # 2. 按评级分析
        rating_analysis = self.analyze_by_rating(llm_data)

        # 3. 检查并使用负面纯度分析器
        if NegativePurityAnalyzer:
            logger.info("🎯 使用负面纯度分析器...")
            purity_analyzer = NegativePurityAnalyzer()
            purity_df = purity_analyzer.analyze_word_purity(rating_analysis)

            if not purity_df.empty:
                # 获取高纯度关键词
                high_purity_keywords = purity_analyzer.get_high_purity_keywords(
                    purity_df,
                    purity_threshold=0.7,  # 70%以上的纯度
                    discrimination_threshold=0.4,  # 40%以上的区分度
                    min_docs=3  # 至少出现在3个文档中
                )

                # 保存纯度分析结果
                purity_output = self.output_dir / "negative_purity_analysis.csv"
                purity_df.to_csv(purity_output, index=False, encoding='utf-8-sig')
                logger.info(f"💾 纯度分析结果已保存: {purity_output}")

                # 保存高纯度关键词
                if not high_purity_keywords.empty:
                    high_purity_output = self.output_dir / "high_purity_negative_keywords.csv"
                    high_purity_keywords.to_csv(high_purity_output, index=False, encoding='utf-8-sig')
                    logger.info(f"💾 高纯度关键词已保存: {high_purity_output}")

                # 生成报告
                purity_analyzer.generate_purity_report(purity_df)

                # 直接使用纯度分析的结果作为最终评分
                word_scores_df = purity_df
        else:
            logger.error("❌ 负面纯度分析器模块未加载，无法进行分析。")
            word_scores_df = pd.DataFrame()

        # 4. 保存结果
        if not word_scores_df.empty:
            output_file = self.output_dir / "word_scores_analysis.csv"
            word_scores_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            logger.info(f"💾 最终分析结果已保存: {output_file}")

            # 显示前10个高质量负面词汇
            logger.info("🔍 前10个高质量负面词汇:")
            logger.info("\n" + word_scores_df.head(10).to_string())

        logger.info("✅ Python老师学习分析完成")
        return word_scores_df

def test_python_teacher_learning():
    """测试Python老师学习系统"""
    print("🧪 测试Python老师学习系统V2")
    print("="*50)
    
    # 创建老师
    teacher = PythonTeacherLearningV2(
        data_dir="E:/LLMData/analysis_output",
        output_dir="./teacher_output"
    )
    
    # 运行分析
    word_scores_df = teacher.run_complete_analysis()
    
    if not word_scores_df.empty:
        print(f"\n📊 分析结果:")
        print(f"  总词汇数: {len(word_scores_df)}")
        print(f"  前5个负面词汇:")
        for _, row in word_scores_df.head(5).iterrows():
            print(f"    {row['word']}: {row['negative_score']:.3f}")
    else:
        print("❌ 分析失败或无数据")

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    test_python_teacher_learning()
