#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
规则生成器
从Python老师的学习结果中生成分类规则
"""

import json
import logging
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional
import re

logger = logging.getLogger(__name__)

class RuleGenerator:
    """规则生成器 - 从词汇评分中生成分类规则"""
    
    def __init__(self, word_scores_df: pd.DataFrame):
        """
        初始化规则生成器
        
        Args:
            word_scores_df: 词汇评分DataFrame
        """
        self.word_scores_df = word_scores_df
        self.generated_rules = []
        
        logger.info(f"🔧 规则生成器初始化完成")
        if word_scores_df is not None and not word_scores_df.empty:
            logger.info(f"📊 输入词汇数量: {len(word_scores_df)}")
        else:
            logger.warning("⚠️ 输入的词汇评分数据为空")
    
    def generate_negative_rules(self, top_n: int = 100,
                              min_negative_score: float = 0.6,
                              min_total_count: int = 3,
                              force_core_keywords: bool = True,
                              use_purity_scoring: bool = True) -> List[Dict]:
        """
        生成负面分类规则 - 支持负面纯度评分

        Args:
            top_n: 生成规则的最大数量
            min_negative_score: 最小负面评分阈值
            min_total_count: 最小出现次数
            force_core_keywords: 是否强制包含核心关键词
            use_purity_scoring: 是否使用纯度评分

        Returns:
            生成的规则列表
        """
        logger.info(f"🎯 开始生成负面规则 (top_n={top_n}, min_score={min_negative_score}, 纯度评分={use_purity_scoring})")

        if self.word_scores_df is None or self.word_scores_df.empty:
            logger.warning("⚠️ 没有词汇数据，无法生成规则")
            return []

        # 检查是否有纯度数据
        has_purity_data = 'negative_purity' in self.word_scores_df.columns
        if use_purity_scoring and has_purity_data:
            logger.info("✅ 使用负面纯度评分生成规则")
            return self._generate_rules_with_purity(top_n, min_negative_score, min_total_count, force_core_keywords)
        else:
            logger.info("📊 使用传统评分生成规则")
            return self._generate_rules_traditional(top_n, min_negative_score, min_total_count, force_core_keywords)

        # 核心负面关键词（必须包含的）
        core_negative_keywords = [
            '亏损', '违规', '违法', '处罚', '警示', '风险', '退市', '停牌', '破产',
            '立案', '调查', '诉讼', '减持', '下降', '下滑', '暂停', '终止'
        ]

        rules = []

        # 1. 强制包含核心关键词（如果在数据中找到）
        if force_core_keywords:
            for keyword in core_negative_keywords:
                matches = self.word_scores_df[self.word_scores_df['word'] == keyword]
                if not matches.empty:
                    row = matches.iloc[0]
                    # 对核心关键词降低阈值要求
                    if row['negative_score'] >= 0.3 and row['total_count'] >= 2:
                        rule = {
                            'id': f"core_negative_rule_{len(rules) + 1}",
                            'pattern': f".*{re.escape(keyword)}.*",
                            'type': 'negative',
                            'confidence': max(0.7, row['negative_score']),  # 核心词汇最低0.7置信度
                            'word': keyword,
                            'total_count': int(row['total_count']),
                            'negative_count': int(row['negative_count']),
                            'description': f"核心负面关键词: {keyword}",
                            'priority': 'very_high',
                            'is_core_keyword': True
                        }
                        rules.append(rule)
                        logger.info(f"  ✅ 添加核心关键词规则: {keyword} (评分: {row['negative_score']:.3f})")

        # 2. 筛选其他高质量的负面词汇
        remaining_slots = top_n - len(rules)
        if remaining_slots > 0:
            # 排除已经添加的核心关键词
            added_words = {rule['word'] for rule in rules}

            negative_words = self.word_scores_df[
                (self.word_scores_df['negative_score'] >= min_negative_score) &
                (self.word_scores_df['total_count'] >= min_total_count) &
                (~self.word_scores_df['word'].isin(added_words))
            ].head(remaining_slots)
        
            for _, row in negative_words.iterrows():
                word = row['word']
                negative_score = row['negative_score']
                total_count = row['total_count']

                # 生成基本的包含规则
                rule = {
                    'id': f"negative_rule_{len(rules) + 1}",
                    'pattern': f".*{re.escape(word)}.*",
                    'type': 'negative',
                    'confidence': negative_score,
                    'word': word,
                    'total_count': int(total_count),
                    'negative_count': int(row['negative_count']),
                    'description': f"包含负面关键词: {word}",
                    'priority': 'high' if negative_score >= 0.8 else 'medium',
                    'is_core_keyword': False
                }

                rules.append(rule)
        
        # 生成组合规则（高置信度词汇的组合）
        high_confidence_words = negative_words[negative_words['negative_score'] >= 0.8]['word'].tolist()
        
        if len(high_confidence_words) >= 2:
            # 生成一些二元组合规则
            for i in range(min(5, len(high_confidence_words) - 1)):
                word1 = high_confidence_words[i]
                word2 = high_confidence_words[i + 1]
                
                combo_rule = {
                    'id': f"negative_combo_rule_{i + 1}",
                    'pattern': f".*{re.escape(word1)}.*{re.escape(word2)}.*",
                    'type': 'negative',
                    'confidence': 0.95,  # 组合规则置信度更高
                    'words': [word1, word2],
                    'description': f"包含负面词汇组合: {word1} + {word2}",
                    'priority': 'very_high'
                }
                
                rules.append(combo_rule)
        
        self.generated_rules = rules
        
        logger.info(f"✅ 生成了 {len(rules)} 条负面规则")
        logger.info(f"   - 单词规则: {len([r for r in rules if 'word' in r])}")
        logger.info(f"   - 组合规则: {len([r for r in rules if 'words' in r])}")
        
        return rules
    
    def generate_positive_rules(self, top_n: int = 50,
                              min_positive_score: float = 0.7,
                              min_total_count: int = 3) -> List[Dict]:
        """
        生成正面分类规则
        
        Args:
            top_n: 生成规则的最大数量
            min_positive_score: 最小正面评分阈值
            min_total_count: 最小出现次数
            
        Returns:
            生成的规则列表
        """
        logger.info(f"🎯 开始生成正面规则 (top_n={top_n}, min_score={min_positive_score})")
        
        if self.word_scores_df is None or self.word_scores_df.empty:
            logger.warning("⚠️ 没有词汇数据，无法生成规则")
            return []
        
        # 筛选高质量的正面词汇
        positive_words = self.word_scores_df[
            (self.word_scores_df['positive_score'] >= min_positive_score) &
            (self.word_scores_df['total_count'] >= min_total_count)
        ].sort_values('positive_score', ascending=False).head(top_n)
        
        rules = []
        
        for _, row in positive_words.iterrows():
            word = row['word']
            positive_score = row['positive_score']
            total_count = row['total_count']
            
            rule = {
                'id': f"positive_rule_{len(rules) + 1}",
                'pattern': f".*{re.escape(word)}.*",
                'type': 'positive',
                'confidence': positive_score,
                'word': word,
                'total_count': int(total_count),
                'positive_count': int(row['positive_count']),
                'description': f"包含正面关键词: {word}",
                'priority': 'high' if positive_score >= 0.8 else 'medium'
            }
            
            rules.append(rule)
        
        logger.info(f"✅ 生成了 {len(rules)} 条正面规则")
        return rules
    
    def generate_neutral_rules(self, top_n: int = 30,
                             min_neutral_score: float = 0.8,
                             min_total_count: int = 5) -> List[Dict]:
        """
        生成中性分类规则（高置信度）
        
        Args:
            top_n: 生成规则的最大数量
            min_neutral_score: 最小中性评分阈值
            min_total_count: 最小出现次数
            
        Returns:
            生成的规则列表
        """
        logger.info(f"🎯 开始生成中性规则 (top_n={top_n}, min_score={min_neutral_score})")
        
        if self.word_scores_df is None or self.word_scores_df.empty:
            logger.warning("⚠️ 没有词汇数据，无法生成规则")
            return []
        
        # 筛选高质量的中性词汇
        neutral_words = self.word_scores_df[
            (self.word_scores_df['neutral_score'] >= min_neutral_score) &
            (self.word_scores_df['total_count'] >= min_total_count) &
            (self.word_scores_df['negative_score'] <= 0.2)  # 确保不是负面词汇
        ].sort_values('neutral_score', ascending=False).head(top_n)
        
        rules = []
        
        for _, row in neutral_words.iterrows():
            word = row['word']
            neutral_score = row['neutral_score']
            total_count = row['total_count']
            
            rule = {
                'id': f"neutral_rule_{len(rules) + 1}",
                'pattern': f".*{re.escape(word)}.*",
                'type': 'neutral',
                'confidence': neutral_score,
                'word': word,
                'total_count': int(total_count),
                'neutral_count': int(row['neutral_count']),
                'description': f"包含中性关键词: {word}",
                'priority': 'high' if neutral_score >= 0.9 else 'medium'
            }
            
            rules.append(rule)
        
        logger.info(f"✅ 生成了 {len(rules)} 条中性规则")
        return rules
    
    def save_rules_to_file(self, rules: List[Dict], file_path: str):
        """
        保存规则到文件
        
        Args:
            rules: 规则列表
            file_path: 保存路径
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(rules, f, ensure_ascii=False, indent=2)
            
            logger.info(f"💾 规则已保存到: {file_path}")
            logger.info(f"📊 保存了 {len(rules)} 条规则")
            
        except Exception as e:
            logger.error(f"❌ 保存规则失败: {e}")
            raise
    
    def generate_all_rules(self, output_dir: str = "./rules_output") -> Dict[str, List[Dict]]:
        """
        生成所有类型的规则
        
        Args:
            output_dir: 输出目录
            
        Returns:
            所有生成的规则
        """
        logger.info("🚀 开始生成所有类型的规则...")
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        all_rules = {
            'negative': self.generate_negative_rules(),
            'positive': self.generate_positive_rules(),
            'neutral': self.generate_neutral_rules()
        }
        
        # 保存各类型规则
        for rule_type, rules in all_rules.items():
            if rules:
                file_path = output_path / f"{rule_type}_rules.json"
                self.save_rules_to_file(rules, str(file_path))
        
        # 保存合并的规则文件
        combined_rules = []
        for rules in all_rules.values():
            combined_rules.extend(rules)
        
        if combined_rules:
            combined_path = output_path / "all_rules.json"
            self.save_rules_to_file(combined_rules, str(combined_path))
        
        logger.info(f"✅ 所有规则生成完成，共 {len(combined_rules)} 条规则")
        return all_rules

    def _generate_rules_with_purity(self, top_n: int, min_negative_score: float,
                                   min_total_count: int, force_core_keywords: bool) -> List[Dict]:
        """使用负面纯度生成规则"""
        rules = []

        # 1. 优先选择高纯度词汇
        high_purity_words = self.word_scores_df[
            (self.word_scores_df['negative_purity'] >= 0.8) &  # 80%以上纯度
            (self.word_scores_df['discrimination'] >= 0.5) &   # 50%以上区分度
            (self.word_scores_df['total_count'] >= min_total_count)
        ].head(top_n // 2)  # 取一半名额

        for _, row in high_purity_words.iterrows():
            rule = {
                'id': f"high_purity_rule_{len(rules) + 1}",
                'pattern': f".*{re.escape(row['word'])}.*",
                'type': 'negative',
                'confidence': min(0.95, row['negative_purity'] + 0.1),  # 高纯度词汇给予更高置信度
                'word': row['word'],
                'total_count': int(row['total_count']),
                'negative_count': int(row['negative_count']),
                'description': f"高纯度负面关键词: {row['word']} (纯度: {row['negative_purity']:.3f})",
                'priority': 'very_high',
                'is_core_keyword': row.get('matches_user_pattern', False),
                'purity_score': row['negative_purity'],
                'discrimination_score': row['discrimination']
            }
            rules.append(rule)

        # 2. 选择中等纯度但高频的词汇
        medium_purity_words = self.word_scores_df[
            (self.word_scores_df['negative_purity'] >= 0.6) &
            (self.word_scores_df['negative_purity'] < 0.8) &
            (self.word_scores_df['total_count'] >= min_total_count * 2) &  # 要求更高的频次
            (~self.word_scores_df['word'].isin([r['word'] for r in rules]))
        ].head(top_n // 3)  # 取三分之一名额

        for _, row in medium_purity_words.iterrows():
            rule = {
                'id': f"medium_purity_rule_{len(rules) + 1}",
                'pattern': f".*{re.escape(row['word'])}.*",
                'type': 'negative',
                'confidence': row['negative_purity'],
                'word': row['word'],
                'total_count': int(row['total_count']),
                'negative_count': int(row['negative_count']),
                'description': f"中等纯度负面关键词: {row['word']} (纯度: {row['negative_purity']:.3f})",
                'priority': 'high',
                'is_core_keyword': row.get('matches_user_pattern', False),
                'purity_score': row['negative_purity'],
                'discrimination_score': row['discrimination']
            }
            rules.append(rule)

        # 3. 强制包含用户模式匹配的词汇
        if force_core_keywords:
            user_pattern_words = self.word_scores_df[
                (self.word_scores_df.get('matches_user_pattern', False) == True) &
                (~self.word_scores_df['word'].isin([r['word'] for r in rules]))
            ]

            for _, row in user_pattern_words.iterrows():
                rule = {
                    'id': f"user_pattern_rule_{len(rules) + 1}",
                    'pattern': f".*{re.escape(row['word'])}.*",
                    'type': 'negative',
                    'confidence': max(0.8, row.get('negative_purity', 0.8)),  # 用户模式至少0.8置信度
                    'word': row['word'],
                    'total_count': int(row['total_count']),
                    'negative_count': int(row['negative_count']),
                    'description': f"用户剪裁模式: {row['word']}",
                    'priority': 'very_high',
                    'is_core_keyword': True,
                    'purity_score': row.get('negative_purity', 0.8),
                    'discrimination_score': row.get('discrimination', 0.8)
                }
                rules.append(rule)

        logger.info(f"✅ 基于纯度生成了 {len(rules)} 条负面规则")
        logger.info(f"   高纯度规则: {len([r for r in rules if 'high_purity' in r['id']])}")
        logger.info(f"   中等纯度规则: {len([r for r in rules if 'medium_purity' in r['id']])}")
        logger.info(f"   用户模式规则: {len([r for r in rules if 'user_pattern' in r['id']])}")

        return rules

    def _generate_rules_traditional(self, top_n: int, min_negative_score: float,
                                  min_total_count: int, force_core_keywords: bool) -> List[Dict]:
        """传统方法生成规则（兼容性）"""
        # 这里保持原有的逻辑
        rules = []

        # 核心负面关键词（必须包含的）
        core_negative_keywords = [
            '亏损', '违规', '违法', '处罚', '警示', '风险', '退市', '停牌', '破产',
            '立案', '调查', '诉讼', '减持', '下降', '下滑', '暂停', '终止'
        ]

        # 1. 强制包含核心关键词（如果在数据中找到）
        if force_core_keywords:
            for keyword in core_negative_keywords:
                matches = self.word_scores_df[self.word_scores_df['word'] == keyword]
                if not matches.empty:
                    row = matches.iloc[0]
                    # 对核心关键词降低阈值要求
                    if row['negative_score'] >= 0.3 and row['total_count'] >= 2:
                        rule = {
                            'id': f"core_negative_rule_{len(rules) + 1}",
                            'pattern': f".*{re.escape(keyword)}.*",
                            'type': 'negative',
                            'confidence': max(0.7, row['negative_score']),  # 核心词汇最低0.7置信度
                            'word': keyword,
                            'total_count': int(row['total_count']),
                            'negative_count': int(row['negative_count']),
                            'description': f"核心负面关键词: {keyword}",
                            'priority': 'very_high',
                            'is_core_keyword': True
                        }
                        rules.append(rule)

        # 2. 筛选其他高质量的负面词汇
        remaining_slots = top_n - len(rules)
        if remaining_slots > 0:
            # 排除已经添加的核心关键词
            added_words = {rule['word'] for rule in rules}

            negative_words = self.word_scores_df[
                (self.word_scores_df['negative_score'] >= min_negative_score) &
                (self.word_scores_df['total_count'] >= min_total_count) &
                (~self.word_scores_df['word'].isin(added_words))
            ].head(remaining_slots)

            for _, row in negative_words.iterrows():
                rule = {
                    'id': f"negative_rule_{len(rules) + 1}",
                    'pattern': f".*{re.escape(row['word'])}.*",
                    'type': 'negative',
                    'confidence': row['negative_score'],
                    'word': row['word'],
                    'total_count': int(row['total_count']),
                    'negative_count': int(row['negative_count']),
                    'description': f"包含负面关键词: {row['word']}",
                    'priority': 'high' if row['negative_score'] >= 0.8 else 'medium',
                    'is_core_keyword': False
                }
                rules.append(rule)

        logger.info(f"✅ 传统方法生成了 {len(rules)} 条负面规则")
        return rules

def test_rule_generator():
    """测试规则生成器"""
    print("🧪 测试规则生成器")
    print("="*50)
    
    # 创建模拟的词汇评分数据
    test_data = [
        {'word': '亏损', 'negative_score': 0.9, 'positive_score': 0.1, 'neutral_score': 0.0, 
         'total_count': 100, 'negative_count': 90, 'positive_count': 10, 'neutral_count': 0},
        {'word': '违规', 'negative_score': 0.85, 'positive_score': 0.05, 'neutral_score': 0.1,
         'total_count': 80, 'negative_count': 68, 'positive_count': 4, 'neutral_count': 8},
        {'word': '增长', 'negative_score': 0.1, 'positive_score': 0.8, 'neutral_score': 0.1,
         'total_count': 120, 'negative_count': 12, 'positive_count': 96, 'neutral_count': 12},
        {'word': '股东大会', 'negative_score': 0.05, 'positive_score': 0.05, 'neutral_score': 0.9,
         'total_count': 200, 'negative_count': 10, 'positive_count': 10, 'neutral_count': 180}
    ]
    
    df = pd.DataFrame(test_data)
    
    # 创建规则生成器
    generator = RuleGenerator(df)
    
    # 生成规则
    all_rules = generator.generate_all_rules()
    
    print(f"\n📊 规则生成结果:")
    for rule_type, rules in all_rules.items():
        print(f"  {rule_type}: {len(rules)} 条规则")
        if rules:
            print(f"    示例: {rules[0]['word']} (置信度: {rules[0]['confidence']:.3f})")

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    test_rule_generator()
