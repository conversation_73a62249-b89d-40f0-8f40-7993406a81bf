#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
共享规则库加载器
Java和Python都可以使用这个加载器来读取共享规则库
"""

import json
import re
import os
from pathlib import Path
from typing import Dict, List, Tuple, Optional

class SharedRuleLibrary:
    def __init__(self, rule_library_path: str = "shared_rule_library.json"):
        """
        初始化共享规则库
        
        Args:
            rule_library_path: 规则库文件路径
        """
        self.rule_library_path = Path(rule_library_path)
        self.rules = {}
        self.compiled_patterns = {}
        self.metadata = {}
        
        # 加载规则库
        self.load_rule_library()
    
    def load_rule_library(self) -> bool:
        """加载规则库文件"""
        try:
            if not self.rule_library_path.exists():
                print(f"❌ 规则库文件不存在: {self.rule_library_path}")
                return False
            
            with open(self.rule_library_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 提取元数据
            self.metadata = data.get('metadata', {})
            
            # 加载规则
            self.rules = {
                'negative': data.get('negative_rules', {}),
                'positive': data.get('positive_rules', {}),
                'neutral': data.get('neutral_rules', {})
            }
            
            # 编译正则表达式
            self._compile_patterns()
            
            print(f"✅ 规则库加载成功: {self.metadata.get('version', 'unknown')}")
            print(f"   负面规则: {len(self.rules['negative'])} 类")
            print(f"   正面规则: {len(self.rules['positive'])} 类")
            print(f"   中性规则: {len(self.rules['neutral'])} 类")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载规则库失败: {e}")
            return False
    
    def _compile_patterns(self):
        """编译所有正则表达式模式"""
        self.compiled_patterns = {}
        
        for category, rules in self.rules.items():
            self.compiled_patterns[category] = {}
            for rule_name, rule_data in rules.items():
                patterns = rule_data.get('patterns', [])
                compiled_patterns = []
                
                for pattern in patterns:
                    try:
                        compiled_patterns.append(re.compile(pattern))
                    except re.error as e:
                        print(f"⚠️ 正则表达式编译失败: {pattern}, 错误: {e}")
                
                self.compiled_patterns[category][rule_name] = compiled_patterns
    
    def classify_announcement(self, title: str, content: str = "") -> Dict:
        """
        分类公告
        
        Args:
            title: 公告标题
            content: 公告内容（可选）
        
        Returns:
            分类结果字典
        """
        full_text = f"{title} {content}".strip()
        
        # 初始化结果
        result = {
            'classification': 'uncertain',  # uncertain, negative, positive, neutral
            'confidence': 0.0,
            'matched_rules': [],
            'matched_patterns': [],
            'reasoning': []
        }
        
        # 检查负面规则
        negative_matches = self._check_rules('negative', full_text)
        if negative_matches:
            result['classification'] = 'negative'
            result['matched_rules'].extend(negative_matches)
            result['confidence'] = max(0.8, len(negative_matches) * 0.1)
            result['reasoning'].append(f"匹配 {len(negative_matches)} 个负面规则")
        
        # 检查正面规则
        positive_matches = self._check_rules('positive', full_text)
        if positive_matches:
            result['classification'] = 'positive'
            result['matched_rules'].extend(positive_matches)
            result['confidence'] = max(0.8, len(positive_matches) * 0.1)
            result['reasoning'].append(f"匹配 {len(positive_matches)} 个正面规则")
        
        # 检查中性规则
        neutral_matches = self._check_rules('neutral', full_text)
        if neutral_matches and not (negative_matches or positive_matches):
            result['classification'] = 'neutral'
            result['matched_rules'].extend(neutral_matches)
            result['confidence'] = 0.9
            result['reasoning'].append(f"匹配 {len(neutral_matches)} 个中性规则")
        
        # 如果都没有匹配，标记为不确定
        if not any([negative_matches, positive_matches, neutral_matches]):
            result['reasoning'].append("未匹配任何规则，建议交给LLM处理")
        
        return result
    
    def _check_rules(self, category: str, text: str) -> List[str]:
        """检查特定类别的规则"""
        matches = []
        
        if category not in self.compiled_patterns:
            return matches
        
        for rule_name, patterns in self.compiled_patterns[category].items():
            for pattern in patterns:
                if pattern.search(text):
                    matches.append(f"{category}_{rule_name}")
                    break  # 一个规则类匹配即可
        
        return matches
    
    def get_rule_info(self, rule_name: str) -> Optional[Dict]:
        """获取特定规则的详细信息"""
        for category, rules in self.rules.items():
            if rule_name in rules:
                return {
                    'category': category,
                    'name': rule_name,
                    **rules[rule_name]
                }
        return None
    
    def add_rule(self, category: str, rule_name: str, patterns: List[str], confidence: str = "medium"):
        """添加新规则"""
        if category not in self.rules:
            self.rules[category] = {}
        
        self.rules[category][rule_name] = {
            'confidence': confidence,
            'patterns': patterns
        }
        
        # 重新编译模式
        self._compile_patterns()
        
        print(f"✅ 添加新规则: {category}_{rule_name}")
    
    def remove_rule(self, category: str, rule_name: str):
        """删除规则"""
        if category in self.rules and rule_name in self.rules[category]:
            del self.rules[category][rule_name]
            self._compile_patterns()
            print(f"✅ 删除规则: {category}_{rule_name}")
    
    def save_rule_library(self, output_path: str = None):
        """保存规则库到文件"""
        if output_path is None:
            output_path = self.rule_library_path
        
        # 准备保存数据
        save_data = {
            'metadata': self.metadata,
            'negative_rules': self.rules['negative'],
            'positive_rules': self.rules['positive'],
            'neutral_rules': self.rules['neutral']
        }
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 规则库已保存到: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ 保存规则库失败: {e}")
            return False
    
    def export_java_code(self, output_path: str = "java_rules.java"):
        """导出Java代码格式的规则"""
        java_code = """// 自动生成的Java规则代码
// 基于共享规则库生成

import java.util.List;
import java.util.regex.Pattern;

public class AutoGeneratedRules {
    
    private static final List<Pattern> negativePatterns = List.of(
"""
        
        # 添加负面规则
        for rule_name, rule_data in self.rules['negative'].items():
            java_code += f"        // {rule_name}\n"
            for pattern in rule_data['patterns']:
                # 转义Java字符串
                java_pattern = pattern.replace('\\', '\\\\').replace('"', '\\"')
                java_code += f'        Pattern.compile("{java_pattern}"),\n'
            java_code += "\n"
        
        java_code += """    );
    
    private static final List<Pattern> positivePatterns = List.of(
"""
        
        # 添加正面规则
        for rule_name, rule_data in self.rules['positive'].items():
            java_code += f"        // {rule_name}\n"
            for pattern in rule_data['patterns']:
                java_pattern = pattern.replace('\\', '\\\\').replace('"', '\\"')
                java_code += f'        Pattern.compile("{java_pattern}"),\n'
            java_code += "\n"
        
        java_code += """    );
    
    private static final List<Pattern> neutralPatterns = List.of(
"""
        
        # 添加中性规则
        for rule_name, rule_data in self.rules['neutral'].items():
            java_code += f"        // {rule_name}\n"
            for pattern in rule_data['patterns']:
                java_pattern = pattern.replace('\\', '\\\\').replace('"', '\\"')
                java_code += f'        Pattern.compile("{java_pattern}"),\n'
            java_code += "\n"
        
        java_code += """    );
}
"""
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(java_code)
            
            print(f"✅ Java规则代码已导出到: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ 导出Java代码失败: {e}")
            return False

def main():
    """测试规则库加载器"""
    print("🧪 测试共享规则库加载器...")
    
    # 创建规则库实例
    rule_lib = SharedRuleLibrary()
    
    # 测试分类功能
    test_cases = [
        {
            'title': '关于收到证监会警示函的公告',
            'expected': 'negative'
        },
        {
            'title': '2023年业绩预亏公告',
            'expected': 'negative'
        },
        {
            'title': '2023年业绩预增公告',
            'expected': 'positive'
        },
        {
            'title': '关于召开股东大会的通知',
            'expected': 'neutral'
        },
        {
            'title': '关于聘任会计师事务所的公告',
            'expected': 'neutral'
        }
    ]
    
    print("\n📋 测试分类功能:")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        result = rule_lib.classify_announcement(test_case['title'])
        expected = test_case['expected']
        actual = result['classification']
        
        status = "✅" if actual == expected else "❌"
        print(f"{i}. {status} {test_case['title']}")
        print(f"   期望: {expected}, 实际: {actual}")
        print(f"   置信度: {result['confidence']:.2f}")
        print(f"   匹配规则: {result['matched_rules']}")
        print()
    
    # 导出Java代码
    rule_lib.export_java_code()
    
    print("🎉 测试完成！")

if __name__ == "__main__":
    main()




