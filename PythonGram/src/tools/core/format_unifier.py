#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式统一工具
统一前后200个记录的格式，确保版本管理一致性
"""

import json
import os
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FormatUnifier:
    """格式统一器"""
    
    def __init__(self, analysis_output_dir: str = "E:/LLMData/analysis_output"):
        """
        初始化格式统一器
        
        Args:
            analysis_output_dir: 分析输出目录
        """
        self.analysis_output_dir = Path(analysis_output_dir)
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'updated_files': 0,
            'error_files': 0,
            'old_format_files': 0,
            'new_format_files': 0
        }
        
        logger.info(f"📁 格式统一器初始化，目录: {self.analysis_output_dir}")
    
    def scan_format_differences(self) -> Dict:
        """
        扫描格式差异
        
        Returns:
            格式差异统计
        """
        logger.info("🔍 扫描格式差异...")
        
        if not self.analysis_output_dir.exists():
            logger.error(f"❌ 目录不存在: {self.analysis_output_dir}")
            return {}
        
        json_files = list(self.analysis_output_dir.glob("*.json"))
        self.stats['total_files'] = len(json_files)
        
        format_types = {
            'old_timestamp_format': 0,    # v_20250825_233321 格式
            'new_version_format': 0,      # v1.0 格式
            'no_python_student_c': 0,     # 没有python_student_c字段
            'mixed_format': 0             # 混合格式
        }
        
        sample_files = {
            'old_format': [],
            'new_format': [],
            'no_student_c': []
        }
        
        for file_path in json_files[:100]:  # 只检查前100个文件
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                decision_logic = data.get('decisionLogic', {})
                python_student_c = decision_logic.get('python_student_c', {})
                
                if not python_student_c:
                    format_types['no_python_student_c'] += 1
                    sample_files['no_student_c'].append(file_path.name)
                else:
                    versions = list(python_student_c.keys())
                    has_old_format = any(v.startswith('v_') and len(v) > 10 for v in versions)
                    has_new_format = any(v.startswith('v') and len(v) <= 5 for v in versions)
                    
                    if has_old_format and has_new_format:
                        format_types['mixed_format'] += 1
                    elif has_old_format:
                        format_types['old_timestamp_format'] += 1
                        sample_files['old_format'].append(file_path.name)
                    elif has_new_format:
                        format_types['new_version_format'] += 1
                        sample_files['new_format'].append(file_path.name)
                
                self.stats['processed_files'] += 1
                
            except Exception as e:
                logger.error(f"❌ 读取文件失败 {file_path}: {e}")
                self.stats['error_files'] += 1
        
        # 显示扫描结果
        print("\n📊 格式差异扫描结果:")
        print("="*50)
        for format_type, count in format_types.items():
            percentage = count / self.stats['processed_files'] * 100 if self.stats['processed_files'] > 0 else 0
            print(f"  {format_type}: {count} ({percentage:.1f}%)")
        
        # 显示样本文件
        print(f"\n📋 样本文件:")
        for category, files in sample_files.items():
            if files:
                print(f"  {category}: {files[:3]}...")  # 只显示前3个
        
        return format_types
    
    def unify_version_format(self, target_version: str = "v1.0", dry_run: bool = True) -> None:
        """
        统一版本格式
        
        Args:
            target_version: 目标版本号
            dry_run: 是否为试运行（不实际修改文件）
        """
        logger.info(f"🔧 开始统一版本格式，目标版本: {target_version}")
        logger.info(f"模式: {'试运行' if dry_run else '实际执行'}")
        
        if not self.analysis_output_dir.exists():
            logger.error(f"❌ 目录不存在: {self.analysis_output_dir}")
            return
        
        json_files = list(self.analysis_output_dir.glob("*.json"))
        
        for file_path in json_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                decision_logic = data.get('decisionLogic', {})
                python_student_c = decision_logic.get('python_student_c', {})
                
                if not python_student_c:
                    continue  # 跳过没有python_student_c的文件
                
                # 检查是否需要更新
                needs_update = False
                old_versions = []
                
                for version_key in list(python_student_c.keys()):
                    if version_key.startswith('v_') and len(version_key) > 10:
                        # 这是旧的时间戳格式
                        old_versions.append(version_key)
                        needs_update = True
                
                if needs_update:
                    logger.info(f"📝 需要更新: {file_path.name}")
                    
                    if not dry_run:
                        # 合并所有旧版本的数据到新版本
                        merged_data = {}
                        latest_timestamp = ""
                        
                        for old_version in old_versions:
                            version_data = python_student_c[old_version]
                            
                            # 保留最新的数据
                            if not merged_data or version_data.get('timestamp', '') > latest_timestamp:
                                merged_data = version_data.copy()
                                latest_timestamp = version_data.get('timestamp', '')
                            
                            # 删除旧版本
                            del python_student_c[old_version]
                        
                        # 更新版本信息
                        merged_data['studentVersion'] = target_version.replace('v', '')
                        merged_data['ruleVersion'] = target_version.replace('v', '')
                        
                        # 添加新版本
                        python_student_c[target_version] = merged_data
                        
                        # 写回文件
                        with open(file_path, 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        
                        self.stats['updated_files'] += 1
                        logger.debug(f"✅ 已更新: {file_path.name}")
                    else:
                        logger.info(f"  旧版本: {old_versions}")
                        logger.info(f"  将统一为: {target_version}")
                
                self.stats['processed_files'] += 1
                
            except Exception as e:
                logger.error(f"❌ 处理文件失败 {file_path}: {e}")
                self.stats['error_files'] += 1
        
        # 显示统计结果
        print(f"\n📊 格式统一结果:")
        print("="*30)
        print(f"  总文件数: {self.stats['total_files']}")
        print(f"  处理文件数: {self.stats['processed_files']}")
        print(f"  更新文件数: {self.stats['updated_files']}")
        print(f"  错误文件数: {self.stats['error_files']}")
        
        if dry_run:
            print(f"\n⚠️ 这是试运行，没有实际修改文件")
            print(f"如需实际执行，请设置 dry_run=False")
    
    def validate_format_consistency(self) -> bool:
        """
        验证格式一致性
        
        Returns:
            是否格式一致
        """
        logger.info("✅ 验证格式一致性...")
        
        json_files = list(self.analysis_output_dir.glob("*.json"))
        inconsistent_files = []
        
        for file_path in json_files[:50]:  # 检查前50个文件
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                decision_logic = data.get('decisionLogic', {})
                python_student_c = decision_logic.get('python_student_c', {})
                
                if python_student_c:
                    versions = list(python_student_c.keys())
                    
                    # 检查是否有旧格式
                    has_old_format = any(v.startswith('v_') and len(v) > 10 for v in versions)
                    
                    if has_old_format:
                        inconsistent_files.append(file_path.name)
                
            except Exception as e:
                logger.error(f"❌ 验证文件失败 {file_path}: {e}")
        
        if inconsistent_files:
            print(f"\n⚠️ 发现 {len(inconsistent_files)} 个格式不一致的文件:")
            for file_name in inconsistent_files[:10]:  # 只显示前10个
                print(f"  - {file_name}")
            return False
        else:
            print(f"\n✅ 格式一致性验证通过")
            return True
    
    def create_backup(self) -> Optional[str]:
        """
        创建备份
        
        Returns:
            备份目录路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = Path(f"E:/LLMData/backups/format_unify_backup_{timestamp}")
        
        try:
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 只备份有python_student_c字段的文件
            json_files = list(self.analysis_output_dir.glob("*.json"))
            backup_count = 0
            
            for file_path in json_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    if 'decisionLogic' in data and 'python_student_c' in data['decisionLogic']:
                        backup_file = backup_dir / file_path.name
                        with open(backup_file, 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        backup_count += 1
                
                except Exception as e:
                    logger.error(f"❌ 备份文件失败 {file_path}: {e}")
            
            logger.info(f"✅ 备份完成: {backup_dir}")
            logger.info(f"📁 备份文件数: {backup_count}")
            return str(backup_dir)
            
        except Exception as e:
            logger.error(f"❌ 创建备份失败: {e}")
            return None

def main():
    """主函数"""
    print("🔧 格式统一工具")
    print("="*50)
    
    # 创建格式统一器
    unifier = FormatUnifier()
    
    # 1. 扫描格式差异
    print("\n1️⃣ 扫描格式差异")
    format_stats = unifier.scan_format_differences()
    
    # 2. 创建备份
    print("\n2️⃣ 创建备份")
    backup_dir = unifier.create_backup()
    
    if backup_dir:
        # 3. 试运行格式统一
        print("\n3️⃣ 试运行格式统一")
        unifier.unify_version_format(target_version="v1.0", dry_run=True)
        
        # 4. 询问是否执行
        print("\n4️⃣ 确认执行")
        response = input("是否执行实际的格式统一？(y/N): ").strip().lower()
        
        if response == 'y':
            print("\n🚀 执行格式统一...")
            unifier.unify_version_format(target_version="v1.0", dry_run=False)
            
            # 5. 验证结果
            print("\n5️⃣ 验证格式一致性")
            is_consistent = unifier.validate_format_consistency()
            
            if is_consistent:
                print("\n🎉 格式统一完成！")
            else:
                print("\n⚠️ 格式统一可能存在问题，请检查")
        else:
            print("\n⏹️ 取消执行，文件未修改")
    else:
        print("\n❌ 备份失败，取消格式统一操作")

if __name__ == "__main__":
    main()
