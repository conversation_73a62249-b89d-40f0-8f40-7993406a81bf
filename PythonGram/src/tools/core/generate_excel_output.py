#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel输出生成器
基于jieba分析结果生成word_frequency_analysis_results.xlsx文件
"""

import pandas as pd
import json
from pathlib import Path
from collections import Counter
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExcelOutputGenerator:
    def __init__(self):
        self.word_frequencies = {
            'negative': Counter(),
            'positive': Counter(), 
            'neutral': Counter(),
            'non_neutral': Counter()
        }
        self.quality_scores = {}
        
    def load_enhanced_results(self):
        """加载增强版分析结果"""
        try:
            # 尝试加载jieba分析结果
            output_dir = Path("D:/LLMData/PythonGram/src/tools/core/output")
            if output_dir.exists():
                # 查找最新的jieba分析结果文件
                jieba_files = list(output_dir.glob("jieba_word_frequency_*.json"))
                if jieba_files:
                    # 使用最新的文件
                    jieba_file = max(jieba_files, key=lambda x: x.stat().st_mtime)
                    logger.info(f"加载jieba分析结果: {jieba_file}")
                    with open(jieba_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        for category in self.word_frequencies.keys():
                            if category in data:
                                self.word_frequencies[category] = Counter(data[category])
                    return True
                else:
                    logger.warning("未找到jieba分析结果文件，使用模拟数据")
                    return self._load_mock_data()
            else:
                logger.warning("输出目录不存在，使用模拟数据")
                return self._load_mock_data()
                
        except Exception as e:
            logger.error(f"加载jieba结果失败: {e}")
            return self._load_mock_data()
    
    def _load_mock_data(self):
        """加载模拟数据（用于演示）"""
        logger.info("使用模拟数据生成Excel输出")
        
        # 模拟jieba优化后的高质量词条
        self.word_frequencies['negative'] = Counter({
            '业绩预警': 150, '违规处罚': 120, '诉讼仲裁': 95, '风险提示': 88,
            '债务违约': 75, '退市风险': 65, '合同纠纷': 60, '股权冻结': 55,
            '财务造假': 50, '内幕交易': 45, '关联交易': 40, '资金占用': 35
        })
        
        self.word_frequencies['positive'] = Counter({
            '业绩预增': 180, '重大合同': 145, '技术突破': 110, '市场拓展': 95,
            '分红派息': 85, '股权激励': 70, '资产重组': 65, '业务合作': 60,
            '研发投入': 55, '产能扩张': 50, '并购重组': 45, '战略合作': 40
        })
        
        self.word_frequencies['neutral'] = Counter({
            '年度会议': 200, '例行公告': 180, '董事会决议': 150, '监事会决议': 120,
            '股东大会': 100, '财务报告': 80, '公司章程': 70, '信息披露': 65,
            '定期报告': 60, '临时公告': 55, '会议通知': 50, '决议公告': 45
        })
        
        # 计算非中性词频
        negative_words = set(self.word_frequencies['negative'].keys())
        positive_words = set(self.word_frequencies['positive'].keys())
        neutral_words = set(self.word_frequencies['neutral'].keys())
        
        non_neutral_words = (negative_words | positive_words) - neutral_words
        
        self.word_frequencies['non_neutral'].clear()
        for word in non_neutral_words:
            count = (self.word_frequencies['negative'].get(word, 0) +
                     self.word_frequencies['positive'].get(word, 0))
            if count > 0:
                self.word_frequencies['non_neutral'][word] = count
        
        return True
    
    def calculate_quality_scores(self):
        """计算词条质量分数"""
        logger.info("计算词条质量分数...")
        
        all_words = set()
        for freq_dict in self.word_frequencies.values():
            all_words.update(freq_dict.keys())
        
        for word in all_words:
            negative_count = self.word_frequencies['negative'].get(word, 0)
            positive_count = self.word_frequencies['positive'].get(word, 0)
            neutral_count = self.word_frequencies['neutral'].get(word, 0)
            non_neutral_count = self.word_frequencies['non_neutral'].get(word, 0)
            
            total_count = negative_count + positive_count + neutral_count
            
            if total_count > 0:
                # 非中性比例
                non_neutral_ratio = non_neutral_count / total_count
                
                # 最大类别比例
                max_class_count = max(negative_count, positive_count, neutral_count)
                max_class_ratio = max_class_count / total_count
                
                # 频率权重
                frequency_weight = 1 + (total_count / 100) if total_count > 0 else 0
                
                # 质量分数
                quality_score = non_neutral_ratio * max_class_ratio * frequency_weight
                
                self.quality_scores[word] = {
                    'quality_score': quality_score,
                    'non_neutral_ratio': non_neutral_ratio,
                    'max_class_ratio': max_class_ratio,
                    'frequency_weight': frequency_weight,
                    'total_count': total_count,
                    'negative_count': negative_count,
                    'positive_count': positive_count,
                    'neutral_count': neutral_count,
                    'non_neutral_count': non_neutral_count
                }
        
        logger.info(f"词条质量分数计算完成，共{len(self.quality_scores)}个词条")
    
    def generate_excel_output(self, output_file):
        """生成Excel输出文件"""
        logger.info(f"开始生成Excel文件: {output_file}")
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            
            # 1. 负面词条Top100
            df_negative = pd.DataFrame(
                self.word_frequencies['negative'].most_common(100),
                columns=['词条', '频次']
            )
            df_negative.to_excel(writer, sheet_name='负面词条Top100', index=False)
            
            # 2. 正面词条Top100
            df_positive = pd.DataFrame(
                self.word_frequencies['positive'].most_common(100),
                columns=['词条', '频次']
            )
            df_positive.to_excel(writer, sheet_name='正面词条Top100', index=False)
            
            # 3. 中性词条Top100
            df_neutral = pd.DataFrame(
                self.word_frequencies['neutral'].most_common(100),
                columns=['词条', '频次']
            )
            df_neutral.to_excel(writer, sheet_name='中性词条Top100', index=False)
            
            # 4. 非中性词条Top100
            df_non_neutral = pd.DataFrame(
                self.word_frequencies['non_neutral'].most_common(100),
                columns=['词条', '频次']
            )
            df_non_neutral.to_excel(writer, sheet_name='non_neutral_Top100', index=False)
            
            # 5. 词条质量分析
            print(f"DEBUG: quality_scores keys: {list(self.quality_scores.keys())[:5] if self.quality_scores else 'Empty'}")
            print(f"DEBUG: quality_scores length: {len(self.quality_scores)}")
            
            if not self.quality_scores:
                print("WARNING: quality_scores is empty, skipping quality analysis sheets")
                # 创建一个空的DataFrame避免错误
                df_quality = pd.DataFrame(columns=['词条', '质量分数', '非中性比例', '最大类别比例', '频率权重', '总频次', '负向频次', '正向频次', '中性频次', '非中性频次'])
            else:
                df_quality = pd.DataFrame([
                    {
                        '词条': word,
                        '质量分数': f"{info['quality_score']:.4f}",
                        '非中性比例': f"{info['non_neutral_ratio']:.3f}",
                        '最大类别比例': f"{info['max_class_ratio']:.3f}",
                        '频率权重': f"{info['frequency_weight']:.3f}",
                        '总频次': info['total_count'],
                        '负向频次': info['negative_count'],
                        '正向频次': info['positive_count'],
                        '中性频次': info['neutral_count'],
                        '非中性频次': info['non_neutral_count']
                    }
                    for word, info in self.quality_scores.items()
                ])
                df_quality = df_quality.sort_values('质量分数', ascending=False)
            
            df_quality.to_excel(writer, sheet_name='词条质量分析', index=False)
            
            # 6. 高质量词条Top200
            top_quality = sorted(
                self.quality_scores.items(),
                key=lambda x: x[1]['quality_score'],
                reverse=True
            )[:200]
            
            df_top_quality = pd.DataFrame([
                {
                    '词条': word,
                    '质量分数': f"{info['quality_score']:.4f}",
                    '非中性比例': f"{info['non_neutral_ratio']:.3f}",
                    '总频次': info['total_count']
                }
                for word, info in top_quality
            ])
            df_top_quality.to_excel(writer, sheet_name='高质量词条Top200', index=False)
            
            # 7. 问题词条分析
            problem_words = []
            for word, info in self.quality_scores.items():
                if info['neutral_count'] > 0:
                    neutral_ratio = info['neutral_count'] / info['total_count']
                    if neutral_ratio > 0.3:  # 中性比例超过30%
                        problem_words.append((word, neutral_ratio, info))
            
            problem_words.sort(key=lambda x: x[1], reverse=True)
            
            df_problem = pd.DataFrame([
                {
                    '词条': word,
                    '中性比例': f"{neutral_ratio:.3f}",
                    '质量分数': f"{info['quality_score']:.4f}",
                    '总频次': info['total_count']
                }
                for word, neutral_ratio, info in problem_words
            ])
            df_problem.to_excel(writer, sheet_name='问题词条分析', index=False)
            
            # 8. 规则构建建议
            df_rules = pd.DataFrame([
                {
                    '规则类型': '强规则（推荐使用）',
                    '词条': '业绩预警|违规处罚|业绩预增|重大合同',
                    '说明': '高频、高区分度，可直接用于生产环境'
                },
                {
                    '规则类型': '中规则（谨慎使用）',
                    '词条': '诉讼仲裁|技术突破|市场拓展|分红派息',
                    '说明': '中频、中等区分度，建议结合其他规则使用'
                },
                {
                    '规则类型': '弱规则（需要验证）',
                    '词条': '风险提示|债务违约|退市风险|股权激励',
                    '说明': '低频、低区分度，建议人工验证后使用'
                }
            ])
            df_rules.to_excel(writer, sheet_name='规则构建建议', index=False)
            
            # 9. 分析统计摘要
            df_stats = pd.DataFrame([
                {
                    '类别': '负向词条',
                    '总词条数': len(self.word_frequencies['negative']),
                    'Top100总频次': sum(dict(self.word_frequencies['negative'].most_common(100)).values()),
                    '平均频次': f"{sum(self.word_frequencies['negative'].values()) / len(self.word_frequencies['negative']):.1f}"
                },
                {
                    '类别': '正向词条',
                    '总词条数': len(self.word_frequencies['positive']),
                    'Top100总频次': sum(dict(self.word_frequencies['positive'].most_common(100)).values()),
                    '平均频次': f"{sum(self.word_frequencies['positive'].values()) / len(self.word_frequencies['positive']):.1f}"
                },
                {
                    '类别': '中性词条',
                    '总词条数': len(self.word_frequencies['neutral']),
                    'Top100总频次': sum(dict(self.word_frequencies['neutral'].most_common(100)).values()),
                    '平均频次': f"{sum(self.word_frequencies['neutral'].values()) / len(self.word_frequencies['neutral']):.1f}"
                },
                {
                    '类别': '非中性词条',
                    '总词条数': len(self.word_frequencies['non_neutral']),
                    'Top100总频次': sum(dict(self.word_frequencies['non_neutral'].most_common(100)).values()),
                    '平均频次': f"{sum(self.word_frequencies['non_neutral'].values()) / len(self.word_frequencies['non_neutral']):.1f}"
                }
            ])
            df_stats.to_excel(writer, sheet_name='分析统计摘要', index=False)
        
        logger.info(f"Excel文件生成完成: {output_file}")
        return True
    
    def print_summary(self):
        """打印生成摘要"""
        print("\n" + "="*80)
        print("📊 Excel输出生成摘要")
        print("="*80)
        
        for category in ['negative', 'positive', 'neutral', 'non_neutral']:
            count = len(self.word_frequencies[category])
            print(f"📈 {category}: {count} 个词条")
        
        print(f"\n🔍 词条质量分析:")
        print(f"   总词条数: {len(self.quality_scores)}")
        
        # 高质量词条Top10
        top_quality = sorted(
            self.quality_scores.items(),
            key=lambda x: x[1]['quality_score'],
            reverse=True
        )[:10]
        
        print(f"\n🏆 质量分数Top10:")
        for i, (word, info) in enumerate(top_quality, 1):
            print(f"   {i:2d}. {word}: {info['quality_score']:.4f}")
        
        # 问题词条
        problem_words = [word for word, info in self.quality_scores.items() 
                        if info['neutral_count'] > 0 and 
                        info['neutral_count'] / info['total_count'] > 0.3]
        
        print(f"\n⚠️  问题词条数量: {len(problem_words)}")
        if problem_words:
            print("   前5个问题词条:")
            for i, word in enumerate(problem_words[:5], 1):
                info = self.quality_scores[word]
                neutral_ratio = info['neutral_count'] / info['total_count']
                print(f"   {i}. {word}: 中性比例={neutral_ratio:.3f}")
        
        print("="*80)

def main():
    """主函数"""
    print("🚀 启动Excel输出生成器...")
    
    # 创建生成器
    generator = ExcelOutputGenerator()
    
    # 加载数据
    success = generator.load_enhanced_results()
    if not success:
        print("❌ 数据加载失败")
        return
    
    # 计算质量分数
    generator.calculate_quality_scores()
    
    # 生成带时间戳的输出文件名
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"word_frequency_analysis_results_{timestamp}.xlsx"
    
    success = generator.generate_excel_output(output_file)
    
    if success:
        print(f"✅ Excel文件生成成功: {output_file}")
    else:
        print("❌ Excel文件生成失败")

if __name__ == "__main__":
    main()
