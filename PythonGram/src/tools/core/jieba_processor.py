#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jieba文本处理器

封装了所有与jieba相关的操作，包括分词、停用词过滤和关键词提取。
"""
import jieba
import jieba.analyse
import logging
from typing import List, Tuple, Set

logger = logging.getLogger(__name__)

class JiebaProcessor:
    def __init__(self, stopwords_path: str = None):
        # 加载一个更完整的停用词表（如果需要，可以从外部文件加载）
        self.stopwords_path = stopwords_path
        self.stopwords = self._load_stopwords()
        logger.info(f"Jieba处理器初始化，加载了 {len(self.stopwords)} 个停用词。")

    def _load_stopwords(self) -> Set[str]:
        """如果提供了路径，则从文件加载停用词，否则使用默认列表。"""
        default_stopwords = {
            '的', '了', '是', '在', '和', '与', '或', '也', '等', '公司', '我们', '进行',
            '本公司', '有限公司', '关于', '说明', '公告', '通知', '议案', '情况'
        }
        if self.stopwords_path and Path(self.stopwords_path).is_file():
            try:
                with open(self.stopwords_path, 'r', encoding='utf-8') as f:
                    # 读取行，去除空白，并过滤掉空行
                    stopwords_from_file = {line.strip() for line in f if line.strip()}
                    return default_stopwords.union(stopwords_from_file)
            except Exception as e:
                logger.warning(f"无法从 {self.stopwords_path} 加载停用词: {e}。将使用默认列表。")
                return default_stopwords
        else:
            logger.info("未提供自定义停用词文件或文件不存在。将使用默认列表。")
            return default_stopwords

    def extract_weighted_keywords(self, text: str, top_k: int = 20) -> List[Tuple[str, float]]:
        """
        使用TextRank算法提取带权重的关键词。
        TextRank通常比TF-IDF更适合单篇文档的关键词提取。
        """
        if not isinstance(text, str) or not text.strip():
            return []

        # 使用jieba的TextRank算法提取关键词
        # allowPOS=('n', 'nr', 'ns', 'nt', 'nz', 'v', 'vn') 可以限制只提取名词和动词
        keywords = jieba.analyse.textrank(text, topK=top_k, withWeight=True, allowPOS=('n', 'v', 'vn', 'nz'))
        
        # 过滤掉停用词和单字词
        filtered_keywords = [
            (word, weight) for word, weight in keywords
            if word not in self.stopwords and len(word) > 1
        ]
        return filtered_keywords