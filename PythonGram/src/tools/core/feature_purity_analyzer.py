#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征纯度分析器
基于统计显著性计算特征纯度的分析器
"""

import logging
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict

logger = logging.getLogger(__name__)

class FeaturePurityAnalyzer:
    """基于统计显著性计算特征纯度的分析器"""
    
    def __init__(self, word_scores_df: pd.DataFrame):
        """
        初始化分析器
        
        Args:
            word_scores_df: 来自 InformationGainScorer 的包含统计评分的DataFrame
        """
        if word_scores_df is None or word_scores_df.empty:
            raise ValueError("输入的 word_scores_df 为空或无效。")
        
        self.df = word_scores_df.copy()
        logger.info("🔬 高级特征纯度分析器初始化完成。")
    
    def analyze_purity(self, p_value_threshold: float = 0.05, min_doc_count: int = 3):
        """
        计算每个词条的"纯度"并筛选出高质量特征。
        
        Args:
            p_value_threshold: P值阈值，用于筛选统计显著的特征
            min_doc_count: 最小文档数阈值
        """
        logger.info("🔍 开始计算特征纯度...")
        
        # 检查必要的列是否存在
        required_columns = ['P值_负向', 'P值_正向', '总文档数', '卡方值_负向', '卡方值_正向', '卡方值_中性']
        missing_columns = [col for col in required_columns if col not in self.df.columns]
        
        if missing_columns:
            logger.error(f"缺少必要的列: {missing_columns}")
            return
        
        # 1. 基础过滤：确保统计显著性 (P值足够小) 且有一定支持度
        # 我们只关心与"负面"或"正面"显著相关的词
        significant_mask = (
            (self.df['P值_负向'] < p_value_threshold) | 
            (self.df['P值_正向'] < p_value_threshold)
        ) & (self.df['总文档数'] >= min_doc_count)
        
        self.df = self.df[significant_mask].copy()
        logger.info(f"经过P值和最低文档数过滤后，剩余 {len(self.df)} 个候选特征。")
        
        if self.df.empty:
            logger.warning("没有找到足够显著的特征词进行纯度分析。")
            return
        
        # 2. 计算卡方总分和各类别的"纯度"
        # 添加一个极小值 epsilon 防止除以零
        epsilon = 1e-9
        chi2_sum = self.df['卡方值_负向'] + self.df['卡方值_正向'] + self.df['卡方值_中性'] + epsilon
        
        self.df['负面纯度'] = self.df['卡方值_负向'] / chi2_sum
        self.df['正面纯度'] = self.df['卡方值_正向'] / chi2_sum
        self.df['中性纯度'] = self.df['卡方值_中性'] / chi2_sum
        self.df['卡方总分'] = chi2_sum - epsilon
        
        # 3. 确定每个词条的主要倾向类别
        self.df['主要倾向'] = self.df[['负面纯度', '正面纯度', '中性纯度']].idxmax(axis=1).str.replace('纯度', '')
        
        logger.info("✅ 特征纯度计算完成。")
    
    def get_pure_keywords(self, category: str, purity_threshold: float = 0.7, top_n: int = 100) -> pd.DataFrame:
        """
        获取指定类别的高纯度关键词列表。
        
        Args:
            category (str): '负面', '正面', 或 '中性'
            purity_threshold (float): 纯度阈值
            top_n (int): 返回前N个结果
            
        Returns:
            pd.DataFrame: 筛选并排序后的高纯度关键词DataFrame
        """
        if f'{category}纯度' not in self.df.columns:
            logger.error(f"无效的类别: {category}。请从 ['负面', '正面', '中性'] 中选择。")
            return pd.DataFrame()
        
        purity_col = f'{category}纯度'
        chi2_col = f'卡方值_{category}向' if category != '中性' else '卡方值_中性'
        
        # 筛选出主要倾向为该类别，且纯度达标的词条
        filtered_df = self.df[
            (self.df['主要倾向'] == category) &
            (self.df[purity_col] >= purity_threshold)
        ].copy()
        
        # 按该类别的卡方值排序
        if chi2_col in filtered_df.columns:
            sorted_df = filtered_df.sort_values(by=chi2_col, ascending=False)
        else:
            # 如果没有对应的卡方值列，按纯度排序
            sorted_df = filtered_df.sort_values(by=purity_col, ascending=False)
        
        return sorted_df.head(top_n)
    
    def save_purity_report(self, output_path: str):
        """
        保存特征纯度分析报告到Excel文件
        
        Args:
            output_path: 输出文件路径
        """
        if self.df.empty or '负面纯度' not in self.df.columns:
            logger.warning("⚠️ 没有可供保存的纯度分析数据。请先运行 analyze_purity()。")
            return
        
        output_path_obj = Path(output_path)
        output_path_obj.parent.mkdir(parents=True, exist_ok=True)
        
        with pd.ExcelWriter(output_path_obj, engine='openpyxl') as writer:
            # 1. 保存完整的分析结果
            self.df.sort_values(by='卡方总分', ascending=False).to_excel(
                writer, sheet_name='总览_按卡方总分排序', index=False
            )
            
            # 2. 为每个类别保存高纯度词汇列表
            for category in ['负面', '正面', '中性']:
                pure_keywords_df = self.get_pure_keywords(category, purity_threshold=0.7, top_n=500)
                if not pure_keywords_df.empty:
                    sheet_name = f'高纯度_{category}词'
                    pure_keywords_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        logger.info(f"💾 特征纯度分析报告已保存: {output_path}")
    
    def get_summary_stats(self) -> Dict:
        """获取纯度分析的统计摘要"""
        if self.df.empty or '负面纯度' not in self.df.columns:
            return {"message": "请先运行 analyze_purity()"}
        
        stats = {
            "总特征数": len(self.df),
            "各类别分布": {
                "负面": len(self.df[self.df['主要倾向'] == '负面']),
                "正面": len(self.df[self.df['主要倾向'] == '正面']),
                "中性": len(self.df[self.df['主要倾向'] == '中性'])
            },
            "高纯度特征数": {
                "负面(≥0.7)": len(self.df[(self.df['主要倾向'] == '负面') & (self.df['负面纯度'] >= 0.7)]),
                "正面(≥0.7)": len(self.df[(self.df['主要倾向'] == '正面') & (self.df['正面纯度'] >= 0.7)]),
                "中性(≥0.7)": len(self.df[(self.df['主要倾向'] == '中性') & (self.df['中性纯度'] >= 0.7)])
            },
            "平均纯度": {
                "负面": self.df[self.df['主要倾向'] == '负面']['负面纯度'].mean() if len(self.df[self.df['主要倾向'] == '负面']) > 0 else 0,
                "正面": self.df[self.df['主要倾向'] == '正面']['正面纯度'].mean() if len(self.df[self.df['主要倾向'] == '正面']) > 0 else 0,
                "中性": self.df[self.df['主要倾向'] == '中性']['中性纯度'].mean() if len(self.df[self.df['主要倾向'] == '中性']) > 0 else 0
            }
        }
        
        return stats

def main():
    """主函数 - 演示特征纯度分析器的使用"""
    print("🔬 特征纯度分析器演示")
    print("=" * 60)
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 这里需要从实际的数据源获取word_scores_df
    # 示例：假设我们有一个包含必要列的DataFrame
    try:
        # 这里应该从实际的分析结果中加载数据
        # from python_teacher_learning_v2 import PythonTeacherLearningV2
        # teacher = PythonTeacherLearningV2(...)
        # word_scores_df = teacher.run_complete_analysis()
        
        print("⚠️ 这是一个演示脚本。")
        print("实际使用时，请提供包含以下列的DataFrame:")
        print("- 词条")
        print("- P值_负向, P值_正向")
        print("- 总文档数")
        print("- 卡方值_负向, 卡方值_正向, 卡方值_中性")
        
        # 创建示例数据用于演示
        import numpy as np
        sample_data = {
            '词条': ['退市风险', '业绩增长', '股东大会', '严重亏损', '稳定发展'],
            'P值_负向': [0.001, 0.8, 0.5, 0.001, 0.3],
            'P值_正向': [0.9, 0.001, 0.6, 0.9, 0.001],
            '总文档数': [50, 30, 100, 25, 40],
            '卡方值_负向': [25.5, 0.1, 2.0, 30.2, 1.5],
            '卡方值_正向': [0.1, 20.3, 1.8, 0.2, 15.8],
            '卡方值_中性': [1.2, 2.1, 15.5, 1.1, 3.2]
        }
        
        word_scores_df = pd.DataFrame(sample_data)
        
        # 创建分析器
        analyzer = FeaturePurityAnalyzer(word_scores_df)
        
        # 执行纯度分析
        analyzer.analyze_purity()
        
        # 获取统计摘要
        stats = analyzer.get_summary_stats()
        print("\n📊 纯度分析统计摘要:")
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        # 获取高纯度负面关键词
        pure_negative = analyzer.get_pure_keywords('负面', top_n=10)
        if not pure_negative.empty:
            print(f"\n🔍 高纯度负面关键词 (前10个):")
            print(pure_negative[['词条', '负面纯度', '卡方值_负向']].to_string(index=False))
        
        # 保存报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = f"feature_purity_report_{timestamp}.xlsx"
        analyzer.save_purity_report(report_path)
        
        print(f"\n✅ 演示完成！报告已保存: {report_path}")
        
    except Exception as e:
        logger.error(f"演示过程中出现错误: {e}")

if __name__ == "__main__":
    main()
