#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本化知识库配置管理系统
基于风险优先级的规则配置，支持版本管理和生产升级
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

class RiskPriorityThresholds(BaseModel):
    """风险优先级阈值配置"""
    # P0 - 安全性（生命线）
    negative_recall_threshold: float = Field(default=0.95, ge=0.90, le=1.0, 
                                           description="负面召回率最低门槛")
    
    # P1 - 可靠性（第二道防线）
    neutral_contamination_threshold: float = Field(default=0.10, ge=0.05, le=0.30,
                                                  description="中性污染率最高门槛")
    
    # P2 - 效率（优化目标）
    neutral_recall_target: float = Field(default=0.80, ge=0.60, le=0.95,
                                        description="中性召回率目标值")

class SafetyPatterns(BaseModel):
    """P0安全性模式 - 绝不能遗漏的负面信号"""
    
    # 核心负面关键词（极高风险）
    critical_negative_keywords: List[str] = Field(default=[
        "亏损", "预亏", "违规", "违法", "处罚", "警示", "风险提示",
        "退市", "停牌", "破产", "立案", "调查", "诉讼", "仲裁",
        "冻结", "查封", "减持", "下降", "下滑", "预降", "大幅",
        "严重", "重大风险", "异常", "暂停", "终止", "监管函",
        "关注函", "问询函", "行政处罚", "刑事", "拘留"
    ])
    
    # 数字+负面词汇组合模式
    numerical_negative_patterns: List[str] = Field(default=[
        r".*\d+.*[万亿].*[亏损|损失|下降|减少].*",
        r".*[亏损|损失].*\d+.*[万亿].*",
        r".*预计.*[亏损|下降].*\d+.*",
        r".*同比.*[下降|减少].*\d+.*%.*",
        r".*净利润.*[下降|减少|亏损].*\d+.*"
    ])
    
    # 监管机构相关（必须标记为风险）- 使用更精确的模式
    regulatory_patterns: List[str] = Field(default=[
        r".*收到.*证监会.*处罚.*",
        r".*证监会.*立案调查.*",
        r".*银保监会.*处罚.*",
        r".*问询函.*",
        r".*关注函.*",
        r".*监管函.*"
    ])

class ReliabilityPatterns(BaseModel):
    """P1可靠性模式 - 只有绝对确定的才能标记为中性"""
    
    # 超安全中性模式（污染率<5%）
    ultra_safe_neutral_patterns: List[str] = Field(default=[
        r"^.*股东大会.*决议.*(?!.*风险|.*亏损|.*违规|.*处罚|.*重大|.*异常).*$",
        r"^.*董事会.*决议.*(?!.*风险|.*亏损|.*违规|.*处罚|.*重大|.*异常).*$",
        r"^.*监事会.*决议.*(?!.*风险|.*亏损|.*违规|.*处罚|.*重大|.*异常).*$",
        r"^.*定期报告.*(?!.*预警|.*风险|.*亏损|.*异常|.*重大).*$",
        r"^.*中签.*公告.*$",
        r"^.*招股.*说明.*书.*$",
        r"^.*聘任.*(?!.*违规|.*调查).*公告.*$"
    ])
    
    # 中性排除条件（包含这些的绝不能标记为中性）
    neutral_exclusion_patterns: List[str] = Field(default=[
        r".*\d+.*[万亿元].*",  # 任何包含具体金额的
        r".*[重大|重要|紧急|特别|异常].*",  # 任何强调重要性的
        r".*[风险|警示|提示|注意].*",  # 任何风险相关的
        r".*[增长|下降|变化|调整].*\d+.*%.*",  # 任何包含百分比变化的
        r".*[收购|出售|转让|投资].*\d+.*",  # 任何包含金额的交易
        r".*[业绩|利润|收入|营收].*[预|估|计].*"  # 任何业绩预测
    ])

class EfficiencyPatterns(BaseModel):
    """P2效率模式 - 在安全可靠前提下的优化"""
    
    # 扩展中性模式（在通过安全和可靠性检查后使用）
    extended_neutral_patterns: List[str] = Field(default=[
        r".*公司章程.*修订.*",
        r".*信息披露.*制度.*",
        r".*内部控制.*制度.*",
        r".*会计政策.*变更.*(?!.*重大).*",
        r".*独立董事.*意见.*",
        r".*审计.*意见.*(?!.*保留|.*否定).*"
    ])

class KnowledgeBaseVersion(BaseModel):
    """知识库版本信息"""
    version: str = Field(description="版本号，格式：major.minor.patch")
    code_version: str = Field(description="对应的代码版本")
    created_at: datetime = Field(default_factory=datetime.now)
    created_by: str = Field(default="VersionedKnowledgeConfig")
    description: str = Field(description="版本更新说明")
    
    # 性能基准（用于版本对比）
    performance_baseline: Optional[Dict] = Field(default=None, description="性能基准数据")
    
    # 兼容性信息
    compatible_student_versions: List[str] = Field(default=["1.0"], description="兼容的学生版本")
    requires_migration: bool = Field(default=False, description="是否需要数据迁移")

class VersionedKnowledgeConfig(BaseModel):
    """版本化知识库配置"""
    
    # 版本信息
    version_info: KnowledgeBaseVersion
    
    # 风险优先级配置
    risk_thresholds: RiskPriorityThresholds = Field(default_factory=RiskPriorityThresholds)
    
    # 三层风险模式
    safety_patterns: SafetyPatterns = Field(default_factory=SafetyPatterns)
    reliability_patterns: ReliabilityPatterns = Field(default_factory=ReliabilityPatterns)
    efficiency_patterns: EfficiencyPatterns = Field(default_factory=EfficiencyPatterns)
    
    # 系统配置
    system_config: Dict = Field(default={
        "enable_safety_first_mode": True,
        "enable_reliability_check": True,
        "enable_efficiency_optimization": True,
        "log_classification_trace": True,
        "auto_version_increment": False
    })
    
    class Config:
        extra = "forbid"
        validate_assignment = True

class VersionedKnowledgeManager:
    """版本化知识库管理器"""
    
    def __init__(self, config_dir: str = "E:/LLMData/knowledge_configs"):
        """
        初始化版本化知识库管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        self.current_config: Optional[VersionedKnowledgeConfig] = None
        self.version_history: List[Dict] = []
        
        logger.info(f"📚 版本化知识库管理器初始化完成")
        logger.info(f"📁 配置目录: {self.config_dir}")
    
    def create_initial_version(self, version: str = "1.0.0", 
                             code_version: str = "1.0.0") -> VersionedKnowledgeConfig:
        """创建初始版本配置"""
        
        version_info = KnowledgeBaseVersion(
            version=version,
            code_version=code_version,
            description="初始版本 - 基于风险优先级的规则配置",
            performance_baseline={
                "target_negative_recall": 0.95,
                "target_contamination_rate": 0.10,
                "target_neutral_recall": 0.80
            }
        )
        
        config = VersionedKnowledgeConfig(version_info=version_info)
        
        # 保存配置
        self._save_config(config)
        self.current_config = config
        
        logger.info(f"✅ 创建初始版本配置: v{version}")
        return config
    
    def load_version(self, version: str) -> Optional[VersionedKnowledgeConfig]:
        """加载指定版本的配置"""
        config_file = self.config_dir / f"knowledge_config_v{version}.json"
        
        if not config_file.exists():
            logger.error(f"❌ 版本配置文件不存在: {config_file}")
            return None
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            config = VersionedKnowledgeConfig.model_validate(config_data)
            self.current_config = config
            
            logger.info(f"✅ 加载版本配置: v{version}")
            return config
            
        except Exception as e:
            logger.error(f"❌ 加载版本配置失败: {e}")
            return None
    
    def load_latest_version(self) -> Optional[VersionedKnowledgeConfig]:
        """加载最新版本配置"""
        # 查找所有配置文件
        config_files = list(self.config_dir.glob("knowledge_config_v*.json"))
        
        if not config_files:
            logger.warning("⚠️ 没有找到配置文件，创建初始版本")
            return self.create_initial_version()
        
        # 按版本号排序，取最新的
        def version_key(file_path):
            version_str = file_path.stem.replace("knowledge_config_v", "")
            try:
                parts = version_str.split('.')
                return tuple(int(p) for p in parts)
            except:
                return (0, 0, 0)
        
        latest_file = max(config_files, key=version_key)
        version = latest_file.stem.replace("knowledge_config_v", "")
        
        return self.load_version(version)
    
    def increment_version(self, increment_type: str = "minor", 
                         description: str = "", 
                         code_version: str = None) -> VersionedKnowledgeConfig:
        """递增版本号"""
        if not self.current_config:
            logger.error("❌ 没有当前配置，无法递增版本")
            return None
        
        current_version = self.current_config.version_info.version
        parts = current_version.split('.')
        major, minor, patch = int(parts[0]), int(parts[1]), int(parts[2]) if len(parts) > 2 else 0
        
        if increment_type == "major":
            major += 1
            minor = 0
            patch = 0
        elif increment_type == "minor":
            minor += 1
            patch = 0
        else:  # patch
            patch += 1
        
        new_version = f"{major}.{minor}.{patch}"
        new_code_version = code_version or f"{major}.{minor}.{patch}"
        
        # 创建新版本配置
        new_version_info = KnowledgeBaseVersion(
            version=new_version,
            code_version=new_code_version,
            description=description or f"版本升级: {current_version} → {new_version}",
            performance_baseline=self.current_config.version_info.performance_baseline
        )
        
        # 复制当前配置并更新版本信息
        new_config_data = self.current_config.model_dump()
        new_config_data['version_info'] = new_version_info.model_dump()
        
        new_config = VersionedKnowledgeConfig.model_validate(new_config_data)
        
        # 保存新版本
        self._save_config(new_config)
        self.current_config = new_config
        
        logger.info(f"✅ 版本升级完成: v{current_version} → v{new_version}")
        return new_config
    
    def _save_config(self, config: VersionedKnowledgeConfig):
        """保存配置到文件"""
        version = config.version_info.version
        config_file = self.config_dir / f"knowledge_config_v{version}.json"
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config.model_dump(), f, ensure_ascii=False, indent=2, default=str)
            
            # 创建最新版本的符号链接
            latest_file = self.config_dir / "knowledge_config_latest.json"
            if latest_file.exists():
                latest_file.unlink()
            
            # 复制为最新版本
            with open(config_file, 'r', encoding='utf-8') as src, \
                 open(latest_file, 'w', encoding='utf-8') as dst:
                dst.write(src.read())
            
            logger.info(f"💾 配置已保存: {config_file}")
            
        except Exception as e:
            logger.error(f"❌ 保存配置失败: {e}")
            raise
    
    def get_version_history(self) -> List[Dict]:
        """获取版本历史"""
        config_files = list(self.config_dir.glob("knowledge_config_v*.json"))
        history = []
        
        for config_file in sorted(config_files):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                version_info = config_data.get('version_info', {})
                history.append({
                    'version': version_info.get('version'),
                    'code_version': version_info.get('code_version'),
                    'created_at': version_info.get('created_at'),
                    'description': version_info.get('description'),
                    'file_path': str(config_file)
                })
                
            except Exception as e:
                logger.error(f"读取版本历史失败 {config_file}: {e}")
        
        return history
    
    def get_current_version_info(self) -> Dict:
        """获取当前版本信息"""
        if not self.current_config:
            return {}
        
        return {
            "knowledge_version": self.current_config.version_info.version,
            "code_version": self.current_config.version_info.code_version,
            "created_at": self.current_config.version_info.created_at,
            "description": self.current_config.version_info.description,
            "risk_thresholds": self.current_config.risk_thresholds.model_dump(),
            "system_config": self.current_config.system_config
        }

def create_production_ready_config():
    """创建生产就绪的配置"""
    print("🏭 创建生产就绪的知识库配置...")
    
    manager = VersionedKnowledgeManager()
    
    # 创建初始版本
    config = manager.create_initial_version(
        version="1.0.0",
        code_version="1.0.0"
    )
    
    print(f"✅ 生产配置创建完成:")
    print(f"  知识库版本: v{config.version_info.version}")
    print(f"  代码版本: v{config.version_info.code_version}")
    print(f"  配置文件: {manager.config_dir}")
    
    # 显示版本历史
    history = manager.get_version_history()
    print(f"\n📋 版本历史:")
    for item in history:
        print(f"  v{item['version']} (代码v{item['code_version']}) - {item['description']}")
    
    return manager

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 创建生产配置
    create_production_ready_config()
