#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原子信号学习系统管理器
提供便捷的运行管理界面
"""

import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List
from atomic_signal_learning_system import AtomicSignalLearningSystem
from paths_config import paths_config, find_existing_path, get_path_str

class LearningSystemManager:
    """学习系统管理器"""
    
    def __init__(self):
        """初始化管理器"""
        self.version = "3.3.0"  # 🎛️ 管理器版本 - 路径配置更新
        self.learning_system = AtomicSignalLearningSystem()
        
        print(f"🎛️ 原子信号学习系统管理器 v{self.version}")
        print("="*60)
    
    def show_system_status(self):
        """显示系统状态"""
        print("\n📊 系统状态:")
        print("-"*40)
        
        # 显示工作目录结构
        workspace = self.learning_system.work_dir
        print(f"📁 工作目录: {workspace}")
        
        # 统计各目录文件数
        for name, path in self.learning_system.paths.items():
            if path.exists():
                file_count = len(list(path.glob("*")))
                print(f"   {name}: {file_count} 个文件")
            else:
                print(f"   {name}: 目录不存在")
        
        # 显示累计统计
        stats = self.learning_system.learning_stats
        print(f"\n📈 累计统计:")
        print(f"   总学习周期: {stats['total_cycles']}")
        print(f"   LLM文件处理: {stats['llm_answers_processed']}")
        print(f"   负召回案例: {stats['low_recall_cases_collected']}")
        print(f"   原子信号提取: {stats['atomic_signals_extracted']}")
        print(f"   词库更新: {stats['production_words_updated']}")
        if stats['last_update']:
            print(f"   最后更新: {stats['last_update']}")
    
    def run_single_cycle(self, llm_data_dir: str = None):
        """运行单次学习周期"""
        print(f"\n🔄 运行单次学习周期...")

        # 使用配置的路径
        if llm_data_dir is None:
            llm_data_dir = get_path_str("LLM_DATA_SOURCE")

        print(f"📁 LLM数据目录: {llm_data_dir}")
        
        start_time = time.time()
        result = self.learning_system.run_complete_learning_cycle(llm_data_dir)
        duration = time.time() - start_time
        
        if result.get("status") != "failed":
            print(f"\n✅ 学习周期 #{result['cycle_number']} 完成！")
            print(f"⏱️ 执行时间: {duration:.1f}秒")
            
            # 显示详细结果
            results = result['results']
            print(f"\n📊 本周期成果:")
            print(f"   步骤1 - LLM提取: {results['step1'].get('processed_files', 0)} 个文件")
            print(f"   步骤2 - 负召回案例: {results['step2']['total_cases']} 个")
            print(f"   步骤3 - 原子信号: {results['step3']['total_atomic_signals']} 个")
            print(f"   步骤4 - 词库更新: {results['step4']['added_words']} 个词汇")
            
            return result
        else:
            print(f"❌ 学习周期失败: {result.get('error', '未知错误')}")
            return None
    
    def run_continuous_learning(self, cycles: int = 5, interval_minutes: int = 60):
        """运行连续学习模式"""
        print(f"\n🔄 启动连续学习模式...")
        print(f"📊 计划周期数: {cycles}")
        print(f"⏱️ 间隔时间: {interval_minutes} 分钟")
        
        successful_cycles = 0
        
        for i in range(cycles):
            print(f"\n{'='*20} 周期 {i+1}/{cycles} {'='*20}")
            
            result = self.run_single_cycle()
            
            if result:
                successful_cycles += 1
                print(f"✅ 周期 {i+1} 成功完成")
            else:
                print(f"❌ 周期 {i+1} 执行失败")
            
            # 如果不是最后一个周期，等待间隔时间
            if i < cycles - 1:
                print(f"⏳ 等待 {interval_minutes} 分钟后开始下一周期...")
                time.sleep(interval_minutes * 60)
        
        print(f"\n🎉 连续学习完成！")
        print(f"📊 成功周期: {successful_cycles}/{cycles}")
        
        return successful_cycles
    
    def view_learning_history(self, last_n: int = 5):
        """查看学习历史"""
        print(f"\n📚 学习历史 (最近{last_n}个周期):")
        print("-"*60)
        
        logs_dir = self.learning_system.paths["logs"]
        log_files = sorted(logs_dir.glob("learning_cycle_*.json"), reverse=True)
        
        if not log_files:
            print("   暂无学习历史记录")
            return
        
        for i, log_file in enumerate(log_files[:last_n]):
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    cycle_data = json.load(f)
                
                cycle_num = cycle_data['cycle_number']
                start_time = datetime.fromisoformat(cycle_data['start_time']).strftime('%Y-%m-%d %H:%M:%S')
                duration = cycle_data['duration_seconds']
                
                results = cycle_data['results']
                
                print(f"📅 周期 #{cycle_num} - {start_time} (耗时: {duration:.1f}s)")
                print(f"   LLM处理: {results['step1'].get('processed_files', 0)} | "
                      f"负召回: {results['step2']['total_cases']} | "
                      f"原子信号: {results['step3']['total_atomic_signals']} | "
                      f"词库更新: {results['step4']['added_words']}")
                print()
                
            except Exception as e:
                print(f"   ⚠️ 读取日志失败: {log_file.name} - {e}")
    
    def export_production_lexicon(self, output_file: str = "final_production_lexicon.json"):
        """导出最新的生产词库"""
        print(f"\n📤 导出生产词库...")
        
        # 使用路径配置查找最新的生产词库文件
        try:
            latest_file = paths_config.find_latest_production_lexicon()
            production_dir = latest_file.parent
            lexicon_files = [latest_file]
        except FileNotFoundError:
            # 备用方案：在学习系统的生产词库目录中查找
            production_dir = self.learning_system.paths["production_lexicon"]
            lexicon_files = sorted(production_dir.glob("production_lexicon_*.json"), reverse=True)
        
        if not lexicon_files:
            print("❌ 没有找到生产词库文件")
            return None
        
        latest_file = lexicon_files[0]
        
        try:
            # 复制到指定位置
            import shutil
            shutil.copy2(latest_file, output_file)
            
            # 读取并显示统计信息
            with open(output_file, 'r', encoding='utf-8') as f:
                lexicon_data = json.load(f)
            
            print(f"✅ 生产词库已导出: {output_file}")
            print(f"📊 词库信息:")
            print(f"   版本: {lexicon_data.get('version', 'unknown')}")
            print(f"   描述: {lexicon_data.get('description', 'N/A')}")
            
            if 'word_lists' in lexicon_data:
                word_lists = lexicon_data['word_lists']
                print(f"   负面词汇: {len(word_lists.get('all_negative_words', []))} 个")
                print(f"   正面词汇: {len(word_lists.get('all_positive_words', []))} 个")
            
            if 'learning_history' in lexicon_data:
                print(f"   学习历史: {len(lexicon_data['learning_history'])} 次更新")
            
            return output_file
            
        except Exception as e:
            print(f"❌ 导出失败: {e}")
            return None
    
    def interactive_menu(self):
        """交互式菜单"""
        while True:
            print(f"\n🎛️ 原子信号学习系统管理器")
            print("="*40)
            print("1. 查看系统状态")
            print("2. 运行单次学习周期")
            print("3. 运行连续学习模式")
            print("4. 查看学习历史")
            print("5. 导出生产词库")
            print("6. 清理工作目录")
            print("0. 退出")
            print("-"*40)
            
            try:
                choice = input("请选择操作 (0-6): ").strip()
                
                if choice == "0":
                    print("👋 再见！")
                    break
                elif choice == "1":
                    self.show_system_status()
                elif choice == "2":
                    llm_dir = input("LLM数据目录 (回车使用默认): ").strip()
                    if not llm_dir:
                        llm_dir = None  # 使用配置的默认路径
                    self.run_single_cycle(llm_dir)
                elif choice == "3":
                    try:
                        cycles = int(input("周期数 (默认5): ").strip() or "5")
                        interval = int(input("间隔分钟 (默认60): ").strip() or "60")
                        self.run_continuous_learning(cycles, interval)
                    except ValueError:
                        print("❌ 请输入有效数字")
                elif choice == "4":
                    try:
                        n = int(input("显示最近几个周期 (默认5): ").strip() or "5")
                        self.view_learning_history(n)
                    except ValueError:
                        print("❌ 请输入有效数字")
                elif choice == "5":
                    output_file = input("输出文件名 (回车使用默认): ").strip()
                    if not output_file:
                        output_file = "final_production_lexicon.json"
                    self.export_production_lexicon(output_file)
                elif choice == "6":
                    confirm = input("确认清理工作目录？(y/N): ").strip().lower()
                    if confirm == 'y':
                        self.clean_workspace()
                else:
                    print("❌ 无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出程序")
                break
            except Exception as e:
                print(f"❌ 操作失败: {e}")
    
    def clean_workspace(self):
        """清理工作目录"""
        print("\n🧹 清理工作目录...")
        
        import shutil
        
        try:
            if self.learning_system.work_dir.exists():
                shutil.rmtree(self.learning_system.work_dir)
                print("✅ 工作目录已清理")
                
                # 重新初始化
                self.learning_system = AtomicSignalLearningSystem()
                print("✅ 系统已重新初始化")
            else:
                print("⚠️ 工作目录不存在")
                
        except Exception as e:
            print(f"❌ 清理失败: {e}")

def main():
    """主函数"""
    manager = LearningSystemManager()
    
    # 显示初始状态
    manager.show_system_status()
    
    # 启动交互式菜单
    manager.interactive_menu()

if __name__ == "__main__":
    main()
