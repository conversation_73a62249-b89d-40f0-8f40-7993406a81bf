#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强词库 - 包含争议词汇的"宁可错杀不可放过"版本
基于低召回率分析结果，补充关键漏掉词汇
"""

import json
import time
from pathlib import Path
from typing import Dict, List, Tuple
from dataclasses import dataclass
from paths_config import find_existing_path

@dataclass
class EnhancedClassificationResult:
    """增强分类结果"""
    category: str
    confidence: float
    method: str
    matched_signals: List[str]
    controversial_signals: List[str]  # 新增：争议词汇信号
    processing_time: float
    explanation: str

class EnhancedFinancialLexicon:
    """增强金融词库 - 宁可错杀不可放过版本"""
    
    def __init__(self, optimized_lexicon_file: str = "optimized_financial_lexicon.json"):
        """
        初始化增强词库
        
        Args:
            optimized_lexicon_file: 优化词库文件路径
        """
        self.version = "2.5.1"  # 🛡️ 争议词汇补充版本 - 路径配置更新
        self.optimized_lexicon = None
        self.load_optimized_lexicon(optimized_lexicon_file)
        
        # 基于分析结果的关键补充词汇
        self.critical_missing_words = {
            # 高频漏掉的负面词汇
            "E_TERRIBLE": {
                "争议词汇_负面优先": [
                    "减少",    # 虽然有正面用法，但负面风险更高
                    "下降",    # 同理：股价下降(负面) vs 风险下降(正面)
                    "降低",    # 同理：利润降低(负面) vs 成本降低(正面)
                    "缩减",    # 同理：业务缩减(负面) vs 债务缩减(正面)
                ],
                "基础负面动词": [
                    "流失",    # 客户流失、人才流失
                    "恶化",    # 状况恶化、关系恶化
                    "萎缩",    # 市场萎缩、业务萎缩
                    "衰退",    # 行业衰退、业绩衰退
                ],
                "基础负面形容词": [
                    "困难",    # 经营困难、财务困难
                    "严峻",    # 形势严峻、挑战严峻
                    "疲软",    # 市场疲软、需求疲软
                    "低迷",    # 行业低迷、股价低迷
                ],
                "基础负面名词": [
                    "压力",    # 竞争压力、资金压力
                    "挑战",    # 市场挑战、经营挑战
                    "危机",    # 财务危机、信任危机
                    "问题",    # 质量问题、管理问题
                ]
            },
            
            # 漏掉的正面词汇
            "A_EXCELLENT": {
                "基础正面动词": [
                    "突破",    # 技术突破、业绩突破
                    "签署",    # 签署协议、签署合同
                    "获得",    # 获得订单、获得认证
                    "实现",    # 实现增长、实现目标
                ],
                "基础正面形容词": [
                    "重大",    # 重大突破、重大合作
                    "显著",    # 显著增长、显著改善
                    "优异",    # 优异表现、优异成绩
                    "卓越",    # 卓越业绩、卓越品质
                ],
                "基础正面名词": [
                    "突破",    # 技术突破、市场突破
                    "合作",    # 战略合作、深度合作
                    "协议",    # 合作协议、战略协议
                    "成果",    # 研发成果、经营成果
                ]
            },
            
            # 中等负面词汇
            "D_BAD": {
                "中等负面词汇": [
                    "不足",    # 资金不足、能力不足
                    "缺失",    # 内控缺失、人才缺失
                    "滞后",    # 发展滞后、技术滞后
                    "受阻",    # 进展受阻、销售受阻
                ]
            }
        }
        
        print(f"🛡️ 增强金融词库 v{self.version} 初始化完成")
        print(f"📊 补充词汇统计:")
        total_added = 0
        for category, subcategories in self.critical_missing_words.items():
            category_total = sum(len(words) for words in subcategories.values())
            total_added += category_total
            print(f"   {category}: {category_total} 个词汇")
        print(f"   总计补充: {total_added} 个关键词汇")
    
    def load_optimized_lexicon(self, file_path: str):
        """加载优化词库"""
        if Path(file_path).exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.optimized_lexicon = json.load(f)
                print(f"✅ 加载优化词库: {file_path}")
            except Exception as e:
                print(f"⚠️ 加载优化词库失败: {e}")
        else:
            print(f"⚠️ 优化词库文件不存在: {file_path}")
    
    def get_all_negative_words(self) -> List[str]:
        """获取所有负面词汇（包括补充的）"""
        all_negative = []
        
        # 从优化词库获取
        if self.optimized_lexicon:
            categories = self.optimized_lexicon.get("categories", {})
            for category in ["E_TERRIBLE", "D_BAD"]:
                if category in categories:
                    atomic_signals = categories[category].get("atomic_signals", {})
                    for length_key, words in atomic_signals.items():
                        all_negative.extend(words)
        
        # 添加补充的关键词汇
        for category in ["E_TERRIBLE", "D_BAD"]:
            if category in self.critical_missing_words:
                for subcategory, words in self.critical_missing_words[category].items():
                    all_negative.extend(words)
        
        return list(set(all_negative))  # 去重
    
    def get_all_positive_words(self) -> List[str]:
        """获取所有正面词汇（包括补充的）"""
        all_positive = []
        
        # 从优化词库获取
        if self.optimized_lexicon:
            categories = self.optimized_lexicon.get("categories", {})
            for category in ["A_EXCELLENT", "B_GOOD"]:
                if category in categories:
                    atomic_signals = categories[category].get("atomic_signals", {})
                    for length_key, words in atomic_signals.items():
                        all_positive.extend(words)
        
        # 添加补充的关键词汇
        if "A_EXCELLENT" in self.critical_missing_words:
            for subcategory, words in self.critical_missing_words["A_EXCELLENT"].items():
                all_positive.extend(words)
        
        return list(set(all_positive))  # 去重
    
    def get_controversial_words(self) -> List[str]:
        """获取争议词汇列表"""
        controversial = []
        if "E_TERRIBLE" in self.critical_missing_words:
            controversial.extend(
                self.critical_missing_words["E_TERRIBLE"].get("争议词汇_负面优先", [])
            )
        return controversial
    
    def classify_enhanced(self, text: str) -> EnhancedClassificationResult:
        """
        增强分类方法 - 宁可错杀不可放过
        
        Args:
            text: 输入文本
            
        Returns:
            增强分类结果
        """
        start_time = time.time()
        
        if not text.strip():
            return EnhancedClassificationResult(
                category="NEUTRAL",
                confidence=0.0,
                method="enhanced_aggressive",
                matched_signals=[],
                controversial_signals=[],
                processing_time=time.time() - start_time,
                explanation="文本为空"
            )
        
        # 获取词汇列表
        negative_words = self.get_all_negative_words()
        positive_words = self.get_all_positive_words()
        controversial_words = self.get_controversial_words()
        
        # 检查匹配
        matched_negative = [word for word in negative_words if word in text]
        matched_positive = [word for word in positive_words if word in text]
        matched_controversial = [word for word in controversial_words if word in text]
        
        # 激进分类策略
        negative_score = len(matched_negative) + len(matched_controversial) * 1.5  # 争议词汇权重更高
        positive_score = len(matched_positive)
        
        # 决策逻辑 - 宁可错杀不可放过
        if negative_score > 0:
            # 只要有负面信号就优先考虑
            if negative_score >= 3 or matched_controversial:
                category = "E_TERRIBLE"
                confidence = min(0.95, 0.8 + negative_score * 0.05)
            else:
                category = "D_BAD"
                confidence = min(0.90, 0.7 + negative_score * 0.05)
            
            explanation = f"检测到{len(matched_negative)}个负面信号"
            if matched_controversial:
                explanation += f"，{len(matched_controversial)}个争议信号（按负面处理）"
                
        elif positive_score > negative_score and positive_score >= 2:
            # 正面信号明显且足够多
            category = "A_EXCELLENT"
            confidence = min(0.90, 0.7 + positive_score * 0.05)
            explanation = f"检测到{positive_score}个正面信号"
            
        elif positive_score > 0:
            # 有正面信号但不够强
            category = "B_GOOD"
            confidence = min(0.80, 0.6 + positive_score * 0.05)
            explanation = f"检测到{positive_score}个弱正面信号"
            
        else:
            # 没有明确信号
            category = "C_NEUTRAL"
            confidence = 0.5
            explanation = "未检测到明确的情感信号"
        
        all_matched = matched_negative + matched_positive
        
        return EnhancedClassificationResult(
            category=category,
            confidence=confidence,
            method="enhanced_aggressive",
            matched_signals=all_matched[:5],  # 最多显示5个
            controversial_signals=matched_controversial,
            processing_time=time.time() - start_time,
            explanation=explanation
        )
    
    def export_enhanced_lexicon(self, output_file: str = "enhanced_financial_lexicon.json") -> str:
        """导出增强词库"""
        enhanced_lexicon = {
            "version": self.version,
            "description": "增强金融词库 - 宁可错杀不可放过版本",
            "strategy": "高召回率，争议词汇按负面处理",
            "base_lexicon": "optimized_financial_lexicon.json",
            "enhancements": {
                "critical_missing_words": self.critical_missing_words,
                "total_added_words": sum(
                    len(words) for category in self.critical_missing_words.values()
                    for words in category.values()
                ),
                "controversial_words_policy": "按负面处理，宁可错杀不可放过"
            },
            "word_lists": {
                "all_negative_words": self.get_all_negative_words(),
                "all_positive_words": self.get_all_positive_words(),
                "controversial_words": self.get_controversial_words()
            },
            "statistics": {
                "total_negative_words": len(self.get_all_negative_words()),
                "total_positive_words": len(self.get_all_positive_words()),
                "controversial_words": len(self.get_controversial_words())
            }
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(enhanced_lexicon, f, ensure_ascii=False, indent=2)
        
        print(f"💾 增强词库已导出: {output_file}")
        return output_file

def test_enhanced_lexicon():
    """测试增强词库效果"""
    print("🧪 测试增强词库效果")
    print("="*60)
    
    # 创建增强词库
    lexicon = EnhancedFinancialLexicon()
    
    # 测试案例 - 重点测试之前漏检的案例
    test_cases = [
        # 之前漏检的负面案例
        "订单减少客户流失",
        "公司经营遇到困难", 
        "利润出现减少",
        "市场竞争加剧压力增大",
        
        # 争议词汇测试
        "亏损减少风险降低",  # 正面上下文中的"减少"
        "收入减少利润下降",  # 负面上下文中的"减少"
        
        # 之前漏检的正面案例
        "技术创新取得突破",
        "签署重大合作协议",
        
        # 中性案例
        "股东大会审议年度报告",
        "董事会决议通过议案",
        
        # 边界案例
        "业务发展存在压力",
        "面临市场挑战困难"
    ]
    
    print(f"📊 增强分类测试结果:")
    print(f"{'文本':<35} {'分类':<12} {'置信度':<8} {'争议词':<10} {'说明'}")
    print("-"*100)
    
    for text in test_cases:
        result = lexicon.classify_enhanced(text)
        controversial_str = ",".join(result.controversial_signals) if result.controversial_signals else "无"
        
        print(f"{text[:33]:<35} {result.category:<12} "
              f"{result.confidence:<8.3f} {controversial_str:<10} {result.explanation}")
    
    # 导出增强词库
    output_file = lexicon.export_enhanced_lexicon()
    
    print(f"\n📈 增强词库统计:")
    stats = lexicon.export_enhanced_lexicon("temp_stats.json")
    with open("temp_stats.json", 'r', encoding='utf-8') as f:
        stats_data = json.load(f)
    
    print(f"   负面词汇总数: {stats_data['statistics']['total_negative_words']}")
    print(f"   正面词汇总数: {stats_data['statistics']['total_positive_words']}")
    print(f"   争议词汇总数: {stats_data['statistics']['controversial_words']}")
    print(f"   补充词汇总数: {stats_data['enhancements']['total_added_words']}")
    
    # 清理临时文件
    Path("temp_stats.json").unlink()
    
    print(f"\n✅ 增强词库测试完成！")
    print(f"📁 增强词库文件: {output_file}")

def main():
    """主函数"""
    print("🛡️ 增强金融词库 - 宁可错杀不可放过版本")
    print("="*60)
    
    # 运行测试
    test_enhanced_lexicon()

if __name__ == "__main__":
    main()
