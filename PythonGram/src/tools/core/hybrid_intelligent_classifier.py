#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合智能分类器
结合原子信号分类器和语义上下文分析器，实现最高精度的分类
"""

import json
import re
import time
from typing import Dict, List, Tuple
from dataclasses import dataclass

# 导入组件
from semantic_context_analyzer import SemanticContextAnalyzer

@dataclass
class HybridClassificationResult:
    """混合分类结果"""
    category: str
    confidence: float
    method: str  # atomic_signal, semantic_context, hybrid
    matched_signals: List[str]
    context_analysis: Dict
    rule_trace: List[str]
    processing_time: float
    explanation: str

class HybridIntelligentClassifier:
    """混合智能分类器"""
    
    def __init__(self, lexicon_file: str = "atomic_signal_lexicon.json"):
        """初始化混合分类器"""
        self.lexicon_file = lexicon_file
        self.atomic_rules = []
        self.semantic_analyzer = SemanticContextAnalyzer()
        
        # 分类方法优先级
        self.method_priority = {
            'atomic_signal': 3,      # 原子信号优先级最高
            'semantic_context': 2,   # 语义上下文次之
            'hybrid': 1              # 混合方法最低
        }
        
        self.load_atomic_rules()
        
        # 统计信息
        self.classification_stats = {
            'total_classifications': 0,
            'atomic_signal_used': 0,
            'semantic_context_used': 0,
            'hybrid_used': 0,
            'uncertain_cases': 0
        }
    
    def load_atomic_rules(self):
        """加载原子信号规则"""
        try:
            with open(self.lexicon_file, 'r', encoding='utf-8') as f:
                lexicon_data = json.load(f)
            
            self.atomic_rules = lexicon_data.get('rules', [])
            
            # 优化规则：过滤载体词汇规则
            carrier_words = self.semantic_analyzer.carrier_words
            filtered_rules = []
            
            for rule in self.atomic_rules:
                component = rule.get('component', '')
                # 如果是纯载体词汇，跳过（交给语义分析器处理）
                if component not in carrier_words:
                    filtered_rules.append(rule)
            
            self.atomic_rules = filtered_rules
            print(f"✅ 加载原子信号规则: {len(self.atomic_rules)} 条（已过滤载体词汇）")
            
        except Exception as e:
            print(f"❌ 加载原子规则失败: {e}")
            self.atomic_rules = []
    
    def classify(self, title: str, content: str = "") -> HybridClassificationResult:
        """
        混合智能分类
        
        策略：
        1. 优先使用原子信号分类（高精度）
        2. 如果有载体词汇，使用语义上下文分析
        3. 混合两种方法的结果
        """
        start_time = time.time()
        self.classification_stats['total_classifications'] += 1
        
        full_text = f"{title} {content}".strip()
        if not full_text:
            return self._create_result(
                category="UNCERTAIN",
                confidence=0.0,
                method="empty_text",
                matched_signals=[],
                context_analysis={},
                rule_trace=["文本为空"],
                start_time=start_time,
                explanation="输入文本为空"
            )
        
        # 1. 尝试原子信号分类
        atomic_result = self._classify_with_atomic_signals(full_text)
        
        # 2. 尝试语义上下文分析
        semantic_result = self._classify_with_semantic_context(full_text)
        
        # 3. 决策逻辑
        final_result = self._make_hybrid_decision(atomic_result, semantic_result, full_text, start_time)
        
        return final_result
    
    def _classify_with_atomic_signals(self, text: str) -> Dict:
        """使用原子信号分类"""
        matched_rules = []
        
        for rule in self.atomic_rules:
            if re.search(rule['pattern'], text, re.IGNORECASE):
                matched_rules.append(rule)
        
        if not matched_rules:
            return {
                'category': None,
                'confidence': 0.0,
                'matched_signals': [],
                'rule_trace': ['未匹配任何原子信号']
            }
        
        # 选择最高优先级的规则
        priority_weights = {
            'E_TERRIBLE': 5, 'D_BAD': 4, 'A_EXCELLENT': 3, 'B_GOOD': 2, 'C_NEUTRAL': 1
        }
        
        best_rule = max(matched_rules, key=lambda x: (
            priority_weights.get(x['category'], 0),
            x.get('confidence', 0)
        ))
        
        matched_signals = [rule['component'] for rule in matched_rules]
        
        # 如果有多个同类别信号，提升置信度
        same_category_matches = [r for r in matched_rules if r['category'] == best_rule['category']]
        confidence_boost = min(0.1, len(same_category_matches) * 0.02)
        final_confidence = min(0.98, best_rule.get('confidence', 0.8) + confidence_boost)
        
        return {
            'category': best_rule['category'],
            'confidence': final_confidence,
            'matched_signals': matched_signals,
            'rule_trace': [f"原子信号匹配: {best_rule['component']} → {best_rule['category']}"]
        }
    
    def _classify_with_semantic_context(self, text: str) -> Dict:
        """使用语义上下文分析"""
        try:
            result = self.semantic_analyzer.classify_with_context(text)
            return {
                'category': result['category'],
                'confidence': result['confidence'],
                'context_analysis': result.get('context_analysis', {}),
                'carriers': result.get('carriers', []),
                'explanation': result.get('explanation', ''),
                'rule_trace': [f"语义上下文分析: {result.get('explanation', '')}"]
            }
        except Exception as e:
            return {
                'category': None,
                'confidence': 0.0,
                'context_analysis': {},
                'carriers': [],
                'explanation': f"语义分析失败: {e}",
                'rule_trace': [f"语义分析失败: {e}"]
            }
    
    def _make_hybrid_decision(self, atomic_result: Dict, semantic_result: Dict, 
                            text: str, start_time: float) -> HybridClassificationResult:
        """混合决策逻辑"""
        
        atomic_category = atomic_result.get('category')
        semantic_category = semantic_result.get('category')
        
        # 决策规则
        if atomic_category and atomic_result.get('confidence', 0) >= 0.8:
            # 高置信度原子信号，直接采用
            self.classification_stats['atomic_signal_used'] += 1
            return self._create_result(
                category=atomic_category,
                confidence=atomic_result['confidence'],
                method="atomic_signal",
                matched_signals=atomic_result['matched_signals'],
                context_analysis={},
                rule_trace=atomic_result['rule_trace'],
                start_time=start_time,
                explanation=f"高置信度原子信号: {atomic_result['matched_signals'][0] if atomic_result['matched_signals'] else 'unknown'}"
            )
        
        elif semantic_category and semantic_result.get('carriers'):
            # 有载体词汇，优先使用语义分析
            if atomic_category and atomic_category != semantic_category:
                # 两种方法结果不一致，需要混合决策
                return self._resolve_conflict(atomic_result, semantic_result, text, start_time)
            else:
                # 使用语义分析结果
                self.classification_stats['semantic_context_used'] += 1
                return self._create_result(
                    category=semantic_category,
                    confidence=semantic_result['confidence'],
                    method="semantic_context",
                    matched_signals=[],
                    context_analysis=semantic_result.get('context_analysis', {}),
                    rule_trace=semantic_result['rule_trace'],
                    start_time=start_time,
                    explanation=semantic_result.get('explanation', '')
                )
        
        elif atomic_category:
            # 只有原子信号结果
            self.classification_stats['atomic_signal_used'] += 1
            return self._create_result(
                category=atomic_category,
                confidence=atomic_result['confidence'],
                method="atomic_signal",
                matched_signals=atomic_result['matched_signals'],
                context_analysis={},
                rule_trace=atomic_result['rule_trace'],
                start_time=start_time,
                explanation=f"原子信号匹配: {atomic_result['matched_signals'][0] if atomic_result['matched_signals'] else 'unknown'}"
            )
        
        elif semantic_category:
            # 只有语义分析结果
            self.classification_stats['semantic_context_used'] += 1
            return self._create_result(
                category=semantic_category,
                confidence=semantic_result['confidence'],
                method="semantic_context",
                matched_signals=[],
                context_analysis=semantic_result.get('context_analysis', {}),
                rule_trace=semantic_result['rule_trace'],
                start_time=start_time,
                explanation=semantic_result.get('explanation', '')
            )
        
        else:
            # 两种方法都无法分类
            self.classification_stats['uncertain_cases'] += 1
            return self._create_result(
                category="UNCERTAIN",
                confidence=0.0,
                method="no_match",
                matched_signals=[],
                context_analysis={},
                rule_trace=["原子信号和语义分析都无法分类"],
                start_time=start_time,
                explanation="未找到明确的分类信号"
            )
    
    def _resolve_conflict(self, atomic_result: Dict, semantic_result: Dict, 
                         text: str, start_time: float) -> HybridClassificationResult:
        """解决分类冲突"""
        self.classification_stats['hybrid_used'] += 1
        
        atomic_category = atomic_result['category']
        semantic_category = semantic_result['category']
        
        # 冲突解决策略
        priority_weights = {
            'E_TERRIBLE': 5, 'D_BAD': 4, 'A_EXCELLENT': 3, 'B_GOOD': 2, 'C_NEUTRAL': 1
        }
        
        atomic_priority = priority_weights.get(atomic_category, 0)
        semantic_priority = priority_weights.get(semantic_category, 0)
        
        # 如果原子信号是高风险类别（E/D），优先采用
        if atomic_category in ['E_TERRIBLE', 'D_BAD'] and atomic_result['confidence'] >= 0.7:
            final_category = atomic_category
            final_confidence = atomic_result['confidence'] * 0.9  # 略微降低置信度
            explanation = f"冲突解决: 原子信号({atomic_category}) vs 语义分析({semantic_category})，采用高风险原子信号"
        
        # 如果语义分析置信度明显更高
        elif semantic_result['confidence'] - atomic_result['confidence'] > 0.2:
            final_category = semantic_category
            final_confidence = semantic_result['confidence'] * 0.9
            explanation = f"冲突解决: 语义分析置信度更高({semantic_result['confidence']:.3f} vs {atomic_result['confidence']:.3f})"
        
        # 否则按优先级选择
        elif atomic_priority >= semantic_priority:
            final_category = atomic_category
            final_confidence = atomic_result['confidence'] * 0.8
            explanation = f"冲突解决: 原子信号优先级更高({atomic_category} vs {semantic_category})"
        
        else:
            final_category = semantic_category
            final_confidence = semantic_result['confidence'] * 0.8
            explanation = f"冲突解决: 语义分析优先级更高({semantic_category} vs {atomic_category})"
        
        combined_trace = atomic_result['rule_trace'] + semantic_result['rule_trace']
        combined_trace.append(explanation)
        
        return self._create_result(
            category=final_category,
            confidence=final_confidence,
            method="hybrid",
            matched_signals=atomic_result['matched_signals'],
            context_analysis=semantic_result.get('context_analysis', {}),
            rule_trace=combined_trace,
            start_time=start_time,
            explanation=explanation
        )
    
    def _create_result(self, category: str, confidence: float, method: str,
                      matched_signals: List[str], context_analysis: Dict,
                      rule_trace: List[str], start_time: float, explanation: str) -> HybridClassificationResult:
        """创建分类结果"""
        return HybridClassificationResult(
            category=category,
            confidence=confidence,
            method=method,
            matched_signals=matched_signals,
            context_analysis=context_analysis,
            rule_trace=rule_trace,
            processing_time=time.time() - start_time,
            explanation=explanation
        )
    
    def get_classification_stats(self) -> Dict:
        """获取分类统计"""
        total = self.classification_stats['total_classifications']
        if total == 0:
            return {"message": "暂无分类数据"}
        
        return {
            "总分类次数": total,
            "原子信号分类": f"{self.classification_stats['atomic_signal_used']} ({self.classification_stats['atomic_signal_used']/total*100:.1f}%)",
            "语义上下文分类": f"{self.classification_stats['semantic_context_used']} ({self.classification_stats['semantic_context_used']/total*100:.1f}%)",
            "混合决策分类": f"{self.classification_stats['hybrid_used']} ({self.classification_stats['hybrid_used']/total*100:.1f}%)",
            "不确定分类": f"{self.classification_stats['uncertain_cases']} ({self.classification_stats['uncertain_cases']/total*100:.1f}%)",
            "确定性比例": f"{(total - self.classification_stats['uncertain_cases'])/total*100:.1f}%"
        }

def test_hybrid_classifier():
    """测试混合智能分类器"""
    print("🧪 测试混合智能分类器")
    print("="*80)
    
    classifier = HybridIntelligentClassifier()
    
    test_cases = [
        # 原子信号测试
        ("公司股票进入退市整理期", "E_TERRIBLE"),
        ("公司预计净利润同比由盈转亏", "D_BAD"),
        
        # 语义上下文测试
        ("年度报告显示公司净利润大幅增长", "A_EXCELLENT"),
        ("年度报告披露公司严重亏损", "E_TERRIBLE"),
        ("股东大会审议通过", "C_NEUTRAL"),
        
        # 混合测试（可能有冲突）
        ("年度报告显示退市风险", "E_TERRIBLE"),
        ("业绩报告亏损扩大", "D_BAD"),
        
        # 复杂案例
        ("公司召开股东大会审议年度报告，显示净利润增长25%", "A_EXCELLENT"),
        ("董事会决议公司面临严重财务困难", "E_TERRIBLE"),
        
        # 边界案例
        ("公司发布一般性公告", "C_NEUTRAL"),
    ]
    
    print("分类测试结果:")
    print(f"{'文本':<40} {'期望':<12} {'预测':<12} {'置信度':<8} {'方法':<15} {'结果'}")
    print("-"*110)
    
    correct_predictions = 0
    total_predictions = len(test_cases)
    
    for text, expected in test_cases:
        result = classifier.classify(text)
        is_correct = result.category == expected
        
        if is_correct:
            correct_predictions += 1
        
        result_icon = "✅" if is_correct else "❌"
        print(f"{text[:38]:<40} {expected:<12} {result.category:<12} "
              f"{result.confidence:<8.3f} {result.method:<15} {result_icon}")
    
    accuracy = correct_predictions / total_predictions
    print(f"\n📊 混合分类器准确率: {accuracy:.3f} ({correct_predictions}/{total_predictions})")
    
    # 显示分类统计
    stats = classifier.get_classification_stats()
    print(f"\n📈 分类方法统计:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    return classifier, accuracy

if __name__ == "__main__":
    classifier, accuracy = test_hybrid_classifier()
    
    print(f"\n🎯 混合智能分类器总结:")
    print(f"   ✅ 结合了原子信号的高精度和语义分析的智能化")
    print(f"   ✅ 解决了载体词汇的语义依赖问题")
    print(f"   ✅ 提供了冲突解决机制")
    print(f"   ✅ 实现了 {accuracy:.1%} 的分类准确率")
    
    if accuracy >= 0.9:
        print(f"🏆 性能评级: 优秀")
    elif accuracy >= 0.8:
        print(f"👍 性能评级: 良好")
    else:
        print(f"⚠️ 性能评级: 需要改进")
