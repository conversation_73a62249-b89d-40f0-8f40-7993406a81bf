#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本化知识库构建器
从LLM分析结果中"反编译"并构建带版本管理的教师知识库
"""

import json
import logging
import time
from pathlib import Path
from datetime import datetime
from collections import Counter, defaultdict
from typing import Dict, List, Set, Optional
import re
import pandas as pd

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class VersionedKnowledgeBuilder:
    """版本化知识库构建器"""
    
    def __init__(self, output_dir: str = "E:/LLMData/knowledge_bases"):
        """
        初始化版本化知识库构建器
        
        Args:
            output_dir: 知识库输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 版本管理
        self.current_version = "1.0"
        self.version_history = []
        
        # 分析字段
        self.analysis_fields = ['summary', 'keyFactors', 'riskFactors', 'eventTags', 'reasoning']
        
        # 评级映射
        self.rating_mapping = {
            '优': 'A_EXCELLENT',
            '好': 'B_GOOD', 
            '中': 'C_NEUTRAL',
            '差': 'D_POOR',
            '劣': 'E_TERRIBLE'
        }
        
        logger.info(f"📚 版本化知识库构建器初始化完成")
        logger.info(f"📁 输出目录: {self.output_dir}")
    
    def load_existing_version_history(self) -> List[Dict]:
        """加载现有版本历史"""
        version_file = self.output_dir / "version_history.json"
        
        if version_file.exists():
            try:
                with open(version_file, 'r', encoding='utf-8') as f:
                    self.version_history = json.load(f)
                
                if self.version_history:
                    # 获取最新版本号
                    latest_version = self.version_history[-1]['version']
                    version_parts = latest_version.split('.')
                    major, minor = int(version_parts[0]), int(version_parts[1])
                    self.current_version = f"{major}.{minor + 1}"
                
                logger.info(f"📖 加载版本历史: {len(self.version_history)} 个版本")
                logger.info(f"🔢 当前版本: {self.current_version}")
                
            except Exception as e:
                logger.error(f"❌ 加载版本历史失败: {e}")
                self.version_history = []
        
        return self.version_history
    
    def analyze_llm_data(self, analysis_dir: str, sample_size: Optional[int] = None) -> Dict:
        """
        分析LLM数据，提取知识模式
        
        Args:
            analysis_dir: LLM分析结果目录
            sample_size: 采样大小，None表示全部分析
            
        Returns:
            分析结果
        """
        logger.info(f"🔍 开始分析LLM数据: {analysis_dir}")
        
        analysis_path = Path(analysis_dir)
        if not analysis_path.exists():
            logger.error(f"❌ 分析目录不存在: {analysis_path}")
            return {}
        
        json_files = list(analysis_path.glob("*.json"))
        logger.info(f"📁 找到 {len(json_files)} 个JSON文件")
        
        if sample_size and sample_size < len(json_files):
            import random
            random.seed(42)
            json_files = random.sample(json_files, sample_size)
            logger.info(f"🎯 采样 {len(json_files)} 个文件进行分析")
        
        # 按评级分组收集数据
        rating_data = defaultdict(list)
        field_coverage = Counter()
        processing_stats = {
            'total_files': len(json_files),
            'processed_files': 0,
            'failed_files': 0,
            'rating_distribution': Counter()
        }
        
        for file_path in json_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 获取评级
                rating = data.get('ratingLevel', '')
                if rating not in self.rating_mapping:
                    continue
                
                rating_key = self.rating_mapping[rating]
                processing_stats['rating_distribution'][rating] += 1
                
                # 提取分析字段
                extracted_data = {
                    'file_name': file_path.name,
                    'title': data.get('title', ''),
                    'rating': rating
                }
                
                # 提取各个分析字段
                for field in self.analysis_fields:
                    if field in data:
                        field_coverage[field] += 1
                        extracted_data[field] = data[field]
                
                rating_data[rating_key].append(extracted_data)
                processing_stats['processed_files'] += 1
                
            except Exception as e:
                logger.debug(f"处理文件失败 {file_path}: {e}")
                processing_stats['failed_files'] += 1
        
        logger.info(f"📊 数据分析完成:")
        logger.info(f"  处理文件: {processing_stats['processed_files']}/{processing_stats['total_files']}")
        logger.info(f"  评级分布: {dict(processing_stats['rating_distribution'])}")
        logger.info(f"  字段覆盖: {dict(field_coverage)}")
        
        return {
            'rating_data': dict(rating_data),
            'processing_stats': processing_stats,
            'field_coverage': dict(field_coverage)
        }
    
    def extract_knowledge_patterns(self, rating_data: Dict) -> Dict:
        """
        从评级数据中提取知识模式
        
        Args:
            rating_data: 按评级分组的数据
            
        Returns:
            提取的知识模式
        """
        logger.info("🧠 开始提取知识模式...")
        
        knowledge_patterns = {}
        
        for rating_key, data_list in rating_data.items():
            logger.info(f"  分析 {rating_key}: {len(data_list)} 个样本")
            
            # 收集所有文本内容
            all_texts = []
            keyword_counter = Counter()
            risk_factors = Counter()
            event_tags = Counter()
            
            for item in data_list:
                # 收集文本
                text_parts = []
                if 'summary' in item:
                    text_parts.append(str(item['summary']))
                if 'reasoning' in item:
                    text_parts.append(str(item['reasoning']))
                
                all_texts.extend(text_parts)
                
                # 收集关键因素
                if 'keyFactors' in item:
                    factors = item['keyFactors']
                    if isinstance(factors, list):
                        keyword_counter.update(factors)
                    elif isinstance(factors, str):
                        keyword_counter.update([factors])
                
                # 收集风险因素
                if 'riskFactors' in item:
                    risks = item['riskFactors']
                    if isinstance(risks, list):
                        risk_factors.update(risks)
                    elif isinstance(risks, str):
                        risk_factors.update([risks])
                
                # 收集事件标签
                if 'eventTags' in item:
                    tags = item['eventTags']
                    if isinstance(tags, list):
                        event_tags.update(tags)
                    elif isinstance(tags, str):
                        event_tags.update([tags])
            
            # 提取高频关键词
            top_keywords = [word for word, count in keyword_counter.most_common(20)]
            top_risks = [risk for risk, count in risk_factors.most_common(10)]
            top_events = [event for event, count in event_tags.most_common(10)]
            
            # 从文本中提取模式（简单的关键词提取）
            text_patterns = self._extract_text_patterns(all_texts)
            
            knowledge_patterns[rating_key] = {
                'sample_count': len(data_list),
                'top_keywords': top_keywords,
                'top_risk_factors': top_risks,
                'top_event_tags': top_events,
                'text_patterns': text_patterns,
                'confidence_score': min(1.0, len(data_list) / 100)  # 基于样本数量的置信度
            }
        
        return knowledge_patterns
    
    def _extract_text_patterns(self, texts: List[str], top_k: int = 15) -> List[str]:
        """从文本中提取模式"""
        if not texts:
            return []
        
        # 简单的关键词提取（可以后续用jieba等工具增强）
        word_counter = Counter()
        
        for text in texts:
            if not text:
                continue
            
            # 提取中文词汇（简单版本）
            chinese_words = re.findall(r'[\u4e00-\u9fff]+', text)
            
            # 过滤长度
            valid_words = [word for word in chinese_words if 2 <= len(word) <= 6]
            word_counter.update(valid_words)
        
        # 返回高频词汇
        return [word for word, count in word_counter.most_common(top_k)]
    
    def build_versioned_knowledge_base(self, analysis_dir: str, 
                                     version_note: str = "", 
                                     sample_size: Optional[int] = None) -> str:
        """
        构建版本化知识库
        
        Args:
            analysis_dir: LLM分析结果目录
            version_note: 版本说明
            sample_size: 采样大小
            
        Returns:
            知识库版本号
        """
        logger.info(f"🏗️ 开始构建知识库版本 {self.current_version}")
        
        # 加载版本历史
        self.load_existing_version_history()
        
        # 分析LLM数据
        analysis_result = self.analyze_llm_data(analysis_dir, sample_size)
        if not analysis_result:
            logger.error("❌ 数据分析失败")
            return ""
        
        # 提取知识模式
        knowledge_patterns = self.extract_knowledge_patterns(analysis_result['rating_data'])
        
        # 构建知识库
        knowledge_base = {
            'metadata': {
                'version': self.current_version,
                'created_at': datetime.now().isoformat(),
                'created_by': 'VersionedKnowledgeBuilder',
                'version_note': version_note or f"自动生成版本 {self.current_version}",
                'source_data_path': analysis_dir,
                'sample_size': sample_size,
                'processing_stats': analysis_result['processing_stats'],
                'field_coverage': analysis_result['field_coverage']
            },
            'knowledge_patterns': knowledge_patterns,
            'version_info': {
                'major_version': self.current_version.split('.')[0],
                'minor_version': self.current_version.split('.')[1],
                'is_stable': True,
                'compatibility': ['enhanced_student_c_v1.0+']
            }
        }
        
        # 保存知识库
        kb_filename = f"teacher_knowledge_v{self.current_version}.json"
        kb_path = self.output_dir / kb_filename
        
        with open(kb_path, 'w', encoding='utf-8') as f:
            json.dump(knowledge_base, f, ensure_ascii=False, indent=2)
        
        # 更新版本历史
        version_record = {
            'version': self.current_version,
            'created_at': datetime.now().isoformat(),
            'file_path': str(kb_path),
            'note': version_note,
            'stats': analysis_result['processing_stats']
        }
        
        self.version_history.append(version_record)
        
        # 保存版本历史
        with open(self.output_dir / "version_history.json", 'w', encoding='utf-8') as f:
            json.dump(self.version_history, f, ensure_ascii=False, indent=2)
        
        # 创建最新版本的符号链接
        latest_path = self.output_dir / "teacher_knowledge_latest.json"
        if latest_path.exists():
            latest_path.unlink()
        
        # 复制为最新版本
        with open(kb_path, 'r', encoding='utf-8') as src, \
             open(latest_path, 'w', encoding='utf-8') as dst:
            dst.write(src.read())
        
        logger.info(f"✅ 知识库版本 {self.current_version} 构建完成")
        logger.info(f"📁 保存路径: {kb_path}")
        logger.info(f"🔗 最新版本: {latest_path}")
        
        # 导出Excel文件供人工查阅
        self.export_to_excel(knowledge_base, analysis_result['rating_data'])

        return self.current_version

    def export_to_excel(self, knowledge_base: Dict, rating_data: Dict):
        """
        导出知识库到Excel文件供人工查阅

        Args:
            knowledge_base: 知识库数据
            rating_data: 原始评级数据
        """
        logger.info("📊 开始导出Excel文件...")

        version = knowledge_base['metadata']['version']
        excel_path = self.output_dir / f"teacher_knowledge_v{version}_review.xlsx"

        try:
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:

                # 1. 概览工作表
                self._create_overview_sheet(writer, knowledge_base)

                # 2. 各评级的详细分析工作表
                for rating_key, data_list in rating_data.items():
                    self._create_rating_detail_sheet(writer, rating_key, data_list, knowledge_base)

                # 3. 知识模式汇总工作表
                self._create_patterns_summary_sheet(writer, knowledge_base)

                # 4. 原始数据样本工作表
                self._create_raw_samples_sheet(writer, rating_data)

            logger.info(f"✅ Excel文件导出完成: {excel_path}")

        except Exception as e:
            logger.error(f"❌ Excel导出失败: {e}")

    def _create_overview_sheet(self, writer: pd.ExcelWriter, knowledge_base: Dict):
        """创建概览工作表"""
        metadata = knowledge_base['metadata']
        patterns = knowledge_base['knowledge_patterns']

        # 基本信息
        overview_data = [
            ['知识库版本', metadata['version']],
            ['创建时间', metadata['created_at'][:19]],
            ['创建者', metadata['created_by']],
            ['版本说明', metadata['version_note']],
            ['数据源路径', metadata['source_data_path']],
            ['采样大小', metadata.get('sample_size', 'N/A')],
            ['', ''],
            ['处理统计', ''],
            ['总文件数', metadata['processing_stats']['total_files']],
            ['处理成功', metadata['processing_stats']['processed_files']],
            ['处理失败', metadata['processing_stats']['failed_files']],
            ['成功率', f"{metadata['processing_stats']['processed_files']/metadata['processing_stats']['total_files']*100:.1f}%"],
            ['', ''],
            ['评级分布', ''],
        ]

        # 添加评级分布
        for rating, count in metadata['processing_stats']['rating_distribution'].items():
            overview_data.append([f'  {rating}', count])

        overview_data.extend([
            ['', ''],
            ['字段覆盖率', ''],
        ])

        # 添加字段覆盖率
        for field, count in metadata['field_coverage'].items():
            coverage_rate = count / metadata['processing_stats']['processed_files'] * 100
            overview_data.append([f'  {field}', f"{count} ({coverage_rate:.1f}%)"])

        overview_data.extend([
            ['', ''],
            ['知识模式统计', ''],
        ])

        # 添加知识模式统计
        for rating_key, pattern in patterns.items():
            overview_data.append([f'  {rating_key}', f"{pattern['sample_count']} 样本 (置信度: {pattern['confidence_score']:.2f})"])

        df_overview = pd.DataFrame(overview_data, columns=['项目', '值'])
        df_overview.to_excel(writer, sheet_name='概览', index=False)

    def _create_rating_detail_sheet(self, writer: pd.ExcelWriter, rating_key: str,
                                  data_list: List[Dict], knowledge_base: Dict):
        """创建评级详细分析工作表"""
        pattern = knowledge_base['knowledge_patterns'][rating_key]

        # 创建详细数据
        detail_data = []

        # 添加模式摘要
        detail_data.extend([
            ['模式摘要', ''],
            ['样本数量', pattern['sample_count']],
            ['置信度', pattern['confidence_score']],
            ['', ''],
            ['高频关键词 (前20)', ''],
        ])

        for i, keyword in enumerate(pattern['top_keywords'], 1):
            detail_data.append([f'{i:2d}. {keyword}', ''])

        detail_data.extend([
            ['', ''],
            ['主要风险因素 (前10)', ''],
        ])

        for i, risk in enumerate(pattern['top_risk_factors'], 1):
            detail_data.append([f'{i:2d}. {risk}', ''])

        detail_data.extend([
            ['', ''],
            ['常见事件标签 (前10)', ''],
        ])

        for i, event in enumerate(pattern['top_event_tags'], 1):
            detail_data.append([f'{i:2d}. {event}', ''])

        detail_data.extend([
            ['', ''],
            ['文本模式 (前15)', ''],
        ])

        for i, text_pattern in enumerate(pattern['text_patterns'], 1):
            detail_data.append([f'{i:2d}. {text_pattern}', ''])

        df_detail = pd.DataFrame(detail_data, columns=['项目', '值'])

        # 限制工作表名称长度
        sheet_name = rating_key[:20] + '_详情' if len(rating_key) > 20 else rating_key + '_详情'
        df_detail.to_excel(writer, sheet_name=sheet_name, index=False)

    def _create_patterns_summary_sheet(self, writer: pd.ExcelWriter, knowledge_base: Dict):
        """创建知识模式汇总工作表"""
        patterns = knowledge_base['knowledge_patterns']

        summary_data = []

        for rating_key, pattern in patterns.items():
            # 为每个评级创建一行汇总
            keywords_str = ', '.join(pattern['top_keywords'][:10])
            risks_str = ', '.join(pattern['top_risk_factors'][:5])
            events_str = ', '.join(pattern['top_event_tags'][:5])
            text_patterns_str = ', '.join(pattern['text_patterns'][:5])

            summary_data.append({
                '评级': rating_key,
                '样本数': pattern['sample_count'],
                '置信度': round(pattern['confidence_score'], 3),
                '关键词 (前10)': keywords_str,
                '风险因素 (前5)': risks_str,
                '事件标签 (前5)': events_str,
                '文本模式 (前5)': text_patterns_str
            })

        df_summary = pd.DataFrame(summary_data)
        df_summary.to_excel(writer, sheet_name='模式汇总', index=False)

    def _create_raw_samples_sheet(self, writer: pd.ExcelWriter, rating_data: Dict):
        """创建原始数据样本工作表"""
        # 从每个评级中选择一些样本展示
        sample_data = []

        for rating_key, data_list in rating_data.items():
            # 每个评级最多取10个样本
            samples = data_list[:10] if len(data_list) > 10 else data_list

            for item in samples:
                sample_data.append({
                    '评级': rating_key,
                    '原始评级': item.get('rating', ''),
                    '文件名': item.get('file_name', ''),
                    '标题': item.get('title', '')[:100] + '...' if len(item.get('title', '')) > 100 else item.get('title', ''),
                    '摘要': str(item.get('summary', ''))[:200] + '...' if len(str(item.get('summary', ''))) > 200 else str(item.get('summary', '')),
                    '关键因素': str(item.get('keyFactors', ''))[:100] + '...' if len(str(item.get('keyFactors', ''))) > 100 else str(item.get('keyFactors', '')),
                    '风险因素': str(item.get('riskFactors', ''))[:100] + '...' if len(str(item.get('riskFactors', ''))) > 100 else str(item.get('riskFactors', '')),
                    '事件标签': str(item.get('eventTags', ''))[:100] + '...' if len(str(item.get('eventTags', ''))) > 100 else str(item.get('eventTags', ''))
                })

        df_samples = pd.DataFrame(sample_data)
        df_samples.to_excel(writer, sheet_name='原始样本', index=False)

    def list_versions(self) -> List[Dict]:
        """列出所有版本"""
        self.load_existing_version_history()
        return self.version_history

    def get_version_info(self, version: str) -> Optional[Dict]:
        """获取指定版本信息"""
        for record in self.version_history:
            if record['version'] == version:
                return record
        return None

def main():
    """主函数"""
    print("📚 版本化知识库构建器")
    print("="*50)
    
    # 创建构建器
    builder = VersionedKnowledgeBuilder()
    
    # 构建知识库
    version = builder.build_versioned_knowledge_base(
        analysis_dir="E:/LLMData/analysis_output",
        version_note="初始版本 - 从LLM分析结果反编译",
        sample_size=1000  # 先用1000个样本测试
    )
    
    if version:
        print(f"🎉 知识库版本 {version} 构建成功！")
        
        # 显示版本历史
        versions = builder.list_versions()
        print(f"\n📋 版本历史:")
        for v in versions:
            print(f"  v{v['version']}: {v['note']} ({v['created_at'][:10]})")
    else:
        print("❌ 知识库构建失败")

if __name__ == "__main__":
    main()
