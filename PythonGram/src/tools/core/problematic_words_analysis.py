#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题词汇分析 - 检查v2.7中可能导致误判的词汇
"""

import json

def analyze_problematic_words():
    """分析可能有问题的词汇"""
    print("🔍 v2.7版本问题词汇分析")
    print("="*50)
    
    # 加载v2.7词库
    try:
        with open('E:/LLMData/research_archive/01_raw_data/production_lexicon.json', 'r', encoding='utf-8') as f:
            v27 = json.load(f)
        
        negative_words = v27.get('word_lists', {}).get('all_negative_words', [])
        print(f"✅ 加载v2.7词库成功，负面词汇: {len(negative_words)} 个")
        
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return
    
    # 1. 查找包含逗号的复合词汇
    print(f"\n📋 包含逗号的复合词汇:")
    comma_words = [word for word in negative_words if ',' in word]
    if comma_words:
        for word in comma_words:
            print(f"   - \"{word}\" ⚠️ 复合词汇可能过于宽泛")
    else:
        print("   无复合词汇")
    
    # 2. 查找包含正面含义的词汇
    print(f"\n⚠️ 包含正面含义的词汇:")
    positive_indicators = ['发展', '增长', '提升', '改善', '优化', '成功', '突破', '创新']
    problematic = []
    for word in negative_words:
        for indicator in positive_indicators:
            if indicator in word:
                problematic.append((word, indicator))
    
    if problematic:
        for word, indicator in problematic:
            print(f"   - \"{word}\" (包含正面词汇: {indicator})")
    else:
        print("   未发现包含正面含义的负面词汇")
    
    # 3. 查找争议词汇
    print(f"\n🔍 争议词汇检查:")
    controversial_candidates = ['减持', '下降', '降低', '缩减', '减少', '出售', '转让']
    found_controversial = [word for word in negative_words if word in controversial_candidates]
    
    if found_controversial:
        for word in found_controversial:
            print(f"   - \"{word}\" ⚠️ 可能在某些语境下误判")
            
            # 提供误判示例
            examples = {
                '减持': ['减持计划完成', '减持比例较小'],
                '下降': ['成本下降', '风险下降'],
                '降低': ['风险降低', '成本降低'],
                '缩减': ['债务缩减', '成本缩减'],
                '减少': ['亏损减少', '风险减少'],
                '出售': ['资产出售用于发展', '非核心资产出售'],
                '转让': ['股权转让优化结构']
            }
            
            if word in examples:
                print(f"      可能误判案例: {examples[word]}")
    else:
        print("   未发现明显争议词汇")
    
    # 4. 查找过长或过短的词汇
    print(f"\n📏 词汇长度分析:")
    too_short = [word for word in negative_words if len(word) == 1]
    too_long = [word for word in negative_words if len(word) > 6]
    
    if too_short:
        print(f"   过短词汇 (1字符): {too_short} ⚠️ 可能误判")
    
    if too_long:
        print(f"   过长词汇 (>6字符):")
        for word in too_long[:10]:  # 只显示前10个
            print(f"      - \"{word}\"")
        if len(too_long) > 10:
            print(f"      ... 还有 {len(too_long) - 10} 个")
    
    # 5. 模拟测试案例
    print(f"\n🧪 模拟测试案例:")
    test_cases = [
        "公司业务发展良好",
        "大股东减持套现",
        "减持计划顺利完成", 
        "成本下降提升利润",
        "风险降低经营改善",
        "亏损减少业绩好转",
        "收到监管处罚通知",
        "债务重组进展顺利"
    ]
    
    for text in test_cases:
        matched_words = [word for word in negative_words if word in text]
        if matched_words:
            # 判断是否可能误判
            has_positive_context = any(pos in text for pos in ['良好', '顺利', '提升', '改善', '好转'])
            warning = " ⚠️ 可能误判" if has_positive_context and matched_words else ""
            print(f"   \"{text}\" → 匹配: {matched_words}{warning}")
    
    # 6. 建议
    print(f"\n💡 改进建议:")
    print("="*30)
    
    if comma_words:
        print("1. 复合词汇问题:")
        print("   - 建议拆分复合词汇，避免过于宽泛的匹配")
        for word in comma_words:
            parts = word.split(',')
            print(f"   - \"{word}\" → 拆分为: {parts}")
    
    if found_controversial:
        print("2. 争议词汇处理:")
        print("   - 建议将争议词汇移至专门的争议词汇列表")
        print("   - 或者使用更精确的表达，如:")
        suggestions = {
            '减持': '大股东减持、股东减持套现',
            '下降': '业绩下降、收入下降',
            '降低': '利润降低、评级降低',
            '减少': '利润减少、收入减少'
        }
        for word in found_controversial:
            if word in suggestions:
                print(f"     \"{word}\" → {suggestions[word]}")
    
    print("3. 总体建议:")
    print("   - 优先保留明确负面的词汇")
    print("   - 将争议词汇单独处理，降低权重")
    print("   - 避免包含正面含义的复合词汇")
    print("   - 定期基于人工标注结果优化词库")

if __name__ == "__main__":
    analyze_problematic_words()
