#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原子信号拆解学习系统 - 集成管理平台
实现从LLM答案到生产词库的完整闭环学习流程
"""

import json
import jieba
import pandas as pd
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Set, Tuple
from collections import Counter, defaultdict
import logging
from paths_config import paths_config, find_existing_path, get_path_str

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AtomicSignalLearningSystem:
    """原子信号拆解学习系统"""
    
    def __init__(self, work_dir: str = None):
        """
        初始化学习系统

        Args:
            work_dir: 工作目录，如果为None则使用配置的路径
        """
        self.version = "3.3.0"  # 🔄 闭环学习系统版本 - 路径配置更新

        # 使用新的路径配置
        if work_dir is None:
            self.work_dir = find_existing_path("LEARNING_WORKSPACE")
        else:
            self.work_dir = Path(work_dir)
        self.work_dir.mkdir(exist_ok=True)
        
        # 系统组件路径
        self.paths = {
            "llm_answers": self.work_dir / "llm_answers",
            "low_recall_cases": self.work_dir / "low_recall_cases", 
            "segmented_texts": self.work_dir / "segmented_texts",
            "atomic_signals": self.work_dir / "atomic_signals",
            "production_lexicon": self.work_dir / "production_lexicon",
            "logs": self.work_dir / "logs"
        }
        
        # 创建目录结构
        for path in self.paths.values():
            path.mkdir(exist_ok=True)
        
        # 学习统计
        self.learning_stats = {
            "total_cycles": 0,
            "llm_answers_processed": 0,
            "low_recall_cases_collected": 0,
            "atomic_signals_extracted": 0,
            "production_words_updated": 0,
            "last_update": None
        }
        
        logger.info(f"🔄 原子信号拆解学习系统 v{self.version} 初始化完成")
        logger.info(f"📁 工作目录: {self.work_dir}")
    
    def step1_extract_from_llm_answers(self, llm_data_dir: str = None) -> Dict:
        """
        步骤1: 从LLM答案中提取最小单元词库

        Args:
            llm_data_dir: LLM分析结果目录，如果为None则使用配置的路径

        Returns:
            提取结果统计
        """
        logger.info("🔍 步骤1: 从LLM答案中提取最小单元词库...")

        # 使用配置的路径
        if llm_data_dir is None:
            llm_data_dir = get_path_str("LLM_DATA_SOURCE")

        llm_data_path = Path(llm_data_dir)
        if not llm_data_path.exists():
            logger.warning(f"LLM数据目录不存在: {llm_data_dir}")
            return {"status": "failed", "reason": "数据目录不存在"}
        
        # 扫描JSON文件
        json_files = list(llm_data_path.glob("*.json"))
        logger.info(f"📁 发现 {len(json_files)} 个LLM结果文件")
        
        extracted_words = defaultdict(list)
        processed_count = 0
        
        for json_file in json_files[:1000]:  # 限制处理数量，避免过载
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 提取关键信息
                rating = data.get('ratingLevel', '')
                content_fields = [
                    data.get('content', ''),
                    data.get('summary', ''),
                    data.get('reasoning', ''),
                    ' '.join(data.get('keyFactors', [])),
                    ' '.join(data.get('riskFactors', []))
                ]
                
                full_text = ' '.join([text for text in content_fields if text])
                
                # 使用jieba分词提取原子信号
                words = list(jieba.cut(full_text))
                valid_words = [
                    word for word in words 
                    if len(word) >= 2 and re.match(r'^[\u4e00-\u9fff]+$', word)
                ]
                
                # 按评级分类
                if rating in ['优', '好', '中', '差', '劣']:
                    category_map = {'优': 'A_EXCELLENT', '好': 'B_GOOD', '中': 'C_NEUTRAL', '差': 'D_BAD', '劣': 'E_TERRIBLE'}
                    category = category_map[rating]
                    extracted_words[category].extend(valid_words)
                
                processed_count += 1
                
            except Exception as e:
                logger.debug(f"处理文件失败 {json_file}: {e}")
                continue
        
        # 统计和保存结果
        extraction_result = {
            "timestamp": datetime.now().isoformat(),
            "processed_files": processed_count,
            "extracted_categories": {}
        }
        
        for category, words in extracted_words.items():
            word_freq = Counter(words)
            top_words = dict(word_freq.most_common(100))  # 每类保留前100个
            extraction_result["extracted_categories"][category] = {
                "total_words": len(words),
                "unique_words": len(word_freq),
                "top_words": top_words
            }
        
        # 保存到文件
        output_file = self.paths["llm_answers"] / f"extraction_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(extraction_result, f, ensure_ascii=False, indent=2)
        
        self.learning_stats["llm_answers_processed"] += processed_count
        logger.info(f"✅ 步骤1完成: 处理了{processed_count}个文件，提取了{sum(len(words) for words in extracted_words.values())}个词汇")
        
        return extraction_result
    
    def step2_collect_low_recall_cases(self, test_results: List[Dict] = None) -> Dict:
        """
        步骤2: 从负召回案例中汇集关键语句
        
        Args:
            test_results: 测试结果列表
            
        Returns:
            收集结果统计
        """
        logger.info("📋 步骤2: 收集负召回案例...")
        
        # 如果没有提供测试结果，使用默认的已知案例
        if test_results is None:
            test_results = [
                {"text": "订单减少客户流失", "expected": "NEGATIVE", "actual": "NEUTRAL"},
                {"text": "公司经营遇到困难", "expected": "NEGATIVE", "actual": "NEUTRAL"},
                {"text": "利润出现减少", "expected": "NEGATIVE", "actual": "NEUTRAL"},
                {"text": "市场竞争加剧压力增大", "expected": "NEGATIVE", "actual": "POSITIVE"},
                {"text": "技术创新取得突破", "expected": "POSITIVE", "actual": "NEUTRAL"},
                {"text": "签署重大合作协议", "expected": "POSITIVE", "actual": "NEUTRAL"},
            ]
        
        # 筛选负召回案例
        low_recall_cases = []
        for result in test_results:
            if result["expected"] != result["actual"]:
                low_recall_cases.append({
                    "text": result["text"],
                    "expected_category": result["expected"],
                    "actual_category": result["actual"],
                    "error_type": self._classify_error_type(result["expected"], result["actual"]),
                    "timestamp": datetime.now().isoformat()
                })
        
        # 保存关键语句文本
        key_sentences_file = self.paths["low_recall_cases"] / f"key_sentences_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(key_sentences_file, 'w', encoding='utf-8') as f:
            f.write("负召回案例关键语句集合\n")
            f.write("="*50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"案例数量: {len(low_recall_cases)}\n\n")
            
            for i, case in enumerate(low_recall_cases, 1):
                f.write(f"{i}. {case['text']}\n")
                f.write(f"   预期: {case['expected_category']} | 实际: {case['actual_category']}\n")
                f.write(f"   错误类型: {case['error_type']}\n\n")
        
        # 保存详细数据
        cases_data_file = self.paths["low_recall_cases"] / f"cases_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(cases_data_file, 'w', encoding='utf-8') as f:
            json.dump(low_recall_cases, f, ensure_ascii=False, indent=2)
        
        self.learning_stats["low_recall_cases_collected"] += len(low_recall_cases)
        logger.info(f"✅ 步骤2完成: 收集了{len(low_recall_cases)}个负召回案例")
        
        return {
            "total_cases": len(low_recall_cases),
            "key_sentences_file": str(key_sentences_file),
            "cases_data_file": str(cases_data_file)
        }
    
    def step3_segment_texts(self, key_sentences_file: str) -> Dict:
        """
        步骤3: 使用分词软件对关键语句进行分词
        
        Args:
            key_sentences_file: 关键语句文件路径
            
        Returns:
            分词结果统计
        """
        logger.info("✂️ 步骤3: 对关键语句进行分词...")
        
        if not Path(key_sentences_file).exists():
            logger.error(f"关键语句文件不存在: {key_sentences_file}")
            return {"status": "failed", "reason": "文件不存在"}
        
        # 读取关键语句
        with open(key_sentences_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取语句（跳过标题和说明）
        sentences = []
        for line in content.split('\n'):
            if re.match(r'^\d+\.\s+', line):  # 匹配 "1. 文本内容" 格式
                sentence = re.sub(r'^\d+\.\s+', '', line).strip()
                if sentence:
                    sentences.append(sentence)
        
        # 对每个语句进行分词
        segmentation_results = []
        all_atomic_signals = []
        
        for sentence in sentences:
            # 使用jieba分词
            words = list(jieba.cut(sentence))
            
            # 过滤有效的原子信号
            atomic_signals = [
                word for word in words
                if len(word) >= 2 and re.match(r'^[\u4e00-\u9fff]+$', word)
            ]
            
            segmentation_results.append({
                "original_sentence": sentence,
                "jieba_words": words,
                "atomic_signals": atomic_signals,
                "signal_count": len(atomic_signals)
            })
            
            all_atomic_signals.extend(atomic_signals)
        
        # 统计原子信号频次
        signal_frequency = Counter(all_atomic_signals)
        
        # 保存分词结果
        output_file = self.paths["segmented_texts"] / f"segmentation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        segmentation_data = {
            "timestamp": datetime.now().isoformat(),
            "total_sentences": len(sentences),
            "segmentation_results": segmentation_results,
            "atomic_signal_frequency": dict(signal_frequency.most_common(50)),
            "total_atomic_signals": len(all_atomic_signals),
            "unique_atomic_signals": len(signal_frequency)
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(segmentation_data, f, ensure_ascii=False, indent=2)
        
        self.learning_stats["atomic_signals_extracted"] += len(all_atomic_signals)
        logger.info(f"✅ 步骤3完成: 分词{len(sentences)}个语句，提取{len(all_atomic_signals)}个原子信号")
        
        return {
            "total_sentences": len(sentences),
            "total_atomic_signals": len(all_atomic_signals),
            "unique_signals": len(signal_frequency),
            "output_file": str(output_file)
        }
    
    def step4_purify_and_update_lexicon(self, segmentation_file: str, existing_lexicon_file: str = "enhanced_financial_lexicon.json") -> Dict:
        """
        步骤4: 原子纯化并更新生产词库
        
        Args:
            segmentation_file: 分词结果文件
            existing_lexicon_file: 现有词库文件
            
        Returns:
            更新结果统计
        """
        logger.info("🔬 步骤4: 原子纯化并更新生产词库...")
        
        # 加载分词结果
        with open(segmentation_file, 'r', encoding='utf-8') as f:
            segmentation_data = json.load(f)
        
        # 加载现有词库
        existing_lexicon = {}
        if Path(existing_lexicon_file).exists():
            with open(existing_lexicon_file, 'r', encoding='utf-8') as f:
                existing_lexicon = json.load(f)
        
        # 提取高频原子信号
        signal_frequency = segmentation_data.get("atomic_signal_frequency", {})
        high_freq_signals = {
            word: freq for word, freq in signal_frequency.items()
            if freq >= 2  # 至少出现2次
        }
        
        # 原子纯化：基于上下文分析信号倾向
        purified_signals = self._purify_atomic_signals(
            segmentation_data["segmentation_results"], 
            high_freq_signals
        )
        
        # 更新词库
        updated_lexicon = self._update_production_lexicon(existing_lexicon, purified_signals)
        
        # 保存带时间戳的历史版本
        timestamp_file = self.paths["production_lexicon"] / f"production_lexicon_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(timestamp_file, 'w', encoding='utf-8') as f:
            json.dump(updated_lexicon, f, ensure_ascii=False, indent=2)

        # 保存固定名称的当前版本
        current_file = self.paths["production_lexicon"] / "production_lexicon.json"
        with open(current_file, 'w', encoding='utf-8') as f:
            json.dump(updated_lexicon, f, ensure_ascii=False, indent=2)

        # 同时更新开发目录的版本
        with open("production_lexicon.json", 'w', encoding='utf-8') as f:
            json.dump(updated_lexicon, f, ensure_ascii=False, indent=2)
        
        added_words = sum(len(signals) for signals in purified_signals.values())
        self.learning_stats["production_words_updated"] += added_words
        
        logger.info(f"✅ 步骤4完成: 纯化了{len(high_freq_signals)}个信号，更新了{added_words}个词汇到生产词库")
        
        return {
            "high_freq_signals": len(high_freq_signals),
            "purified_signals": {category: len(signals) for category, signals in purified_signals.items()},
            "added_words": added_words,
            "output_file": str(output_file)
        }
    
    def _classify_error_type(self, expected: str, actual: str) -> str:
        """分类错误类型"""
        if expected == "NEGATIVE" and actual == "NEUTRAL":
            return "负面漏检"
        elif expected == "POSITIVE" and actual == "NEUTRAL":
            return "正面漏检"
        elif expected == "NEGATIVE" and actual == "POSITIVE":
            return "负面误判为正面"
        elif expected == "POSITIVE" and actual == "NEGATIVE":
            return "正面误判为负面"
        else:
            return "其他错误"
    
    def _purify_atomic_signals(self, segmentation_results: List[Dict], high_freq_signals: Dict) -> Dict:
        """原子信号纯化"""
        purified = {
            "E_TERRIBLE": [],
            "D_BAD": [],
            "A_EXCELLENT": [],
            "B_GOOD": [],
            "C_NEUTRAL": []
        }
        
        # 基于上下文分析信号倾向
        for result in segmentation_results:
            sentence = result["original_sentence"]
            atomic_signals = result["atomic_signals"]
            
            # 简单的倾向性分析
            if any(neg_word in sentence for neg_word in ["减少", "下降", "困难", "流失", "压力", "问题"]):
                # 负面上下文
                for signal in atomic_signals:
                    if signal in high_freq_signals and signal not in ["公司", "市场", "技术"]:  # 排除中性词
                        if any(extreme_word in sentence for extreme_word in ["严重", "巨大", "重大"]):
                            purified["E_TERRIBLE"].append(signal)
                        else:
                            purified["D_BAD"].append(signal)
            
            elif any(pos_word in sentence for pos_word in ["突破", "增长", "合作", "协议", "成功"]):
                # 正面上下文
                for signal in atomic_signals:
                    if signal in high_freq_signals:
                        purified["A_EXCELLENT"].append(signal)
        
        # 去重
        for category in purified:
            purified[category] = list(set(purified[category]))
        
        return purified
    
    def _update_production_lexicon(self, existing_lexicon: Dict, purified_signals: Dict) -> Dict:
        """更新生产词库"""
        updated_lexicon = existing_lexicon.copy()
        
        # 更新版本号
        current_version = updated_lexicon.get("version", "2.5.0")
        version_parts = current_version.split(".")
        version_parts[1] = str(int(version_parts[1]) + 1)  # 增加次版本号
        updated_lexicon["version"] = ".".join(version_parts)
        
        # 更新描述
        updated_lexicon["description"] = f"自学习更新的金融词库 - {datetime.now().strftime('%Y-%m-%d')}"
        updated_lexicon["last_learning_update"] = datetime.now().isoformat()
        
        # 添加新的原子信号
        if "word_lists" not in updated_lexicon:
            updated_lexicon["word_lists"] = {"all_negative_words": [], "all_positive_words": []}
        
        # 更新负面词汇
        negative_words = set(updated_lexicon["word_lists"].get("all_negative_words", []))
        negative_words.update(purified_signals.get("E_TERRIBLE", []))
        negative_words.update(purified_signals.get("D_BAD", []))
        updated_lexicon["word_lists"]["all_negative_words"] = list(negative_words)
        
        # 更新正面词汇
        positive_words = set(updated_lexicon["word_lists"].get("all_positive_words", []))
        positive_words.update(purified_signals.get("A_EXCELLENT", []))
        positive_words.update(purified_signals.get("B_GOOD", []))
        updated_lexicon["word_lists"]["all_positive_words"] = list(positive_words)
        
        # 添加学习历史
        if "learning_history" not in updated_lexicon:
            updated_lexicon["learning_history"] = []
        
        updated_lexicon["learning_history"].append({
            "timestamp": datetime.now().isoformat(),
            "added_signals": purified_signals,
            "learning_cycle": self.learning_stats["total_cycles"] + 1
        })
        
        return updated_lexicon
    
    def run_complete_learning_cycle(self, llm_data_dir: str = None) -> Dict:
        """运行完整的学习周期"""
        logger.info("🔄 开始完整的原子信号学习周期...")
        
        cycle_start_time = datetime.now()
        cycle_results = {}
        
        try:
            # 步骤1: 从LLM答案提取
            step1_result = self.step1_extract_from_llm_answers(llm_data_dir)
            cycle_results["step1"] = step1_result
            
            # 步骤2: 收集负召回案例
            step2_result = self.step2_collect_low_recall_cases()
            cycle_results["step2"] = step2_result
            
            # 步骤3: 分词处理
            step3_result = self.step3_segment_texts(step2_result["key_sentences_file"])
            cycle_results["step3"] = step3_result
            
            # 步骤4: 纯化和更新词库
            step4_result = self.step4_purify_and_update_lexicon(step3_result["output_file"])
            cycle_results["step4"] = step4_result
            
            # 更新统计
            self.learning_stats["total_cycles"] += 1
            self.learning_stats["last_update"] = datetime.now().isoformat()
            
            cycle_duration = (datetime.now() - cycle_start_time).total_seconds()
            
            # 保存周期报告
            cycle_report = {
                "cycle_number": self.learning_stats["total_cycles"],
                "start_time": cycle_start_time.isoformat(),
                "duration_seconds": cycle_duration,
                "results": cycle_results,
                "cumulative_stats": self.learning_stats
            }
            
            report_file = self.paths["logs"] / f"learning_cycle_{self.learning_stats['total_cycles']:03d}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(cycle_report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"🎉 学习周期{self.learning_stats['total_cycles']}完成！耗时{cycle_duration:.1f}秒")
            logger.info(f"📁 周期报告: {report_file}")
            
            return cycle_report
            
        except Exception as e:
            logger.error(f"❌ 学习周期执行失败: {e}")
            return {"status": "failed", "error": str(e)}

def main():
    """主函数"""
    print("🔄 原子信号拆解学习系统")
    print("="*60)
    
    # 创建学习系统
    learning_system = AtomicSignalLearningSystem()
    
    # 运行完整学习周期
    result = learning_system.run_complete_learning_cycle()
    
    if result.get("status") != "failed":
        print(f"\n🎉 学习周期完成！")
        print(f"📊 周期统计:")
        print(f"   周期编号: {result['cycle_number']}")
        print(f"   执行时间: {result['duration_seconds']:.1f}秒")
        print(f"   LLM文件处理: {result['results']['step1'].get('processed_files', 0)}个")
        print(f"   负召回案例: {result['results']['step2']['total_cases']}个")
        print(f"   原子信号提取: {result['results']['step3']['total_atomic_signals']}个")
        print(f"   词库更新: {result['results']['step4']['added_words']}个词汇")
        
        print(f"\n📈 累计统计:")
        stats = result['cumulative_stats']
        for key, value in stats.items():
            if key != 'last_update':
                print(f"   {key}: {value}")
    else:
        print(f"❌ 学习周期失败: {result.get('error', '未知错误')}")

if __name__ == "__main__":
    main()
