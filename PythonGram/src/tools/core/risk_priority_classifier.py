#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于风险优先级的分类器
P0安全性 > P1可靠性 > P2效率的三层防护体系
"""

import re
import time
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from versioned_knowledge_config import VersionedKnowledgeManager, VersionedKnowledgeConfig

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """风险等级"""
    CRITICAL = ("CRITICAL", "严重风险")
    HIGH = ("HIGH", "高风险")
    MEDIUM = ("MEDIUM", "中等风险")
    LOW = ("LOW", "低风险")
    
    def __init__(self, level: str, description: str):
        self.level = level
        self.description = description

class ClassificationResult(Enum):
    """分类结果"""
    NEGATIVE = ("负面", "明显负面公告")
    POSITIVE = ("正面", "明显正面公告")
    NEUTRAL = ("中性", "明确中性公告")
    UNCERTAIN = ("不确定", "需要LLM进一步分析")
    
    def __init__(self, label: str, description: str):
        self.label = label
        self.description = description

@dataclass
class RiskPriorityResult:
    """风险优先级分类结果"""
    result: ClassificationResult
    confidence: float
    risk_level: RiskLevel
    matched_patterns: List[str]
    processing_time: float
    classification_trace: List[str]
    
    # 版本信息
    knowledge_version: str
    code_version: str
    
    # 风险分析
    safety_check_passed: bool
    reliability_check_passed: bool
    efficiency_optimized: bool

class RiskPriorityClassifier:
    """基于风险优先级的分类器"""
    
    def __init__(self, config_manager: VersionedKnowledgeManager = None):
        """
        初始化风险优先级分类器
        
        Args:
            config_manager: 版本化知识库管理器
        """
        self.config_manager = config_manager or VersionedKnowledgeManager()
        self.config: Optional[VersionedKnowledgeConfig] = None
        
        # 加载最新配置
        self._load_config()
        
        # 编译正则表达式
        self._compile_patterns()
        
        # 统计信息
        self.stats = {
            'total_classifications': 0,
            'safety_blocks': 0,
            'reliability_blocks': 0,
            'efficiency_optimizations': 0,
            'uncertain_cases': 0
        }
        
        logger.info("🛡️ 风险优先级分类器初始化完成")
    
    def _load_config(self):
        """加载配置"""
        self.config = self.config_manager.load_latest_version()
        if not self.config:
            logger.error("❌ 无法加载知识库配置")
            raise RuntimeError("知识库配置加载失败")
        
        logger.info(f"📚 加载知识库配置: v{self.config.version_info.version}")
    
    def _compile_patterns(self):
        """编译正则表达式模式"""
        if not self.config:
            return
        
        # P0 - 安全性模式
        self.safety_patterns = {
            'critical_keywords': [
                re.compile(f".*{keyword}.*", re.IGNORECASE) 
                for keyword in self.config.safety_patterns.critical_negative_keywords
            ],
            'numerical_negative': [
                re.compile(pattern, re.IGNORECASE) 
                for pattern in self.config.safety_patterns.numerical_negative_patterns
            ],
            'regulatory': [
                re.compile(pattern, re.IGNORECASE) 
                for pattern in self.config.safety_patterns.regulatory_patterns
            ]
        }
        
        # P1 - 可靠性模式
        self.reliability_patterns = {
            'ultra_safe_neutral': [
                re.compile(pattern, re.IGNORECASE) 
                for pattern in self.config.reliability_patterns.ultra_safe_neutral_patterns
            ],
            'exclusion': [
                re.compile(pattern, re.IGNORECASE) 
                for pattern in self.config.reliability_patterns.neutral_exclusion_patterns
            ]
        }
        
        # P2 - 效率模式
        self.efficiency_patterns = {
            'extended_neutral': [
                re.compile(pattern, re.IGNORECASE) 
                for pattern in self.config.efficiency_patterns.extended_neutral_patterns
            ]
        }
        
        logger.info("✅ 正则表达式模式编译完成")
    
    def classify(self, title: str, content: str = "") -> RiskPriorityResult:
        """
        基于风险优先级进行分类
        
        Args:
            title: 文档标题
            content: 文档内容
            
        Returns:
            风险优先级分类结果
        """
        start_time = time.time()
        full_text = f"{title} {content}"
        
        self.stats['total_classifications'] += 1
        
        classification_trace = []
        matched_patterns = []
        
        # P0 - 安全性检查（最高优先级）
        safety_result = self._check_safety(full_text, classification_trace, matched_patterns)
        if safety_result:
            self.stats['safety_blocks'] += 1
            return self._create_result(
                result=safety_result,
                confidence=0.95,
                risk_level=RiskLevel.CRITICAL,
                matched_patterns=matched_patterns,
                processing_time=time.time() - start_time,
                classification_trace=classification_trace,
                safety_check_passed=False,
                reliability_check_passed=False,
                efficiency_optimized=False
            )
        
        # P1 - 可靠性检查（第二优先级）
        reliability_result = self._check_reliability(full_text, classification_trace, matched_patterns)
        if reliability_result:
            if reliability_result == ClassificationResult.NEUTRAL:
                # 通过了可靠性检查的中性分类
                return self._create_result(
                    result=reliability_result,
                    confidence=0.90,
                    risk_level=RiskLevel.LOW,
                    matched_patterns=matched_patterns,
                    processing_time=time.time() - start_time,
                    classification_trace=classification_trace,
                    safety_check_passed=True,
                    reliability_check_passed=True,
                    efficiency_optimized=False
                )
            else:
                # 可靠性检查阻止了中性分类
                self.stats['reliability_blocks'] += 1
                return self._create_result(
                    result=ClassificationResult.UNCERTAIN,
                    confidence=0.70,
                    risk_level=RiskLevel.MEDIUM,
                    matched_patterns=matched_patterns,
                    processing_time=time.time() - start_time,
                    classification_trace=classification_trace,
                    safety_check_passed=True,
                    reliability_check_passed=False,
                    efficiency_optimized=False
                )
        
        # P2 - 效率优化（最低优先级）
        efficiency_result = self._check_efficiency(full_text, classification_trace, matched_patterns)
        if efficiency_result:
            self.stats['efficiency_optimizations'] += 1
            return self._create_result(
                result=efficiency_result,
                confidence=0.80,
                risk_level=RiskLevel.LOW,
                matched_patterns=matched_patterns,
                processing_time=time.time() - start_time,
                classification_trace=classification_trace,
                safety_check_passed=True,
                reliability_check_passed=True,
                efficiency_optimized=True
            )
        
        # 无法确定，交给LLM处理
        self.stats['uncertain_cases'] += 1
        classification_trace.append("所有规则检查完毕，无法确定分类")
        
        return self._create_result(
            result=ClassificationResult.UNCERTAIN,
            confidence=0.0,
            risk_level=RiskLevel.LOW,
            matched_patterns=[],
            processing_time=time.time() - start_time,
            classification_trace=classification_trace,
            safety_check_passed=True,
            reliability_check_passed=True,
            efficiency_optimized=False
        )
    
    def _check_safety(self, text: str, trace: List[str], patterns: List[str]) -> Optional[ClassificationResult]:
        """P0 - 安全性检查：绝不能遗漏负面信号"""
        trace.append("P0 - 安全性检查开始")
        
        # 检查关键负面词汇
        for pattern in self.safety_patterns['critical_keywords']:
            if pattern.search(text):
                matched_keyword = pattern.pattern.replace(".*", "").replace("\\", "")
                patterns.append(matched_keyword)
                trace.append(f"P0阻断: 发现关键负面词汇 '{matched_keyword}'")
                return ClassificationResult.NEGATIVE
        
        # 检查数字+负面词汇组合
        for pattern in self.safety_patterns['numerical_negative']:
            if pattern.search(text):
                patterns.append(pattern.pattern)
                trace.append(f"P0阻断: 发现数字+负面词汇组合")
                return ClassificationResult.NEGATIVE
        
        # 检查监管机构相关
        for pattern in self.safety_patterns['regulatory']:
            if pattern.search(text):
                patterns.append(pattern.pattern)
                trace.append(f"P0阻断: 发现监管机构相关内容")
                return ClassificationResult.NEGATIVE
        
        trace.append("P0 - 安全性检查通过")
        return None
    
    def _check_reliability(self, text: str, trace: List[str], patterns: List[str]) -> Optional[ClassificationResult]:
        """P1 - 可靠性检查：只有绝对确定的才能标记为中性"""
        trace.append("P1 - 可靠性检查开始")
        
        # 首先检查排除条件
        for pattern in self.reliability_patterns['exclusion']:
            if pattern.search(text):
                patterns.append(pattern.pattern)
                trace.append(f"P1阻断: 触发排除条件，不能标记为中性")
                return ClassificationResult.UNCERTAIN
        
        # 检查超安全中性模式
        for pattern in self.reliability_patterns['ultra_safe_neutral']:
            if pattern.search(text):
                patterns.append(pattern.pattern)
                trace.append(f"P1通过: 匹配超安全中性模式")
                return ClassificationResult.NEUTRAL
        
        trace.append("P1 - 可靠性检查：未找到确定的中性模式")
        return None
    
    def _check_efficiency(self, text: str, trace: List[str], patterns: List[str]) -> Optional[ClassificationResult]:
        """P2 - 效率优化：在安全可靠前提下的扩展识别"""
        trace.append("P2 - 效率优化检查开始")
        
        # 检查扩展中性模式
        for pattern in self.efficiency_patterns['extended_neutral']:
            if pattern.search(text):
                patterns.append(pattern.pattern)
                trace.append(f"P2优化: 匹配扩展中性模式")
                return ClassificationResult.NEUTRAL
        
        trace.append("P2 - 效率优化：未找到扩展模式")
        return None
    
    def _create_result(self, result: ClassificationResult, confidence: float, 
                      risk_level: RiskLevel, matched_patterns: List[str],
                      processing_time: float, classification_trace: List[str],
                      safety_check_passed: bool, reliability_check_passed: bool,
                      efficiency_optimized: bool) -> RiskPriorityResult:
        """创建分类结果"""
        
        return RiskPriorityResult(
            result=result,
            confidence=confidence,
            risk_level=risk_level,
            matched_patterns=matched_patterns,
            processing_time=processing_time,
            classification_trace=classification_trace,
            knowledge_version=self.config.version_info.version,
            code_version=self.config.version_info.code_version,
            safety_check_passed=safety_check_passed,
            reliability_check_passed=reliability_check_passed,
            efficiency_optimized=efficiency_optimized
        )
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计"""
        total = self.stats['total_classifications']
        if total == 0:
            return {"message": "暂无分类数据"}
        
        return {
            "总分类次数": total,
            "安全性阻断": f"{self.stats['safety_blocks']} ({self.stats['safety_blocks']/total*100:.1f}%)",
            "可靠性阻断": f"{self.stats['reliability_blocks']} ({self.stats['reliability_blocks']/total*100:.1f}%)",
            "效率优化": f"{self.stats['efficiency_optimizations']} ({self.stats['efficiency_optimizations']/total*100:.1f}%)",
            "不确定案例": f"{self.stats['uncertain_cases']} ({self.stats['uncertain_cases']/total*100:.1f}%)",
            "知识库版本": self.config.version_info.version,
            "代码版本": self.config.version_info.code_version
        }
    
    def reload_config(self):
        """重新加载配置（用于生产环境热更新）"""
        logger.info("🔄 重新加载知识库配置...")
        old_version = self.config.version_info.version if self.config else "未知"
        
        self._load_config()
        self._compile_patterns()
        
        new_version = self.config.version_info.version
        logger.info(f"✅ 配置重载完成: v{old_version} → v{new_version}")

def test_risk_priority_classifier():
    """测试风险优先级分类器"""
    print("🧪 测试风险优先级分类器")
    print("="*60)
    
    # 创建分类器
    classifier = RiskPriorityClassifier()
    
    # 测试案例
    test_cases = [
        # P0 - 安全性测试
        ("业绩预警公告", "公司预计2023年度净利润亏损5000万元"),
        ("监管函件", "公司收到证监会关注函"),
        ("违规处罚", "公司因信息披露违规被处罚100万元"),
        
        # P1 - 可靠性测试
        ("股东大会通知", "公司定于2024年3月15日召开年度股东大会"),
        ("董事会决议", "董事会审议通过了年度报告"),
        ("包含金额的公告", "公司董事会决议投资5000万元"),  # 应该被P1阻断
        
        # P2 - 效率测试
        ("公司章程修订", "公司章程部分条款修订公告"),
        ("审计意见", "会计师事务所出具标准无保留审计意见"),
        
        # 不确定案例
        ("一般性公告", "公司发布一般性业务公告")
    ]
    
    print(f"🔍 风险优先级分类测试:")
    for title, content in test_cases:
        result = classifier.classify(title, content)
        
        print(f"\n📄 {title}:")
        print(f"  分类结果: {result.result.label}")
        print(f"  置信度: {result.confidence:.3f}")
        print(f"  风险等级: {result.risk_level.level}")
        print(f"  匹配模式: {result.matched_patterns}")
        print(f"  安全检查: {'✅' if result.safety_check_passed else '❌'}")
        print(f"  可靠检查: {'✅' if result.reliability_check_passed else '❌'}")
        print(f"  效率优化: {'✅' if result.efficiency_optimized else '❌'}")
        print(f"  处理时间: {result.processing_time:.4f}秒")
        print(f"  版本信息: 知识库v{result.knowledge_version}, 代码v{result.code_version}")
        
        if len(result.classification_trace) <= 3:
            print(f"  分类轨迹: {result.classification_trace}")
    
    # 显示性能统计
    stats = classifier.get_performance_stats()
    print(f"\n📊 性能统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    test_risk_priority_classifier()
