#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输出目录管理器
统一管理所有输出文件的分类存储到E:\LLMData\
"""

import os
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Optional

logger = logging.getLogger(__name__)

class OutputManager:
    """输出目录管理器"""
    
    def __init__(self, base_dir: str = "E:/LLMData"):
        """
        初始化输出管理器
        
        Args:
            base_dir: 基础输出目录
        """
        self.base_dir = Path(base_dir)
        
        # 定义各类输出目录
        self.directories = {
            'test_results': self.base_dir / "test_results",      # 测试结果
            'analysis_output': self.base_dir / "analysis_output", # 分析输出
            'logs': self.base_dir / "logs",                      # 日志文件
            'processed_data': self.base_dir / "processed_data",  # 处理后数据
            'reports': self.base_dir / "reports",                # 报告文件
            'configs': self.base_dir / "configs",                # 配置文件
            'temp': self.base_dir / "temp",                      # 临时文件
            'archives': self.base_dir / "archives",              # 归档文件
            'exports': self.base_dir / "exports",                # 导出文件
            'models': self.base_dir / "models"                   # 模型文件
        }
        
        # 创建所有目录
        self._create_directories()
        
        logger.info(f"✅ 输出管理器初始化完成，基础目录: {self.base_dir}")
    
    def _create_directories(self):
        """创建所有输出目录"""
        for dir_name, dir_path in self.directories.items():
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                logger.debug(f"📁 创建目录: {dir_path}")
            except Exception as e:
                logger.error(f"❌ 创建目录失败 {dir_path}: {e}")
    
    def get_directory(self, dir_type: str) -> Path:
        """
        获取指定类型的目录路径
        
        Args:
            dir_type: 目录类型
            
        Returns:
            目录路径
        """
        if dir_type not in self.directories:
            logger.warning(f"⚠️ 未知的目录类型: {dir_type}")
            return self.base_dir
        
        return self.directories[dir_type]
    
    def get_timestamped_path(self, dir_type: str, filename: str, 
                           timestamp_format: str = "%Y%m%d_%H%M%S") -> Path:
        """
        获取带时间戳的文件路径
        
        Args:
            dir_type: 目录类型
            filename: 文件名
            timestamp_format: 时间戳格式
            
        Returns:
            带时间戳的完整文件路径
        """
        directory = self.get_directory(dir_type)
        timestamp = datetime.now().strftime(timestamp_format)
        
        # 分离文件名和扩展名
        name_parts = filename.rsplit('.', 1)
        if len(name_parts) == 2:
            name, ext = name_parts
            timestamped_filename = f"{name}_{timestamp}.{ext}"
        else:
            timestamped_filename = f"{filename}_{timestamp}"
        
        return directory / timestamped_filename
    
    def save_test_result(self, filename: str, data, file_format: str = "excel") -> Path:
        """
        保存测试结果
        
        Args:
            filename: 文件名
            data: 数据（DataFrame或其他）
            file_format: 文件格式 (excel, csv, json)
            
        Returns:
            保存的文件路径
        """
        if file_format.lower() == "excel":
            file_path = self.get_timestamped_path("test_results", f"{filename}.xlsx")
            data.to_excel(file_path, index=False, engine='openpyxl')
        elif file_format.lower() == "csv":
            file_path = self.get_timestamped_path("test_results", f"{filename}.csv")
            data.to_csv(file_path, index=False, encoding='utf-8-sig')
        elif file_format.lower() == "json":
            import json
            file_path = self.get_timestamped_path("test_results", f"{filename}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        else:
            raise ValueError(f"不支持的文件格式: {file_format}")
        
        logger.info(f"💾 测试结果已保存: {file_path}")
        return file_path
    
    def save_analysis_output(self, filename: str, data, file_format: str = "json") -> Path:
        """
        保存分析输出
        
        Args:
            filename: 文件名
            data: 数据
            file_format: 文件格式
            
        Returns:
            保存的文件路径
        """
        if file_format.lower() == "json":
            import json
            file_path = self.get_timestamped_path("analysis_output", f"{filename}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        elif file_format.lower() == "excel":
            file_path = self.get_timestamped_path("analysis_output", f"{filename}.xlsx")
            data.to_excel(file_path, index=False, engine='openpyxl')
        else:
            raise ValueError(f"不支持的文件格式: {file_format}")
        
        logger.info(f"📊 分析输出已保存: {file_path}")
        return file_path
    
    def save_report(self, filename: str, content: str, file_format: str = "txt") -> Path:
        """
        保存报告文件
        
        Args:
            filename: 文件名
            content: 报告内容
            file_format: 文件格式 (txt, md, html)
            
        Returns:
            保存的文件路径
        """
        file_path = self.get_timestamped_path("reports", f"{filename}.{file_format}")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"📋 报告已保存: {file_path}")
        return file_path
    
    def get_log_file_path(self, log_name: str = "python_student_c") -> Path:
        """
        获取日志文件路径
        
        Args:
            log_name: 日志名称
            
        Returns:
            日志文件路径
        """
        return self.get_directory("logs") / f"{log_name}.log"
    
    def cleanup_temp_files(self, older_than_days: int = 7):
        """
        清理临时文件
        
        Args:
            older_than_days: 清理多少天前的文件
        """
        temp_dir = self.get_directory("temp")
        if not temp_dir.exists():
            return
        
        import time
        cutoff_time = time.time() - (older_than_days * 24 * 60 * 60)
        
        cleaned_count = 0
        for file_path in temp_dir.iterdir():
            if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                try:
                    file_path.unlink()
                    cleaned_count += 1
                except Exception as e:
                    logger.error(f"删除临时文件失败 {file_path}: {e}")
        
        logger.info(f"🧹 清理了 {cleaned_count} 个临时文件")
    
    def get_directory_info(self) -> Dict:
        """获取目录信息"""
        info = {
            'base_directory': str(self.base_dir),
            'directories': {},
            'total_size_mb': 0
        }
        
        for dir_name, dir_path in self.directories.items():
            if dir_path.exists():
                file_count = len(list(dir_path.iterdir()))
                
                # 计算目录大小
                total_size = 0
                try:
                    for file_path in dir_path.rglob('*'):
                        if file_path.is_file():
                            total_size += file_path.stat().st_size
                except Exception as e:
                    logger.debug(f"计算目录大小失败 {dir_path}: {e}")
                
                info['directories'][dir_name] = {
                    'path': str(dir_path),
                    'exists': True,
                    'file_count': file_count,
                    'size_mb': round(total_size / 1024 / 1024, 2)
                }
                info['total_size_mb'] += info['directories'][dir_name]['size_mb']
            else:
                info['directories'][dir_name] = {
                    'path': str(dir_path),
                    'exists': False,
                    'file_count': 0,
                    'size_mb': 0
                }
        
        info['total_size_mb'] = round(info['total_size_mb'], 2)
        return info
    
    def print_directory_structure(self):
        """打印目录结构"""
        print(f"\n📁 输出目录结构 (基础目录: {self.base_dir})")
        print("="*60)
        
        info = self.get_directory_info()
        
        for dir_name, dir_info in info['directories'].items():
            status = "✅" if dir_info['exists'] else "❌"
            print(f"{status} {dir_name:15} | {dir_info['file_count']:4d} 文件 | {dir_info['size_mb']:8.2f} MB")
        
        print("-"*60)
        print(f"📊 总计: {info['total_size_mb']} MB")

def test_output_manager():
    """测试输出管理器"""
    print("🧪 测试输出管理器...")
    
    # 创建输出管理器
    output_manager = OutputManager()
    
    # 显示目录结构
    output_manager.print_directory_structure()
    
    # 测试保存功能
    import pandas as pd
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'column1': [1, 2, 3],
        'column2': ['a', 'b', 'c']
    })
    
    # 保存测试结果
    try:
        excel_path = output_manager.save_test_result("test_data", test_data, "excel")
        csv_path = output_manager.save_test_result("test_data", test_data, "csv")
        
        print(f"✅ 测试文件保存成功:")
        print(f"  Excel: {excel_path}")
        print(f"  CSV: {csv_path}")
        
    except Exception as e:
        print(f"❌ 测试保存失败: {e}")
    
    # 测试报告保存
    try:
        report_content = "这是一个测试报告\n包含多行内容"
        report_path = output_manager.save_report("test_report", report_content, "txt")
        print(f"✅ 测试报告保存成功: {report_path}")
        
    except Exception as e:
        print(f"❌ 测试报告保存失败: {e}")

if __name__ == "__main__":
    test_output_manager()
