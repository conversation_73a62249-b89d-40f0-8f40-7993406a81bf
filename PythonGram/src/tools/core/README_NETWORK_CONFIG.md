# 网络路径配置说明

## 概述
本模块提供了智能的网络路径处理功能，可以自动将网络共享路径映射到本地路径，解决无法访问局域网文件的问题。

## 配置文件
配置文件位于 `network_config.json`，包含以下设置：

### 网络路径映射 (network_mappings)
将网络路径前缀映射到本地路径前缀：
```json
{
    "\\\\supmicro4\\F\\": "F:\\",
    "\\\\supmicro4\\D\\": "D:\\",
    "\\\\192.168.1.100\\shared\\": "C:\\shared\\"
}
```

### 替代路径 (alternative_paths)
当主要映射失败时，尝试的替代路径：
```json
[
    "C:\\Announcement\\",
    "D:\\Announcement\\",
    "E:\\Announcement\\"
]
```

### 其他设置
- `network_test_timeout`: 网络连接测试超时时间（秒）
- `enable_network_testing`: 是否启用网络连接测试
- `enable_local_mapping`: 是否启用本地路径映射

## 使用方法

### 1. 基本使用
```python
from student_c_batch_test import BatchTestRunner

# 创建测试运行器（自动加载配置）
runner = BatchTestRunner()
```

### 2. 自定义配置
```python
# 修改配置文件中的映射规则
# 或者直接修改 network_config.json 文件
```

### 3. 故障排除

#### 网络连接问题
- 检查服务器 `supmicro4` 是否可达
- 验证网络权限和防火墙设置
- 确认共享文件夹是否存在

#### 本地映射问题
- 确保本地驱动器存在（F:, D:, C:）
- 检查替代路径是否正确
- 验证文件权限

## 工作流程

1. **检测网络路径**: 识别以 `\\` 开头的路径
2. **网络连接测试**: 测试主机连通性（可选）
3. **路径映射**: 尝试将网络路径映射到本地路径
4. **替代路径**: 如果映射失败，尝试预定义的替代路径
5. **文件读取**: 使用最终路径读取PDF文件

## 常见问题

### Q: 为什么无法访问 `\\supmicro4\F\` 路径？
A: 可能的原因：
- 服务器 `supmicro4` 已关闭或不可达
- 网络权限不足
- 共享文件夹已删除或重命名
- 防火墙阻止连接

### Q: 如何添加新的网络路径映射？
A: 在 `network_config.json` 中添加新的映射规则：
```json
"\\\\新服务器名\\共享名\\": "本地路径\\"
```

### Q: 可以禁用网络测试吗？
A: 可以，在配置文件中设置 `"enable_network_testing": false`

## 日志输出
程序会输出详细的诊断信息：
- 🌐 网络路径检测
- 🔍 网络连接测试
- ✅ 成功映射/连接
- ❌ 失败信息
- ⚠️ 警告信息

## 性能优化
- PDF内容缓存避免重复读取
- 可配置的网络测试超时
- 智能的替代路径查找










