#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学生C增强版 - 混合策略：学生A过滤 + 学生C决策 + LLM老师指导
"""

import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# 导入学生A和学生C的分类器
from student_a_classifier import StudentAClassifier, FilterResult, ClassificationResult
from python_student_c import PythonStudentC

class LLMTeacher:
    """LLM老师 - 指导学生C提高非中性公告识别率"""
    
    def __init__(self):
        self.learning_history = []
        self.performance_stats = {
            'total_cases': 0,
            'correct_predictions': 0,
            'incorrect_predictions': 0,
            'improvement_suggestions': []
        }
    
    def analyze_student_c_performance(self, student_c_result: str, student_a_result: str, 
                                    llm_result: str, content: str) -> Dict:
        """分析学生C的表现并提供改进建议"""
        
        self.performance_stats['total_cases'] += 1
        
        # 判断学生C的预测是否正确
        is_correct = (student_c_result == llm_result)
        if is_correct:
            self.performance_stats['correct_predictions'] += 1
        else:
            self.performance_stats['incorrect_predictions'] += 1
        
        # 分析不一致的原因
        analysis = {
            'case_id': len(self.learning_history) + 1,
            'student_c_prediction': student_c_result,
            'student_a_prediction': student_a_result,
            'llm_ground_truth': llm_result,
            'is_correct': is_correct,
            'content_preview': content[:100] + "..." if len(content) > 100 else content,
            'suggestions': []
        }
        
        # 生成改进建议
        if not is_correct:
            suggestions = self._generate_improvement_suggestions(
                student_c_result, student_a_result, llm_result, content
            )
            analysis['suggestions'] = suggestions
            self.performance_stats['improvement_suggestions'].extend(suggestions)
        
        # 记录学习历史
        self.learning_history.append(analysis)
        
        return analysis
    
    def _generate_improvement_suggestions(self, student_c_result: str, 
                                        student_a_result: str, 
                                        llm_result: str, 
                                        content: str) -> List[str]:
        """生成具体的改进建议"""
        suggestions = []
        
        # 基于结果差异生成建议
        if student_c_result == 'UNCERTAIN' and llm_result in ['POSITIVE', 'NEGATIVE']:
            suggestions.append("学生C过于保守，建议降低不确定性阈值")
            suggestions.append("检查是否遗漏了关键的正则表达式规则")
        
        elif student_c_result in ['POSITIVE', 'NEGATIVE'] and llm_result == 'NEUTRAL':
            suggestions.append("学生C过于激进，建议提高确定性阈值")
            suggestions.append("检查是否存在误判的正则表达式规则")
        
        elif student_c_result != student_a_result and student_a_result == llm_result:
            suggestions.append("学生A的判断更准确，建议学习学生A的规则")
            suggestions.append("考虑将学生A的规则集成到学生C中")
        
        # 基于内容特征生成建议
        if '风险' in content and student_c_result != 'NEGATIVE':
            suggestions.append("包含'风险'关键词，建议优先考虑负面判断")
        
        if '增长' in content and student_c_result != 'POSITIVE':
            suggestions.append("包含'增长'关键词，建议优先考虑正面判断")
        
        return suggestions
    
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        total = self.performance_stats['total_cases']
        if total == 0:
            return {"error": "暂无数据"}
        
        accuracy = self.performance_stats['correct_predictions'] / total
        return {
            'total_cases': total,
            'accuracy': f"{accuracy:.2%}",
            'correct_predictions': self.performance_stats['correct_predictions'],
            'incorrect_predictions': self.performance_stats['incorrect_predictions'],
            'improvement_suggestions_count': len(self.performance_stats['improvement_suggestions']),
            'recent_suggestions': self.performance_stats['improvement_suggestions'][-5:]  # 最近5条建议
        }
    
    def save_learning_history(self, output_path: str = "llm_teacher_learning_history.json"):
        """保存学习历史"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump({
                    'performance_stats': self.performance_stats,
                    'learning_history': self.learning_history
                }, f, ensure_ascii=False, indent=2)
            print(f"✅ 学习历史已保存到: {output_path}")
            return True
        except Exception as e:
            print(f"❌ 保存学习历史失败: {e}")
            return False

class StudentCEnhanced:
    """学生C增强版 - 混合策略实现"""
    
    def __init__(self):
        # 初始化学生A分类器
        self.student_a = StudentAClassifier()
        
        # 初始化学生C分类器
        self.student_c = PythonStudentC()
        
        # 初始化LLM老师
        self.llm_teacher = LLMTeacher()
        
        # 统计信息
        self.stats = {
            'student_a_filtered': 0,
            'student_c_enhanced': 0,
            'llm_teacher_guided': 0,
            'consistency_improved': 0
        }
    
    def classify_enhanced(self, title: str, content: str = "", llm_ground_truth: str = None) -> Dict:
        """增强版分类：学生A过滤 + 学生C决策 + LLM老师指导"""
        
        start_time = time.time()
        
        # 第一步：学生A过滤
        student_a_result = self.student_a.classify(title, content)
        self.stats['student_a_filtered'] += 1
        
        # 第二步：学生C决策
        student_c_result = self.student_c.classify(title, content)
        self.stats['student_c_enhanced'] += 1
        
        # 第三步：LLM老师指导（如果有真实标签）
        if llm_ground_truth:
            teacher_analysis = self.llm_teacher.analyze_student_c_performance(
                student_c_result.result.name,
                student_a_result.result.name,
                llm_ground_truth,
                content
            )
            self.stats['llm_teacher_guided'] += 1
            
            # 检查一致性改进
            if student_a_result.result.name == llm_ground_truth:
                self.stats['consistency_improved'] += 1
        else:
            teacher_analysis = None
        
        # 第四步：混合决策策略
        final_decision = self._hybrid_decision_strategy(
            student_a_result, student_c_result, teacher_analysis
        )
        
        processing_time = int((time.time() - start_time) * 1000)
        
        return {
            'final_decision': final_decision,
            'student_a_result': {
                'result': student_a_result.result.name,
                'confidence': student_a_result.confidence,
                'filter_name': student_a_result.filter_name,
                'matched_rules': student_a_result.matched_rules
            },
            'student_c_result': {
                'result': student_c_result.result.name,
                'confidence': student_c_result.confidence,
                'filter_name': student_c_result.filter_name
            },
            'llm_teacher_analysis': teacher_analysis,
            'processing_time_ms': processing_time,
            'strategy_used': 'hybrid_decision'
        }
    
    def _hybrid_decision_strategy(self, student_a_result: ClassificationResult, 
                                 student_c_result: ClassificationResult,
                                 teacher_analysis: Optional[Dict]) -> str:
        """混合决策策略"""
        
        # 策略1：如果学生A有确定性结果，优先使用
        if student_a_result.result != FilterResult.UNCERTAIN:
            return student_a_result.result.name
        
        # 策略2：如果学生C有确定性结果，使用学生C
        if student_c_result.result.name != 'UNCERTAIN':
            return student_c_result.result.name
        
        # 策略3：如果LLM老师有建议，参考建议
        if teacher_analysis and teacher_analysis.get('suggestions'):
            # 基于建议调整决策
            suggestions = teacher_analysis['suggestions']
            if any('负面' in s for s in suggestions):
                return 'NEGATIVE'
            elif any('正面' in s for s in suggestions):
                return 'POSITIVE'
            elif any('中性' in s for s in suggestions):
                return 'NEUTRAL'
        
        # 策略4：默认返回不确定
        return 'UNCERTAIN'
    
    def get_enhancement_stats(self) -> Dict:
        """获取增强效果统计"""
        return {
            'student_a_filtered': self.stats['student_a_filtered'],
            'student_c_enhanced': self.stats['student_c_enhanced'],
            'llm_teacher_guided': self.stats['llm_teacher_guided'],
            'consistency_improved': self.stats['consistency_improved'],
            'llm_teacher_performance': self.llm_teacher.get_performance_report()
        }
    
    def save_enhancement_report(self, output_path: str = "student_c_enhancement_report.json"):
        """保存增强效果报告"""
        try:
            report = {
                'enhancement_stats': self.get_enhancement_stats(),
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'description': '学生C增强版混合策略效果报告'
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 增强效果报告已保存到: {output_path}")
            return True
        except Exception as e:
            print(f"❌ 保存增强效果报告失败: {e}")
            return False

def main():
    """测试学生C增强版"""
    enhanced_c = StudentCEnhanced()
    
    test_cases = [
        {
            'title': '关于收到证监会警示函的公告',
            'content': '公司收到证监会警示函，提醒注意信息披露规范',
            'llm_truth': 'NEGATIVE'
        },
        {
            'title': '2023年业绩预增公告',
            'content': '预计2023年净利润同比增长50%以上',
            'llm_truth': 'POSITIVE'
        },
        {
            'title': '关于召开股东大会的通知',
            'content': '定于2023年12月召开年度股东大会',
            'llm_truth': 'NEUTRAL'
        }
    ]
    
    print("🧪 测试学生C增强版混合策略...")
    print("=" * 60)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📄 测试案例 {i}: {case['title']}")
        result = enhanced_c.classify_enhanced(
            case['title'], 
            case['content'], 
            case['llm_truth']
        )
        
        print(f"  最终决策: {result['final_decision']}")
        print(f"  学生A结果: {result['student_a_result']['result']}")
        print(f"  学生C结果: {result['student_c_result']['result']}")
        print(f"  处理时间: {result['processing_time_ms']}ms")
        
        if result['llm_teacher_analysis']:
            analysis = result['llm_teacher_analysis']
            print(f"  LLM老师分析: {'✅ 正确' if analysis['is_correct'] else '❌ 错误'}")
            if analysis['suggestions']:
                print(f"  改进建议: {analysis['suggestions']}")
    
    # 显示统计信息
    print("\n📊 增强效果统计:")
    stats = enhanced_c.get_enhancement_stats()
    for key, value in stats.items():
        if key != 'llm_teacher_performance':
            print(f"  {key}: {value}")
    
    # 显示LLM老师性能
    llm_perf = stats['llm_teacher_performance']
    print(f"\n🎓 LLM老师性能:")
    for key, value in llm_perf.items():
        print(f"  {key}: {value}")
    
    # 保存报告
    enhanced_c.save_enhancement_report()
    enhanced_c.llm_teacher.save_learning_history()

if __name__ == "__main__":
    main()










