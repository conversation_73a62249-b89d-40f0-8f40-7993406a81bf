# Python学生C依赖包
# 用于PDF内容提取和文本分析

# PDF处理库
PyPDF2>=3.0.0
pdfminer.six>=20221105
pypdf>=3.0.0

# 文本处理和分析
jieba>=0.42.1
nltk>=3.8

# 数据处理
pandas>=1.5.0
numpy>=1.21.0

# JSON和配置处理
jsonschema>=4.0.0

# 系统监控（可选）
psutil>=5.9.0

# 现代化升级库
# 配置管理和数据验证
pydantic>=1.10.0

# NLP增强
spacy>=3.4.0

# 规则引擎
business-rules>=1.0.0

# 结构化日志
structlog>=22.0.0

# 高性能数据处理（可选）
polars>=0.18.0

# 机器学习辅助（可选）
scikit-learn>=1.2.0

# 测试框架（开发用）
pytest>=7.0.0
pytest-cov>=4.0.0

# 类型检查（开发用）
mypy>=1.0.0

# 代码格式化（开发用）
black>=22.0.0
flake8>=5.0.0
