#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NLP依赖安装脚本
自动安装和配置多引擎NLP处理所需的依赖库
"""

import subprocess
import sys
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class NLPDependencyInstaller:
    """NLP依赖安装器"""
    
    def __init__(self):
        self.dependencies = {
            'jieba': {
                'package': 'jieba',
                'description': 'Python中文分词库',
                'required': True
            },
            'lac': {
                'package': 'lac',
                'description': '百度开源词法分析工具',
                'required': False
            },
            'pkuseg': {
                'package': 'pkuseg',
                'description': '北京大学开源分词工具',
                'required': False
            },
            'snownlp': {
                'package': 'snownlp',
                'description': '中文情感分析库',
                'required': False
            },
            'hanlp': {
                'package': 'hanlp',
                'description': '自然语言处理工具包',
                'required': False
            },
            'scipy': {
                'package': 'scipy',
                'description': '科学计算库（用于卡方检验）',
                'required': True
            },
            'tqdm': {
                'package': 'tqdm',
                'description': '进度条库',
                'required': True
            }
        }
    
    def check_installed_packages(self):
        """检查已安装的包"""
        installed = {}
        
        for name, info in self.dependencies.items():
            try:
                __import__(info['package'])
                installed[name] = True
                print(f"✅ {name}: 已安装")
            except ImportError:
                installed[name] = False
                print(f"❌ {name}: 未安装")
        
        return installed
    
    def install_package(self, package_name: str) -> bool:
        """安装单个包"""
        try:
            print(f"正在安装 {package_name}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package_name])
            print(f"✅ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ {package_name} 安装失败: {e}")
            return False
    
    def install_all_dependencies(self, install_optional: bool = True):
        """安装所有依赖"""
        print("🚀 开始安装NLP依赖...")
        
        # 检查当前状态
        installed = self.check_installed_packages()
        
        # 安装缺失的包
        for name, info in self.dependencies.items():
            if not installed[name]:
                if info['required'] or install_optional:
                    print(f"\n📦 安装 {name} ({info['description']})...")
                    success = self.install_package(info['package'])
                    if not success and info['required']:
                        print(f"❌ 必需包 {name} 安装失败，可能影响功能")
                else:
                    print(f"⏭️ 跳过可选包 {name}")
        
        print("\n🎉 依赖安装完成！")
    
    def download_models(self):
        """下载预训练模型"""
        print("\n📥 下载预训练模型...")
        
        # pkuseg金融模型
        try:
            import pkuseg
            print("正在下载pkuseg金融模型...")
            # pkuseg会自动下载模型
            seg = pkuseg.pkuseg(model_name='finance')
            print("✅ pkuseg金融模型下载成功")
        except Exception as e:
            print(f"⚠️ pkuseg金融模型下载失败: {e}")
        
        # HanLP模型
        try:
            import hanlp
            print("正在下载HanLP轻量级模型...")
            hanlp.load(hanlp.pretrained.tok.COARSE_ELECTRA_SMALL_ZH)
            print("✅ HanLP模型下载成功")
        except Exception as e:
            print(f"⚠️ HanLP模型下载失败: {e}")
    
    def setup_jieba_dictionaries(self):
        """设置jieba自定义词典"""
        print("\n📚 配置jieba自定义词典...")

        try:
            import jieba
            import jieba.analyse

            # 当前目录
            current_dir = Path(__file__).parent

            # 加载金融词典
            financial_dict_path = current_dir / "financial_dict.txt"
            if financial_dict_path.exists():
                jieba.load_userdict(str(financial_dict_path))
                print(f"✅ 加载金融词典: {financial_dict_path}")
            else:
                print(f"⚠️ 金融词典文件不存在: {financial_dict_path}")
                # 创建基础金融词典
                self._create_basic_financial_dict(financial_dict_path)

            # 设置停用词
            stop_words_path = current_dir / "stop_words.txt"
            if stop_words_path.exists():
                try:
                    jieba.analyse.set_stop_words(str(stop_words_path))
                    print(f"✅ 设置停用词: {stop_words_path}")
                except AttributeError:
                    # jieba.analyse.set_stop_words可能不存在，使用替代方法
                    print(f"⚠️ jieba版本不支持set_stop_words，停用词将在分析时处理")
            else:
                print(f"⚠️ 停用词文件不存在: {stop_words_path}")
                # 创建基础停用词文件
                self._create_basic_stop_words(stop_words_path)

        except ImportError:
            print("❌ jieba未安装，无法配置词典")
        except Exception as e:
            print(f"⚠️ jieba词典配置失败: {e}")

    def _create_basic_financial_dict(self, dict_path: Path):
        """创建基础金融词典"""
        financial_words = [
            "业绩预警 1000 n",
            "业绩预增 1000 n",
            "业绩下滑 1000 n",
            "业绩亏损 1000 n",
            "净利润 1000 n",
            "扣非净利润 1000 n",
            "营业收入 1000 n",
            "资产重组 1000 n",
            "股权激励 1000 n",
            "股权变动 1000 n",
            "高管变动 1000 n",
            "违规处罚 1000 n",
            "诉讼仲裁 1000 n",
            "风险提示 1000 n",
            "退市风险 1000 n",
            "债务违约 1000 n",
            "担保金额 1000 n",
            "重大合同 1000 n",
            "技术突破 1000 n",
            "市场拓展 1000 n",
            "分红派息 1000 n"
        ]

        try:
            with open(dict_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(financial_words))
            print(f"✅ 创建基础金融词典: {dict_path}")
        except Exception as e:
            print(f"❌ 创建金融词典失败: {e}")

    def _create_basic_stop_words(self, stop_words_path: Path):
        """创建基础停用词文件"""
        stop_words = [
            "的", "了", "在", "是", "有", "和", "与", "及", "或",
            "将", "已", "为", "由", "从", "向", "对", "按", "根据",
            "等", "等等", "如", "如果", "但是", "然而", "因此", "所以",
            "万元", "万股", "亿元", "关于", "公司", "公告", "通知",
            "报告", "会议", "决议", "股东", "董事会", "监事会",
            "事件标签", "其他", "进行", "相关", "情况", "事项"
        ]

        try:
            with open(stop_words_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(stop_words))
            print(f"✅ 创建基础停用词文件: {stop_words_path}")
        except Exception as e:
            print(f"❌ 创建停用词文件失败: {e}")
    
    def run_installation(self, install_optional: bool = True, download_models: bool = True):
        """运行完整安装流程"""
        print("🎯 NLP依赖安装器")
        print("="*50)
        
        # 1. 安装依赖包
        self.install_all_dependencies(install_optional)
        
        # 2. 配置jieba词典
        self.setup_jieba_dictionaries()
        
        # 3. 下载预训练模型
        if download_models:
            self.download_models()
        
        # 4. 最终检查
        print("\n🔍 最终检查...")
        final_status = self.check_installed_packages()
        
        # 统计结果
        installed_count = sum(final_status.values())
        total_count = len(final_status)
        
        print(f"\n📊 安装结果: {installed_count}/{total_count} 个包已安装")
        
        if installed_count == total_count:
            print("🎉 所有依赖安装成功！")
        else:
            print("⚠️ 部分依赖安装失败，但不影响基本功能")
        
        return final_status

def main():
    """主函数"""
    installer = NLPDependencyInstaller()
    
    print("欢迎使用NLP依赖安装器！")
    print("这将安装以下工具库：")
    print("  - jieba: Python中文分词库（必需）")
    print("  - LAC: 百度词法分析工具（可选）")
    print("  - pkuseg: 北大分词工具（可选）")
    print("  - SnowNLP: 情感分析库（可选）")
    print("  - HanLP: 自然语言处理工具包（可选）")
    print("  - scipy: 科学计算库（必需）")
    print("  - tqdm: 进度条库（必需）")
    
    # 询问用户是否安装可选依赖
    while True:
        choice = input("\n是否安装可选依赖？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            install_optional = True
            break
        elif choice in ['n', 'no', '否']:
            install_optional = False
            break
        else:
            print("请输入 y 或 n")
    
    # 询问是否下载模型
    while True:
        choice = input("是否下载预训练模型？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            download_models = True
            break
        elif choice in ['n', 'no', '否']:
            download_models = False
            break
        else:
            print("请输入 y 或 n")
    
    # 运行安装
    installer.run_installation(install_optional, download_models)
    
    print("\n🎯 安装完成！现在可以运行JsonFeatureAnalyzer了")
    print("运行命令: python json_feature_analyzer.py")

if __name__ == "__main__":
    main()
