#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能分类系统 - 整理后的主入口
集成所有核心功能，提供统一的API接口
"""

import json
import time
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter

@dataclass
class ClassificationResult:
    """标准化分类结果"""
    category: str                    # 分类结果
    confidence: float               # 置信度
    method: str                     # 分类方法
    matched_signals: List[str]      # 匹配的信号
    processing_time: float          # 处理时间
    explanation: str                # 分类解释
    
    def to_dict(self) -> Dict:
        return asdict(self)
    
    def to_json(self) -> str:
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)

class AtomicSignalClassifier:
    """原子信号分类器 - 重构版"""
    
    def __init__(self, lexicon_file: str = "atomic_signal_lexicon.json"):
        self.lexicon_file = lexicon_file
        self.rules = []
        self.load_rules()
    
    def load_rules(self):
        """加载原子信号规则"""
        try:
            if Path(self.lexicon_file).exists():
                with open(self.lexicon_file, 'r', encoding='utf-8') as f:
                    lexicon_data = json.load(f)
                self.rules = lexicon_data.get('rules', [])
                print(f"✅ 加载原子信号规则: {len(self.rules)} 条")
            else:
                print(f"⚠️ 规则文件不存在: {self.lexicon_file}")
                self.rules = []
        except Exception as e:
            print(f"❌ 加载规则失败: {e}")
            self.rules = []
    
    def classify(self, title: str, content: str = "") -> ClassificationResult:
        """执行原子信号分类"""
        start_time = time.time()
        full_text = f"{title} {content}".strip()
        
        if not full_text:
            return ClassificationResult(
                category="UNCERTAIN",
                confidence=0.0,
                method="atomic_signal",
                matched_signals=[],
                processing_time=time.time() - start_time,
                explanation="文本为空"
            )
        
        matched_rules = []
        for rule in self.rules:
            if re.search(rule['pattern'], full_text, re.IGNORECASE):
                matched_rules.append(rule)
        
        if not matched_rules:
            return ClassificationResult(
                category="UNCERTAIN",
                confidence=0.0,
                method="atomic_signal",
                matched_signals=[],
                processing_time=time.time() - start_time,
                explanation="未匹配任何原子信号"
            )
        
        # 选择最高优先级的规则
        priority_weights = {
            'E_TERRIBLE': 5, 'D_BAD': 4, 'A_EXCELLENT': 3, 'B_GOOD': 2, 'C_NEUTRAL': 1
        }
        
        best_rule = max(matched_rules, key=lambda x: (
            priority_weights.get(x['category'], 0),
            x.get('confidence', 0)
        ))
        
        matched_signals = [rule['component'] for rule in matched_rules]
        
        return ClassificationResult(
            category=best_rule['category'],
            confidence=best_rule.get('confidence', 0.8),
            method="atomic_signal",
            matched_signals=matched_signals,
            processing_time=time.time() - start_time,
            explanation=f"匹配原子信号: {best_rule['component']}"
        )

class SemanticContextAnalyzer:
    """语义上下文分析器 - 重构版"""
    
    def __init__(self):
        # 载体词汇：本身中性，需要依赖上下文
        self.carrier_words = {
            '年度报告', '季度报告', '半年报告', '业绩报告', '财务报告',
            '股东大会', '董事会', '监事会', '股东会议', '临时股东大会',
            '董事会决议', '股东大会决议', '监事会决议',
            '业绩公告', '财务公告', '重大事项', '重要公告'
        }
        
        # 修饰词汇：具有明确倾向性
        self.modifier_words = {
            'E_TERRIBLE': {
                '退市', '破产', '违规', '违法', '处罚', '警示', '风险',
                '亏损', '巨亏', '严重亏损', '大幅亏损', '持续亏损',
                '下滑', '暴跌', '崩盘', '危机', '困难', '恶化'
            },
            'D_BAD': {
                '下降', '减少', '降低', '缩减', '萎缩', '疲软',
                '不佳', '不利', '困难', '挑战', '压力', '影响'
            },
            'A_EXCELLENT': {
                '优异', '卓越', '杰出', '突出', '显著', '大幅',
                '增长', '上升', '提升', '改善', '优化', '强劲',
                '盈利', '获利', '收益', '利润', '回报', '成功'
            },
            'B_GOOD': {
                '稳定', '平稳', '健康', '良好', '正常', '合理',
                '适度', '温和', '渐进', '持续', '稳健', '可控'
            },
            'C_NEUTRAL': {
                '召开', '审议', '通过', '决定', '确定', '安排',
                '计划', '预计', '拟定', '准备', '组织', '实施'
            }
        }
    
    def classify(self, title: str, content: str = "") -> ClassificationResult:
        """基于语义上下文进行分类"""
        start_time = time.time()
        full_text = f"{title} {content}".strip()
        
        # 检查是否有载体词汇
        found_carriers = []
        for carrier in self.carrier_words:
            if carrier in full_text:
                found_carriers.append(carrier)
        
        if not found_carriers:
            # 没有载体词汇，直接用修饰词分类
            return self._classify_by_modifiers(full_text, start_time)
        
        # 有载体词汇，分析上下文
        return self._classify_with_context(full_text, found_carriers, start_time)
    
    def _classify_by_modifiers(self, text: str, start_time: float) -> ClassificationResult:
        """基于修饰词直接分类"""
        for category, modifiers in self.modifier_words.items():
            for modifier in modifiers:
                if modifier in text:
                    return ClassificationResult(
                        category=category,
                        confidence=0.7,
                        method="semantic_context",
                        matched_signals=[modifier],
                        processing_time=time.time() - start_time,
                        explanation=f"直接匹配修饰词: {modifier}"
                    )
        
        return ClassificationResult(
            category="C_NEUTRAL",
            confidence=0.5,
            method="semantic_context",
            matched_signals=[],
            processing_time=time.time() - start_time,
            explanation="未找到明确信号，默认中性"
        )
    
    def _classify_with_context(self, text: str, carriers: List[str], start_time: float) -> ClassificationResult:
        """基于载体词汇和上下文分类"""
        category_scores = defaultdict(float)
        matched_modifiers = []
        
        # 在载体词汇附近寻找修饰词
        for carrier in carriers:
            carrier_pos = text.find(carrier)
            if carrier_pos != -1:
                # 提取上下文窗口
                window_start = max(0, carrier_pos - 20)
                window_end = min(len(text), carrier_pos + len(carrier) + 20)
                context = text[window_start:window_end]
                
                # 在上下文中寻找修饰词
                for category, modifiers in self.modifier_words.items():
                    for modifier in modifiers:
                        if modifier in context:
                            distance = abs(carrier_pos - context.find(modifier))
                            weight = 1.0 / (1.0 + distance * 0.1)  # 距离越近权重越高
                            category_scores[category] += weight
                            matched_modifiers.append(modifier)
        
        if not category_scores:
            final_category = "C_NEUTRAL"
            confidence = 0.5
            explanation = f"载体词汇 {carriers[0]} 无明确修饰词，默认中性"
        else:
            final_category = max(category_scores.items(), key=lambda x: x[1])[0]
            confidence = min(0.95, 0.6 + max(category_scores.values()) * 0.2)
            explanation = f"载体词汇 {carriers[0]} + 修饰词 {matched_modifiers[0] if matched_modifiers else 'none'}"
        
        return ClassificationResult(
            category=final_category,
            confidence=confidence,
            method="semantic_context",
            matched_signals=matched_modifiers,
            processing_time=time.time() - start_time,
            explanation=explanation
        )

class IntelligentClassificationSystem:
    """智能分类系统 - 主入口"""
    
    def __init__(self, lexicon_file: str = "atomic_signal_lexicon.json"):
        """初始化智能分类系统"""
        self.atomic_classifier = AtomicSignalClassifier(lexicon_file)
        self.semantic_analyzer = SemanticContextAnalyzer()
        
        # 统计信息
        self.stats = {
            'total_classifications': 0,
            'atomic_signal_used': 0,
            'semantic_context_used': 0,
            'hybrid_used': 0
        }
        
        print("🚀 智能分类系统初始化完成")
    
    def classify(self, title: str, content: str = "") -> ClassificationResult:
        """
        执行智能分类
        
        策略：
        1. 优先使用原子信号分类
        2. 如果原子信号置信度不高，使用语义上下文分析
        3. 混合两种方法的结果
        """
        self.stats['total_classifications'] += 1
        
        # 1. 尝试原子信号分类
        atomic_result = self.atomic_classifier.classify(title, content)
        
        # 2. 如果原子信号置信度高，直接返回
        if atomic_result.confidence >= 0.8:
            self.stats['atomic_signal_used'] += 1
            return atomic_result
        
        # 3. 尝试语义上下文分析
        semantic_result = self.semantic_analyzer.classify(title, content)
        
        # 4. 混合决策
        if atomic_result.category != "UNCERTAIN" and semantic_result.category != "UNCERTAIN":
            # 两种方法都有结果，选择置信度更高的
            if atomic_result.confidence >= semantic_result.confidence:
                self.stats['atomic_signal_used'] += 1
                return atomic_result
            else:
                self.stats['semantic_context_used'] += 1
                return semantic_result
        elif atomic_result.category != "UNCERTAIN":
            self.stats['atomic_signal_used'] += 1
            return atomic_result
        elif semantic_result.category != "UNCERTAIN":
            self.stats['semantic_context_used'] += 1
            return semantic_result
        else:
            # 两种方法都无法分类
            return ClassificationResult(
                category="UNCERTAIN",
                confidence=0.0,
                method="no_match",
                matched_signals=[],
                processing_time=0.001,
                explanation="所有方法都无法分类"
            )
    
    def batch_classify(self, texts: List[Tuple[str, str]]) -> List[ClassificationResult]:
        """批量分类"""
        results = []
        for title, content in texts:
            result = self.classify(title, content)
            results.append(result)
        return results
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        total = self.stats['total_classifications']
        if total == 0:
            return {"message": "暂无分类数据"}
        
        return {
            "总分类次数": total,
            "原子信号分类": f"{self.stats['atomic_signal_used']} ({self.stats['atomic_signal_used']/total*100:.1f}%)",
            "语义上下文分类": f"{self.stats['semantic_context_used']} ({self.stats['semantic_context_used']/total*100:.1f}%)",
            "混合决策分类": f"{self.stats['hybrid_used']} ({self.stats['hybrid_used']/total*100:.1f}%)"
        }

def main():
    """主函数 - 演示系统功能"""
    print("🚀 智能分类系统 v1.0.0")
    print("="*60)
    
    # 创建系统
    system = IntelligentClassificationSystem()
    
    # 测试案例
    test_cases = [
        ("公司股票进入退市整理期", ""),
        ("年度报告显示净利润大幅增长", ""),
        ("股东大会审议通过", ""),
        ("公司预计净利润亏损", ""),
        ("业绩报告显示稳定增长", ""),
    ]
    
    print("🧪 分类测试:")
    print(f"{'文本':<35} {'分类':<12} {'置信度':<8} {'方法':<15}")
    print("-"*80)
    
    for title, content in test_cases:
        result = system.classify(title, content)
        print(f"{title[:33]:<35} {result.category:<12} "
              f"{result.confidence:<8.3f} {result.method:<15}")
    
    # 显示统计信息
    stats = system.get_stats()
    print(f"\n📊 系统统计:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    print(f"\n✅ 整理后的智能分类系统运行正常！")
    return system

if __name__ == "__main__":
    system = main()
