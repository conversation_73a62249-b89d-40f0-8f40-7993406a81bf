#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超级Python老师学习系统
集成多种技术，目标准确率85%+
"""

import json
import logging
import numpy as np
from pathlib import Path
from collections import Counter, defaultdict
from typing import Dict, List, Tuple, Optional
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report
from sklearn.model_selection import GridSearchCV
from sklearn.pipeline import Pipeline
import joblib
import re

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SuperPythonTeacher:
    """超级Python老师 - 集成多种技术达到85%+准确率"""
    
    def __init__(self):
        """初始化超级Python老师"""
        self.ensemble_model = None
        self.vectorizer = None
        self.training_stats = {}
        
        # 评级映射
        self.rating_mapping = {
            '优': 'POSITIVE', '好': 'POSITIVE', 
            '中': 'NEUTRAL',
            '差': 'NEGATIVE', '劣': 'NEGATIVE'
        }
        
        # 领域特定关键词
        self.domain_keywords = {
            'NEGATIVE': [
                '亏损', '预亏', '违规', '违法', '处罚', '警示', '风险', '退市', '停牌',
                '破产', '立案', '调查', '诉讼', '仲裁', '冻结', '查封', '减持',
                '下降', '下滑', '预降', '大幅', '严重', '重大', '异常'
            ],
            'POSITIVE': [
                '增长', '盈利', '合同', '突破', '收购', '分红', '中标', '签署',
                '业绩', '净利润', '营业收入', '技术', '新产品', '合作', '协议'
            ],
            'NEUTRAL': [
                '股东大会', '董事会', '监事会', '决议', '公告', '通知', '报告',
                '年度', '季度', '定期', '临时', '召开', '审议', '选举'
            ]
        }
        
        logger.info("🚀 超级Python老师初始化完成")
    
    def create_domain_features(self, text: str) -> Dict[str, float]:
        """创建领域特定特征"""
        text_lower = text.lower()
        features = {}
        
        for category, keywords in self.domain_keywords.items():
            # 关键词匹配得分
            matches = sum(1 for keyword in keywords if keyword in text_lower)
            features[f'{category}_keyword_count'] = matches
            features[f'{category}_keyword_ratio'] = matches / len(keywords) if keywords else 0
            
            # 权重匹配（重要关键词权重更高）
            important_keywords = keywords[:5]  # 前5个是重要关键词
            important_matches = sum(2 for keyword in important_keywords if keyword in text_lower)
            features[f'{category}_important_score'] = important_matches
        
        # 文本长度特征
        features['text_length'] = len(text)
        features['word_count'] = len(text.split())
        
        # 数字特征（金额、百分比等）
        features['has_percentage'] = 1 if re.search(r'\d+%', text) else 0
        features['has_amount'] = 1 if re.search(r'\d+.*[万亿]', text) else 0
        
        return features
    
    def enhanced_text_preprocessing(self, text: str) -> str:
        """增强文本预处理"""
        if not text:
            return ""
        
        text = str(text).strip()
        
        # 标准化数字表达
        text = re.sub(r'(\d+)万', r'\1万', text)
        text = re.sub(r'(\d+)亿', r'\1亿', text)
        text = re.sub(r'(\d+)%', r'\1%', text)
        
        # 标准化常见词汇
        replacements = {
            '净利润': '净利润', '营业收入': '营业收入', '业绩预告': '业绩预告',
            '风险提示': '风险提示', '股东大会': '股东大会', '董事会': '董事会'
        }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        # 保留中文、数字和重要标点
        text = re.sub(r'[^\u4e00-\u9fff0-9%．。，、；：！？（）【】万亿]', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def extract_super_features(self, data_list: List[Dict]) -> Tuple[np.ndarray, List[str]]:
        """提取超级特征"""
        text_features = []
        domain_features_list = []
        labels = []
        
        for item in data_list:
            # 文本特征
            text_parts = []
            
            # 多字段融合
            for field in ['title', 'summary', 'keyFactors', 'riskFactors', 'eventTags']:
                content = item.get(field, '')
                if content:
                    if isinstance(content, list):
                        text_parts.extend([self.enhanced_text_preprocessing(str(c)) for c in content])
                    else:
                        text_parts.append(self.enhanced_text_preprocessing(str(content)))
            
            combined_text = ' '.join([t for t in text_parts if t])
            
            if combined_text and item.get('llm_rating'):
                text_features.append(combined_text)
                
                # 领域特征
                domain_features = self.create_domain_features(combined_text)
                domain_features_list.append(list(domain_features.values()))
                
                labels.append(self.rating_mapping.get(item['llm_rating'], 'NEUTRAL'))
        
        return np.array(domain_features_list), text_features, labels
    
    def create_ensemble_model(self) -> VotingClassifier:
        """创建集成模型"""
        
        # 基础分类器
        rf_classifier = RandomForestClassifier(
            n_estimators=100,  # 减少树的数量
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2,
            class_weight='balanced',
            random_state=42,
            n_jobs=1  # 禁用并行处理
        )
        
        lr_classifier = LogisticRegression(
            class_weight='balanced',
            max_iter=2000,
            C=1.0,
            random_state=42
        )
        
        svm_classifier = SVC(
            class_weight='balanced',
            kernel='rbf',
            C=1.0,
            probability=True,
            random_state=42
        )
        
        # 集成分类器
        ensemble = VotingClassifier(
            estimators=[
                ('rf', rf_classifier),
                ('lr', lr_classifier),
                ('svm', svm_classifier)
            ],
            voting='soft'  # 使用概率投票
        )
        
        return ensemble
    
    def train_super_model(self, train_data: List[Dict]) -> Dict:
        """训练超级模型"""
        logger.info(f"🚀 开始训练超级模型 ({len(train_data)} 个样本)...")
        
        # 1. 提取超级特征
        domain_features, text_features, labels = self.extract_super_features(train_data)
        logger.info(f"提取特征: {len(text_features)} 个样本, {domain_features.shape[1]} 个领域特征")
        
        if len(text_features) < 10:
            logger.error("❌ 训练样本太少")
            return {}
        
        # 2. 平衡数据集（更智能的平衡策略）
        balanced_indices = self.smart_balance_dataset(labels)
        
        balanced_domain_features = domain_features[balanced_indices]
        balanced_text_features = [text_features[i] for i in balanced_indices]
        balanced_labels = [labels[i] for i in balanced_indices]
        
        logger.info(f"平衡后数据分布: {dict(Counter(balanced_labels))}")
        
        # 3. 文本向量化（优化参数）
        self.vectorizer = TfidfVectorizer(
            max_features=3000,  # 增加特征数
            ngram_range=(1, 3),  # 1-3元语法
            min_df=2,
            max_df=0.7,
            sublinear_tf=True,  # 使用对数TF
            use_idf=True
        )
        
        text_vectors = self.vectorizer.fit_transform(balanced_text_features)
        
        # 4. 特征融合
        combined_features = np.hstack([
            text_vectors.toarray(),
            balanced_domain_features
        ])
        
        logger.info(f"融合特征维度: {combined_features.shape}")
        
        # 5. 创建和训练集成模型
        self.ensemble_model = self.create_ensemble_model()
        
        logger.info("🤖 训练集成模型...")
        self.ensemble_model.fit(combined_features, balanced_labels)
        
        # 6. 交叉验证
        from sklearn.model_selection import cross_val_score
        cv_scores = cross_val_score(
            self.ensemble_model,
            combined_features,
            balanced_labels,
            cv=3,  # 减少折数
            scoring='accuracy',
            n_jobs=1  # 禁用并行处理
        )
        
        training_stats = {
            'model_type': 'super_ensemble',
            'training_samples': len(balanced_labels),
            'text_features': text_vectors.shape[1],
            'domain_features': balanced_domain_features.shape[1],
            'total_features': combined_features.shape[1],
            'class_distribution': dict(Counter(balanced_labels)),
            'cv_accuracy_mean': cv_scores.mean(),
            'cv_accuracy_std': cv_scores.std()
        }
        
        self.training_stats = training_stats
        
        logger.info(f"✅ 超级模型训练完成:")
        logger.info(f"  交叉验证准确率: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")
        logger.info(f"  总特征数: {combined_features.shape[1]}")
        logger.info(f"  训练样本: {len(balanced_labels)}")
        
        return training_stats
    
    def smart_balance_dataset(self, labels: List[str]) -> List[int]:
        """智能平衡数据集"""
        label_indices = defaultdict(list)
        for i, label in enumerate(labels):
            label_indices[label].append(i)
        
        # 计算目标样本数（基于最小类别的2-3倍）
        min_count = min(len(indices) for indices in label_indices.values())
        target_count = min(min_count * 2, 800)  # 每类最多800个样本
        
        balanced_indices = []
        import random
        random.seed(42)
        
        for label, indices in label_indices.items():
            if len(indices) <= target_count:
                balanced_indices.extend(indices)
            else:
                # 随机采样
                sampled_indices = random.sample(indices, target_count)
                balanced_indices.extend(sampled_indices)
        
        random.shuffle(balanced_indices)
        return balanced_indices
    
    def predict_super(self, text: str) -> Tuple[str, float]:
        """超级预测"""
        if not self.ensemble_model or not self.vectorizer:
            return 'NEUTRAL', 0.0
        
        # 预处理
        processed_text = self.enhanced_text_preprocessing(text)
        if not processed_text:
            return 'NEUTRAL', 0.0
        
        # 文本特征
        text_vector = self.vectorizer.transform([processed_text])
        
        # 领域特征
        domain_features = self.create_domain_features(processed_text)
        domain_vector = np.array([list(domain_features.values())])
        
        # 特征融合
        combined_features = np.hstack([text_vector.toarray(), domain_vector])
        
        # 预测
        prediction = self.ensemble_model.predict(combined_features)[0]
        probabilities = self.ensemble_model.predict_proba(combined_features)[0]
        confidence = max(probabilities)
        
        return prediction, confidence
    
    def evaluate_super_model(self, test_data: List[Dict]) -> Dict:
        """评估超级模型"""
        logger.info(f"📊 评估超级模型 ({len(test_data)} 个样本)...")
        
        if not self.ensemble_model or not self.vectorizer:
            logger.error("❌ 模型未训练")
            return {}
        
        # 提取测试特征
        domain_features, text_features, test_labels = self.extract_super_features(test_data)
        
        if not text_features:
            logger.error("❌ 无法提取测试特征")
            return {}
        
        # 特征处理
        text_vectors = self.vectorizer.transform(text_features)
        combined_features = np.hstack([text_vectors.toarray(), domain_features])
        
        # 预测
        predictions = self.ensemble_model.predict(combined_features)
        probabilities = self.ensemble_model.predict_proba(combined_features)
        
        # 评估
        accuracy = (predictions == test_labels).mean()
        report = classification_report(test_labels, predictions, output_dict=True)
        
        # 按类别统计
        unique_labels = np.unique(list(test_labels) + list(predictions))
        category_stats = {}
        
        for label in unique_labels:
            mask = np.array(test_labels) == label
            if mask.sum() > 0:
                category_accuracy = (predictions[mask] == np.array(test_labels)[mask]).mean()
                category_stats[label] = {
                    'accuracy': category_accuracy,
                    'count': mask.sum(),
                    'precision': report.get(label, {}).get('precision', 0),
                    'recall': report.get(label, {}).get('recall', 0),
                    'f1_score': report.get(label, {}).get('f1-score', 0)
                }
        
        results = {
            'overall_accuracy': accuracy,
            'category_stats': category_stats,
            'classification_report': report,
            'test_samples': len(text_features)
        }
        
        logger.info(f"✅ 超级模型评估完成:")
        logger.info(f"  总体准确率: {accuracy:.3f}")
        for label, stats in category_stats.items():
            logger.info(f"  {label}: 准确率 {stats['accuracy']:.3f}, F1 {stats['f1_score']:.3f}")
        
        return results

def test_super_teacher():
    """测试超级Python老师"""
    print("🚀 测试超级Python老师")
    print("="*50)
    
    # 导入测试系统
    from teacher_self_test import TeacherSelfTest
    
    # 创建测试系统
    tester = TeacherSelfTest()
    
    # 加载更多数据
    llm_data = tester.load_llm_data(sample_size=5000)  # 增加到5000样本
    if not llm_data:
        print("❌ 无法加载数据")
        return
    
    # 分割数据
    train_set, test_set = tester.split_train_test(llm_data, train_ratio=0.8)
    
    # 创建超级老师
    super_teacher = SuperPythonTeacher()
    
    # 训练模型
    training_results = super_teacher.train_super_model(train_set)
    
    if training_results:
        print(f"\n🎓 超级训练结果:")
        print(f"  交叉验证准确率: {training_results['cv_accuracy_mean']:.3f}")
        print(f"  总特征数: {training_results['total_features']}")
        print(f"  训练样本: {training_results['training_samples']}")
        
        # 评估
        evaluation_results = super_teacher.evaluate_super_model(test_set)
        
        if evaluation_results:
            print(f"\n📊 超级测试集评估:")
            print(f"  总体准确率: {evaluation_results['overall_accuracy']:.3f}")
            
            for label, stats in evaluation_results['category_stats'].items():
                print(f"  {label}: 准确率 {stats['accuracy']:.3f}, F1 {stats['f1_score']:.3f}")
            
            # 结论
            overall_acc = evaluation_results['overall_accuracy']
            if overall_acc >= 0.85:
                print(f"\n🎉 超级老师达到目标！准确率 {overall_acc:.1%} >= 85%")
            elif overall_acc >= 0.80:
                print(f"\n🔥 超级老师表现优秀！准确率 {overall_acc:.1%} >= 80%")
            else:
                print(f"\n💪 超级老师还在努力，准确率 {overall_acc:.1%}")

if __name__ == "__main__":
    test_super_teacher()
