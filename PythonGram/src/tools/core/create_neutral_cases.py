#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中性案例数据提取工具
从现有的中性过滤器分析结果中提取中性案例，生成neutral_cases.csv文件
用于规则回测和性能评估
"""

import pandas as pd
import os
import re
import logging
from typing import List, Dict, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NeutralCaseExtractor:
    def __init__(self, output_dir: str = "."):
        self.output_dir = output_dir
        self.neutral_cases = []
        
    def extract_from_negative_cases(self, negative_csv: str = "negative_cases.csv") -> None:
        """
        从negative_cases.csv中提取可能的中性案例
        基于LLM评级和事件类型进行筛选
        """
        try:
            if not os.path.exists(negative_csv):
                logger.warning(f"负面案例文件不存在: {negative_csv}")
                return
                
            df = pd.read_csv(negative_csv, encoding='utf-8')
            logger.info(f"加载负面案例数据: {len(df)} 条")
            
            # 筛选可能的中性案例
            neutral_candidates = []
            
            for _, row in df.iterrows():
                # 检查LLM评级
                rating_level = str(row.get('ratingLevel', '')).strip()
                sentiment_score = row.get('sentimentScore', 0)
                
                # 检查事件类型
                event_tags = str(row.get('eventTags', '')).strip()
                reasoning = str(row.get('reasoning', '')).strip()
                
                # 中性判断条件
                is_neutral_candidate = False
                neutral_reason = ""
                
                # 条件1：LLM评级为中性
                if rating_level in ['中', '中性', '一般']:
                    is_neutral_candidate = True
                    neutral_reason = f"LLM评级: {rating_level}"
                
                # 条件2：情感分数接近0
                elif abs(sentiment_score) < 0.1:
                    is_neutral_candidate = True
                    neutral_reason = f"情感分数: {sentiment_score:.2f}"
                
                # 条件3：事件类型为中性
                elif any(tag in event_tags for tag in ['例行', '常规', '通知', '公告', '披露']):
                    is_neutral_candidate = True
                    neutral_reason = f"事件类型: {event_tags}"
                
                # 条件4：推理中包含中性关键词
                elif any(keyword in reasoning for keyword in ['例行', '常规', '通知', '股东大会', '定期报告']):
                    is_neutral_candidate = True
                    neutral_reason = f"推理内容: {reasoning[:50]}..."
                
                if is_neutral_candidate:
                    neutral_candidates.append({
                        'fileName': row.get('fileName', ''),
                        'stockCode': row.get('stockCode', ''),
                        'tradeDate': row.get('tradeDate', ''),
                        'title': row.get('title', ''),
                        'summary': row.get('summary', ''),
                        'keyFactors': row.get('keyFactors', ''),
                        'reasoning': row.get('reasoning', ''),
                        'ratingLevel': rating_level,
                        'sentimentScore': sentiment_score,
                        'eventTags': event_tags,
                        'neutralReason': neutral_reason,
                        'source': 'negative_cases_extraction'
                    })
            
            logger.info(f"提取到 {len(neutral_candidates)} 个中性候选案例")
            self.neutral_cases.extend(neutral_candidates)
            
        except Exception as e:
            logger.error(f"从负面案例提取中性案例失败: {e}")
    
    def extract_from_annotation_workflow(self, annotation_dir: str = "../annotation_workflow") -> None:
        """
        从标注工作流中提取中性案例
        """
        try:
            # 查找标注结果文件
            annotation_files = []
            if os.path.exists(annotation_dir):
                for root, dirs, files in os.walk(annotation_dir):
                    for file in files:
                        if file.endswith('.json') and 'annotation' in file.lower():
                            annotation_files.append(os.path.join(root, file))
            
            logger.info(f"找到 {len(annotation_files)} 个标注文件")
            
            for file_path in annotation_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = pd.read_json(f)
                    
                    # 提取中性标注
                    if 'label' in data.columns:
                        neutral_data = data[data['label'] == '中性']
                        for _, row in neutral_data.iterrows():
                            self.neutral_cases.append({
                                'fileName': row.get('fileName', ''),
                                'stockCode': row.get('stockCode', ''),
                                'tradeDate': row.get('tradeDate', ''),
                                'title': row.get('title', ''),
                                'summary': row.get('summary', ''),
                                'keyFactors': row.get('keyFactors', ''),
                                'reasoning': row.get('reasoning', ''),
                                'ratingLevel': '中性',
                                'sentimentScore': 0.0,
                                'eventTags': '标注为中性',
                                'neutralReason': '人工标注',
                                'source': 'annotation_workflow'
                            })
                            
                except Exception as e:
                    logger.warning(f"处理标注文件失败 {file_path}: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"从标注工作流提取中性案例失败: {e}")
    
    def create_synthetic_neutral_cases(self) -> None:
        """
        创建合成的中性案例，基于常见的中性公告模式
        """
        synthetic_cases = [
            {
                'fileName': 'synthetic_001.pdf',
                'stockCode': '000001',
                'tradeDate': '2025-01-01',
                'title': '关于召开2024年年度股东大会的通知',
                'summary': '公司将于2024年3月15日召开年度股东大会，审议年度报告等议案',
                'keyFactors': '股东大会,年度报告,例行程序',
                'reasoning': '这是例行的年度股东大会通知，属于程序性事项，无实质影响',
                'ratingLevel': '中性',
                'sentimentScore': 0.0,
                'eventTags': '股东大会,例行',
                'neutralReason': '合成案例-例行程序',
                'source': 'synthetic'
            },
            {
                'fileName': 'synthetic_002.pdf',
                'stockCode': '000002',
                'tradeDate': '2025-01-02',
                'title': '关于公司名称变更的公告',
                'summary': '公司名称由"XX股份"变更为"YY股份"，已完成工商变更登记',
                'keyFactors': '名称变更,工商登记,程序性',
                'reasoning': '公司名称变更是程序性事项，不影响公司经营和财务状况',
                'ratingLevel': '中性',
                'sentimentScore': 0.0,
                'eventTags': '名称变更,程序性',
                'neutralReason': '合成案例-程序性变更',
                'source': 'synthetic'
            },
            {
                'fileName': 'synthetic_003.pdf',
                'stockCode': '000003',
                'tradeDate': '2025-01-03',
                'title': '关于股权登记日的提示性公告',
                'summary': '公司2024年度权益分派股权登记日为2024年3月20日',
                'keyFactors': '权益分派,股权登记,例行',
                'reasoning': '年度权益分派是例行程序，股权登记日公告属于提示性信息',
                'ratingLevel': '中性',
                'sentimentScore': 0.0,
                'eventTags': '权益分派,例行',
                'neutralReason': '合成案例-例行提示',
                'source': 'synthetic'
            }
        ]
        
        self.neutral_cases.extend(synthetic_cases)
        logger.info(f"创建了 {len(synthetic_cases)} 个合成中性案例")
    
    def save_neutral_cases(self, filename: str = "neutral_cases.csv") -> None:
        """
        保存中性案例到CSV文件
        """
        if not self.neutral_cases:
            logger.warning("没有中性案例可保存")
            return
            
        try:
            df = pd.DataFrame(self.neutral_cases)
            
            # 确保输出目录存在
            os.makedirs(self.output_dir, exist_ok=True)
            
            output_path = os.path.join(self.output_dir, filename)
            df.to_csv(output_path, index=False, encoding='utf-8')
            
            logger.info(f"成功保存 {len(self.neutral_cases)} 个中性案例到: {output_path}")
            
            # 显示统计信息
            print(f"\n=== 中性案例统计 ===")
            print(f"总案例数: {len(self.neutral_cases)}")
            print(f"来源分布:")
            source_counts = df['source'].value_counts()
            for source, count in source_counts.items():
                print(f"  {source}: {count}")
            print(f"文件已保存到: {output_path}")
            
        except Exception as e:
            logger.error(f"保存中性案例失败: {e}")
    
    def run_extraction(self) -> None:
        """
        运行完整的提取流程
        """
        logger.info("开始提取中性案例...")
        
        # 1. 从负面案例中提取
        self.extract_from_negative_cases()
        
        # 2. 从标注工作流中提取
        self.extract_from_annotation_workflow()
        
        # 3. 创建合成案例
        self.create_synthetic_neutral_cases()
        
        # 4. 保存结果
        self.save_neutral_cases()
        
        logger.info("中性案例提取完成")

def main():
    """主函数"""
    extractor = NeutralCaseExtractor()
    extractor.run_extraction()

if __name__ == "__main__":
    main()






