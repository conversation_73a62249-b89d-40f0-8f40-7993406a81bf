#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信息增益评分器

负责计算每个词条的信息增益、卡方值等统计指标，以评估其对分类的贡献度。
"""
import pandas as pd
import numpy as np
from collections import Counter
from typing import Dict, List
from scipy.stats import chi2_contingency
import logging

logger = logging.getLogger(__name__)

class InformationGainScorer:
    def __init__(self, categories: List[str] = None):
        self.categories = categories if categories else ["positive", "negative", "neutral"]

    def _calculate_entropy(self, probabilities: List[float]) -> float:
        """计算熵"""
        return -sum(p * np.log2(p) for p in probabilities if p > 0)

    def calculate_scores(
        self,
        doc_counts: Dict[str, Counter],
        term_freqs: Dict[str, Counter],
        total_docs: int,
        category_dist: Dict[str, int]) -> pd.DataFrame:
        """
        计算所有词条的评分，包括信息增益和卡方检验。
        """
        logger.info("开始计算所有词条的评分...")
        
        # 1. 计算总熵
        total_probs = [count / total_docs for count in category_dist.values()]
        total_entropy = self._calculate_entropy(total_probs)
        logger.info(f"系统总熵: {total_entropy:.4f}")

        all_words = set()
        for cat_counter in doc_counts.values():
            all_words.update(cat_counter.keys())

        results = []
        for word in all_words:
            # --- 信息增益计算 (使用文档频率) ---
            # N(w, c_i) - 包含词w的c_i类文档数
            docs_with_word_per_cat = {cat: doc_counts[cat].get(word, 0) for cat in self.categories}
            # N(w) - 包含词w的总文档数
            total_docs_with_word = sum(docs_with_word_per_cat.values())
            # N(not w) - 不包含词w的总文档数
            total_docs_without_word = total_docs - total_docs_with_word

            # P(word)
            prob_word = total_docs_with_word / total_docs if total_docs > 0 else 0
            # P(not word)
            prob_not_word = 1 - prob_word

            # H(C|word)
            # P(c_i|w) = N(w, c_i) / N(w)
            probs_given_word = [
                count / total_docs_with_word for count in docs_with_word_per_cat.values()
            ] if total_docs_with_word > 0 else [0] * len(self.categories)
            entropy_given_word = self._calculate_entropy(probs_given_word)

            # H(C|not word)
            # N(not w, c_i) = N(c_i) - N(w, c_i)
            docs_without_word_per_cat = {
                cat: category_dist[cat] - docs_with_word_per_cat[cat] for cat in self.categories
            }
            probs_given_not_word = [
                count / total_docs_without_word for count in docs_without_word_per_cat.values()
            ] if total_docs_without_word > 0 else [0] * len(self.categories)
            entropy_not_word = self._calculate_entropy(probs_given_not_word)

            info_gain = total_entropy - (prob_word * entropy_given_word + prob_not_word * entropy_not_word)

            # --- 为每个类别计算卡方检验 ---
            chi2_scores = {}
            p_values = {}
            for cat in self.categories:
                # A = 在当前类别(cat)中，包含该词的文档数
                A = doc_counts[cat].get(word, 0)
                # B = 在其他类别中，包含该词的文档数
                B = total_docs_with_word - A
                # C = 在当前类别(cat)中，不包含该词的文档数
                C = category_dist.get(cat, 0) - A
                # D = 在其他类别中，不包含该词的文档数
                D = total_docs_without_word - B

                contingency_table = [[A, B], [C, D]]
                
                try:
                    chi2, p_value, _, _ = chi2_contingency(contingency_table, correction=False)
                    chi2_scores[cat] = chi2
                    p_values[cat] = p_value
                except ValueError:
                    chi2_scores[cat] = 0.0
                    p_values[cat] = 1.0

            # --- 汇总结果 (同时报告文档频率DF和总词频TF) ---
            total_tf = sum(term_freqs[cat].get(word, 0) for cat in self.categories)
            results.append({
                '词条': word,
                '信息增益': info_gain,
                '卡方值_负向': chi2_scores.get('negative', 0),
                'P值_负向': p_values.get('negative', 1.0),
                '卡方值_正向': chi2_scores.get('positive', 0),
                'P值_正向': p_values.get('positive', 1.0),
                '卡方值_中性': chi2_scores.get('neutral', 0),
                'P值_中性': p_values.get('neutral', 1.0),
                '总文档数': total_docs_with_word,
                '负向文档数': doc_counts['negative'].get(word, 0),
                '正向文档数': doc_counts['positive'].get(word, 0),
                '中性文档数': doc_counts['neutral'].get(word, 0),
                '总加权词频': total_tf,
                '负向加权词频': term_freqs['negative'].get(word, 0),
                '正向加权词频': term_freqs['positive'].get(word, 0),
                '中性加权词频': term_freqs['neutral'].get(word, 0),
            })

        df = pd.DataFrame(results)
        
        # 过滤掉在任何一个类别中P值都不显著的词条
        significant_mask = (df['P值_负向'] < 0.05) | (df['P值_正向'] < 0.05) | (df['P值_中性'] < 0.05)
        df = df[significant_mask].copy()

        # 为了方便，我们可以创建一个总的卡方值用于默认排序
        df['卡方总分'] = df['卡方值_负向'] + df['卡方值_正向'] + df['卡方值_中性']
        df = df.sort_values(by='卡方总分', ascending=False)
        logger.info(f"评分计算完成，生成 {len(df)} 个显著性词条。")
        return df