#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级Python老师学习系统
使用机器学习技术提高学习效果，目标准确率85%+
"""

import json
import logging
import numpy as np
import pandas as pd
from pathlib import Path
from collections import Counter, defaultdict
from typing import Dict, List, Tuple, Optional
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import cross_val_score
from sklearn.utils.class_weight import compute_class_weight
import joblib
import re

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AdvancedPythonTeacher:
    """高级Python老师 - 使用机器学习技术"""
    
    def __init__(self, model_type: str = "random_forest"):
        """
        初始化高级Python老师
        
        Args:
            model_type: 模型类型 ("random_forest", "logistic_regression")
        """
        self.model_type = model_type
        self.vectorizer = None
        self.classifier = None
        self.class_weights = None
        self.feature_names = None
        self.training_stats = {}
        
        # 评级映射
        self.rating_mapping = {
            '优': 'POSITIVE',
            '好': 'POSITIVE', 
            '中': 'NEUTRAL',
            '差': 'NEGATIVE',
            '劣': 'NEGATIVE'
        }
        
        logger.info(f"🧠 高级Python老师初始化完成，模型类型: {model_type}")
    
    def preprocess_text(self, text: str) -> str:
        """
        文本预处理
        
        Args:
            text: 原始文本
            
        Returns:
            预处理后的文本
        """
        if not text:
            return ""
        
        # 转换为字符串并清理
        text = str(text).strip()
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 提取中文字符和重要的标点符号
        text = re.sub(r'[^\u4e00-\u9fff0-9%．。，、；：！？（）【】]', ' ', text)
        
        return text
    
    def extract_enhanced_features(self, data_list: List[Dict]) -> Tuple[List[str], List[str]]:
        """
        提取增强特征
        
        Args:
            data_list: 数据列表
            
        Returns:
            (特征文本列表, 标签列表)
        """
        features = []
        labels = []
        
        for item in data_list:
            # 组合多个文本字段
            text_parts = []
            
            # 标题/内容前100字符
            title_content = self.preprocess_text(item.get('title', ''))
            if title_content:
                text_parts.append(title_content)
            
            # 摘要
            summary = self.preprocess_text(item.get('summary', ''))
            if summary:
                text_parts.append(summary)
            
            # 关键因素
            key_factors = item.get('keyFactors', [])
            if key_factors:
                if isinstance(key_factors, list):
                    text_parts.extend([self.preprocess_text(str(f)) for f in key_factors])
                else:
                    text_parts.append(self.preprocess_text(str(key_factors)))
            
            # 风险因素
            risk_factors = item.get('riskFactors', [])
            if risk_factors:
                if isinstance(risk_factors, list):
                    text_parts.extend([self.preprocess_text(str(f)) for f in risk_factors])
                else:
                    text_parts.append(self.preprocess_text(str(risk_factors)))
            
            # 事件标签
            event_tags = item.get('eventTags', [])
            if event_tags:
                if isinstance(event_tags, list):
                    text_parts.extend([self.preprocess_text(str(t)) for t in event_tags])
                else:
                    text_parts.append(self.preprocess_text(str(event_tags)))
            
            # 合并所有文本
            combined_text = ' '.join([t for t in text_parts if t])
            
            if combined_text and item.get('llm_rating'):
                features.append(combined_text)
                labels.append(self.rating_mapping.get(item['llm_rating'], 'NEUTRAL'))
        
        return features, labels
    
    def balance_dataset(self, features: List[str], labels: List[str]) -> Tuple[List[str], List[str]]:
        """
        平衡数据集
        
        Args:
            features: 特征列表
            labels: 标签列表
            
        Returns:
            平衡后的(特征, 标签)
        """
        # 统计各类别数量
        label_counts = Counter(labels)
        logger.info(f"原始数据分布: {dict(label_counts)}")
        
        # 找到最少的类别数量
        min_count = min(label_counts.values())
        target_count = min(min_count * 3, 500)  # 每个类别最多500个样本
        
        # 按类别分组
        grouped_data = defaultdict(list)
        for feature, label in zip(features, labels):
            grouped_data[label].append(feature)
        
        # 平衡采样
        balanced_features = []
        balanced_labels = []
        
        for label, feature_list in grouped_data.items():
            # 如果样本太多，随机采样
            if len(feature_list) > target_count:
                import random
                random.seed(42)
                sampled_features = random.sample(feature_list, target_count)
            else:
                sampled_features = feature_list
            
            balanced_features.extend(sampled_features)
            balanced_labels.extend([label] * len(sampled_features))
        
        # 打乱数据
        import random
        random.seed(42)
        combined = list(zip(balanced_features, balanced_labels))
        random.shuffle(combined)
        balanced_features, balanced_labels = zip(*combined)
        
        balanced_counts = Counter(balanced_labels)
        logger.info(f"平衡后数据分布: {dict(balanced_counts)}")
        
        return list(balanced_features), list(balanced_labels)
    
    def train_advanced_model(self, train_data: List[Dict]) -> Dict:
        """
        训练高级模型
        
        Args:
            train_data: 训练数据
            
        Returns:
            训练结果
        """
        logger.info(f"🎓 开始训练高级模型 ({len(train_data)} 个样本)...")
        
        # 1. 提取特征
        features, labels = self.extract_enhanced_features(train_data)
        logger.info(f"提取特征: {len(features)} 个样本")
        
        if len(features) < 10:
            logger.error("❌ 训练样本太少")
            return {}
        
        # 2. 平衡数据集
        balanced_features, balanced_labels = self.balance_dataset(features, labels)
        
        # 3. 文本向量化
        logger.info("🔤 文本向量化...")
        self.vectorizer = TfidfVectorizer(
            max_features=2000,  # 最多2000个特征
            ngram_range=(1, 2),  # 1-2元语法
            min_df=2,  # 至少出现2次
            max_df=0.8,  # 最多出现在80%的文档中
            stop_words=None  # 不使用停用词（中文需要特殊处理）
        )
        
        X = self.vectorizer.fit_transform(balanced_features)
        y = np.array(balanced_labels)
        
        # 4. 计算类别权重
        unique_labels = np.unique(y)
        self.class_weights = compute_class_weight(
            'balanced', 
            classes=unique_labels, 
            y=y
        )
        class_weight_dict = dict(zip(unique_labels, self.class_weights))
        logger.info(f"类别权重: {class_weight_dict}")
        
        # 5. 选择和训练模型
        if self.model_type == "random_forest":
            self.classifier = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                class_weight='balanced',
                random_state=42,
                n_jobs=-1
            )
        else:  # logistic_regression
            self.classifier = LogisticRegression(
                class_weight='balanced',
                max_iter=1000,
                random_state=42
            )
        
        logger.info(f"🤖 训练 {self.model_type} 模型...")
        self.classifier.fit(X, y)
        
        # 6. 交叉验证
        cv_scores = cross_val_score(self.classifier, X, y, cv=5, scoring='accuracy')
        
        # 7. 特征重要性分析
        feature_names = self.vectorizer.get_feature_names_out()
        if hasattr(self.classifier, 'feature_importances_'):
            # Random Forest
            importances = self.classifier.feature_importances_
            top_features = sorted(
                zip(feature_names, importances), 
                key=lambda x: x[1], 
                reverse=True
            )[:20]
        else:
            # Logistic Regression
            if len(unique_labels) == 2:
                importances = abs(self.classifier.coef_[0])
            else:
                importances = np.mean(abs(self.classifier.coef_), axis=0)
            
            top_features = sorted(
                zip(feature_names, importances), 
                key=lambda x: x[1], 
                reverse=True
            )[:20]
        
        # 8. 训练统计
        training_stats = {
            'model_type': self.model_type,
            'training_samples': len(balanced_features),
            'feature_count': X.shape[1],
            'class_distribution': dict(Counter(balanced_labels)),
            'cv_accuracy_mean': cv_scores.mean(),
            'cv_accuracy_std': cv_scores.std(),
            'top_features': top_features
        }
        
        self.training_stats = training_stats
        
        logger.info(f"✅ 模型训练完成:")
        logger.info(f"  交叉验证准确率: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")
        logger.info(f"  特征数量: {X.shape[1]}")
        logger.info(f"  训练样本: {len(balanced_features)}")
        
        return training_stats
    
    def predict(self, text: str) -> Tuple[str, float]:
        """
        预测单个文本
        
        Args:
            text: 输入文本
            
        Returns:
            (预测标签, 置信度)
        """
        if not self.classifier or not self.vectorizer:
            return 'NEUTRAL', 0.0
        
        # 预处理文本
        processed_text = self.preprocess_text(text)
        if not processed_text:
            return 'NEUTRAL', 0.0
        
        # 向量化
        X = self.vectorizer.transform([processed_text])
        
        # 预测
        prediction = self.classifier.predict(X)[0]
        probabilities = self.classifier.predict_proba(X)[0]
        
        # 获取置信度
        max_prob = max(probabilities)
        
        return prediction, max_prob
    
    def evaluate_on_test_set(self, test_data: List[Dict]) -> Dict:
        """
        在测试集上评估模型
        
        Args:
            test_data: 测试数据
            
        Returns:
            评估结果
        """
        logger.info(f"📊 在测试集上评估模型 ({len(test_data)} 个样本)...")
        
        if not self.classifier or not self.vectorizer:
            logger.error("❌ 模型未训练")
            return {}
        
        # 提取测试特征
        test_features, test_labels = self.extract_enhanced_features(test_data)
        
        if not test_features:
            logger.error("❌ 无法提取测试特征")
            return {}
        
        # 向量化
        X_test = self.vectorizer.transform(test_features)
        y_test = np.array(test_labels)
        
        # 预测
        y_pred = self.classifier.predict(X_test)
        y_proba = self.classifier.predict_proba(X_test)
        
        # 计算指标
        accuracy = (y_pred == y_test).mean()
        
        # 详细报告
        report = classification_report(y_test, y_pred, output_dict=True)
        conf_matrix = confusion_matrix(y_test, y_pred)
        
        # 按类别统计
        unique_labels = np.unique(np.concatenate([y_test, y_pred]))
        category_stats = {}
        
        for label in unique_labels:
            mask = y_test == label
            if mask.sum() > 0:
                category_accuracy = (y_pred[mask] == y_test[mask]).mean()
                category_stats[label] = {
                    'accuracy': category_accuracy,
                    'count': mask.sum(),
                    'precision': report.get(label, {}).get('precision', 0),
                    'recall': report.get(label, {}).get('recall', 0),
                    'f1_score': report.get(label, {}).get('f1-score', 0)
                }
        
        evaluation_results = {
            'overall_accuracy': accuracy,
            'category_stats': category_stats,
            'classification_report': report,
            'confusion_matrix': conf_matrix.tolist(),
            'test_samples': len(test_features)
        }
        
        logger.info(f"✅ 评估完成:")
        logger.info(f"  总体准确率: {accuracy:.3f}")
        for label, stats in category_stats.items():
            logger.info(f"  {label}: 准确率 {stats['accuracy']:.3f}, F1 {stats['f1_score']:.3f}")
        
        return evaluation_results
    
    def save_model(self, model_path: str = "advanced_python_teacher_model.joblib"):
        """保存模型"""
        if self.classifier and self.vectorizer:
            model_data = {
                'classifier': self.classifier,
                'vectorizer': self.vectorizer,
                'model_type': self.model_type,
                'training_stats': self.training_stats,
                'rating_mapping': self.rating_mapping
            }
            
            joblib.dump(model_data, model_path)
            logger.info(f"💾 模型已保存: {model_path}")
        else:
            logger.error("❌ 没有训练好的模型可保存")
    
    def load_model(self, model_path: str = "advanced_python_teacher_model.joblib"):
        """加载模型"""
        try:
            model_data = joblib.load(model_path)
            
            self.classifier = model_data['classifier']
            self.vectorizer = model_data['vectorizer']
            self.model_type = model_data['model_type']
            self.training_stats = model_data.get('training_stats', {})
            self.rating_mapping = model_data.get('rating_mapping', self.rating_mapping)
            
            logger.info(f"📥 模型已加载: {model_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 模型加载失败: {e}")
            return False

def test_advanced_teacher():
    """测试高级Python老师"""
    print("🧪 测试高级Python老师")
    print("="*50)
    
    # 导入测试系统
    from teacher_self_test import TeacherSelfTest
    
    # 创建测试系统
    tester = TeacherSelfTest()
    
    # 加载数据
    llm_data = tester.load_llm_data(sample_size=3000)  # 增加样本数
    if not llm_data:
        print("❌ 无法加载数据")
        return
    
    # 分割数据
    train_set, test_set = tester.split_train_test(llm_data, train_ratio=0.8)
    
    # 创建高级老师
    teacher = AdvancedPythonTeacher(model_type="random_forest")
    
    # 训练模型
    training_results = teacher.train_advanced_model(train_set)
    
    if training_results:
        print(f"\n🎓 训练结果:")
        print(f"  交叉验证准确率: {training_results['cv_accuracy_mean']:.3f}")
        print(f"  特征数量: {training_results['feature_count']}")
        print(f"  训练样本: {training_results['training_samples']}")
        
        # 在测试集上评估
        evaluation_results = teacher.evaluate_on_test_set(test_set)
        
        if evaluation_results:
            print(f"\n📊 测试集评估:")
            print(f"  总体准确率: {evaluation_results['overall_accuracy']:.3f}")
            
            for label, stats in evaluation_results['category_stats'].items():
                print(f"  {label}: 准确率 {stats['accuracy']:.3f}, "
                      f"精确率 {stats['precision']:.3f}, "
                      f"召回率 {stats['recall']:.3f}, "
                      f"F1 {stats['f1_score']:.3f}")
            
            # 保存模型
            teacher.save_model()
            
            # 结论
            overall_acc = evaluation_results['overall_accuracy']
            if overall_acc >= 0.85:
                print(f"\n🎉 高级老师达到目标！准确率 {overall_acc:.1%} >= 85%")
            elif overall_acc >= 0.75:
                print(f"\n👍 高级老师表现良好，准确率 {overall_acc:.1%}，接近目标")
            else:
                print(f"\n⚠️ 高级老师需要进一步改进，准确率 {overall_acc:.1%}")

if __name__ == "__main__":
    test_advanced_teacher()
