#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化规则引擎
使用声明式规则管理，支持回退到传统正则匹配
"""

import re
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class FilterResult(Enum):
    """过滤结果枚举"""
    POSITIVE = ("正面", "明显正面公告")
    NEGATIVE = ("负面", "明显负面公告") 
    NEUTRAL = ("中性", "明确中性公告")
    UNCERTAIN = ("不确定", "需要LLM进一步分析")
    
    def __init__(self, label: str, description: str):
        self.label = label
        self.description = description

@dataclass
class ClassificationResult:
    """分类结果数据类"""
    result: FilterResult
    confidence: float
    matched_patterns: List[str]
    processing_time: float
    filter_name: str
    rule_trace: List[str] = None  # 新增：规则追踪信息

class RuleCondition:
    """规则条件类"""
    
    def __init__(self, condition_type: str, patterns: List[str], operator: str = "any"):
        self.condition_type = condition_type  # 'positive', 'negative', 'neutral'
        self.patterns = [re.compile(p, re.IGNORECASE) for p in patterns]
        self.operator = operator  # 'any', 'all'
    
    def evaluate(self, text: str) -> Tuple[bool, List[str]]:
        """评估条件是否满足"""
        matched_patterns = []
        
        for pattern in self.patterns:
            if pattern.search(text):
                matched_patterns.append(pattern.pattern)
        
        if self.operator == "any":
            result = len(matched_patterns) > 0
        else:  # "all"
            result = len(matched_patterns) == len(self.patterns)
        
        return result, matched_patterns

class ModernRule:
    """现代化规则类"""
    
    def __init__(self, name: str, priority: int, result_type: FilterResult, 
                 confidence: float, conditions: List[RuleCondition], 
                 exclusion_conditions: List[RuleCondition] = None):
        self.name = name
        self.priority = priority
        self.result_type = result_type
        self.confidence = confidence
        self.conditions = conditions
        self.exclusion_conditions = exclusion_conditions or []
    
    def evaluate(self, text: str) -> Tuple[bool, ClassificationResult]:
        """评估规则是否匹配"""
        start_time = time.time()
        all_matched_patterns = []
        rule_trace = []
        
        # 检查排除条件
        for exclusion in self.exclusion_conditions:
            is_excluded, excluded_patterns = exclusion.evaluate(text)
            if is_excluded:
                rule_trace.append(f"被排除条件阻止: {excluded_patterns}")
                return False, None
        
        # 检查主要条件
        conditions_met = 0
        for condition in self.conditions:
            is_met, matched_patterns = condition.evaluate(text)
            if is_met:
                conditions_met += 1
                all_matched_patterns.extend(matched_patterns)
                rule_trace.append(f"条件满足: {condition.condition_type} - {matched_patterns}")
        
        # 判断是否所有条件都满足
        if conditions_met == len(self.conditions):
            processing_time = time.time() - start_time
            
            result = ClassificationResult(
                result=self.result_type,
                confidence=self.confidence,
                matched_patterns=all_matched_patterns,
                processing_time=processing_time,
                filter_name=self.name,
                rule_trace=rule_trace
            )
            
            return True, result
        
        return False, None

class ModernRuleEngine:
    """现代化规则引擎"""
    
    def __init__(self, config: Dict = None):
        """
        初始化现代化规则引擎
        
        Args:
            config: 规则引擎配置
        """
        self.config = config or {}
        self.rules: List[ModernRule] = []
        
        # 初始化默认规则
        self._init_default_rules()
        
        # 按优先级排序
        self.rules.sort(key=lambda r: r.priority)
        
        logger.info(f"✅ 现代化规则引擎初始化完成，加载 {len(self.rules)} 条规则")
    
    def _init_default_rules(self):
        """初始化默认规则"""
        
        # 高置信度负面规则
        negative_rule = ModernRule(
            name="HighConfidenceNegativeRule",
            priority=1,
            result_type=FilterResult.NEGATIVE,
            confidence=0.95,
            conditions=[
                RuleCondition("negative", [
                    r"警示函", r"业绩预亏", r"亏损", r"违规", r"处罚",
                    r"风险提示", r"停牌", r"退市", r"诉讼", r"仲裁",
                    r"立案调查", r"监管函", r"关注函", r"问询函",
                    r"行政处罚", r"监管处罚", r"业绩大幅下滑", r"预计亏损"
                ], "any")
            ]
        )
        
        # 高置信度正面规则
        positive_rule = ModernRule(
            name="HighConfidencePositiveRule",
            priority=2,
            result_type=FilterResult.POSITIVE,
            confidence=0.90,
            conditions=[
                RuleCondition("positive", [
                    r"重大合同", r"技术突破", r"业绩增长", r"盈利", r"分红",
                    r"收购", r"合作协议", r"新产品发布", r"业绩预增",
                    r"净利润.*增长", r"营业收入.*增长", r"签署.*合同", r"中标"
                ], "any")
            ],
            exclusion_conditions=[
                RuleCondition("negative_exclusion", [
                    r"亏损", r"违规", r"风险", r"警示"
                ], "any")
            ]
        )
        
        # 高置信度中性规则
        neutral_rule = ModernRule(
            name="HighConfidenceNeutralRule",
            priority=3,
            result_type=FilterResult.NEUTRAL,
            confidence=0.99,
            conditions=[
                RuleCondition("neutral", [
                    r"^.*股东大会.*$", r"^.*工商变更.*$", r"^.*董事会决议.*$",
                    r"^.*监事会决议.*$", r"^.*年度报告.*$", r"^.*季度报告.*$",
                    r"召开.*股东大会", r"董事会.*决议", r"监事会.*决议",
                    r"定期报告", r"临时股东大会"
                ], "any")
            ],
            exclusion_conditions=[
                RuleCondition("negative_exclusion", [
                    r"亏损", r"违规", r"风险", r"警示"
                ], "any")
            ]
        )
        
        self.rules = [negative_rule, positive_rule, neutral_rule]
    
    def classify(self, title: str, content: str) -> ClassificationResult:
        """
        使用现代化规则引擎进行分类
        
        Args:
            title: 文档标题
            content: 文档内容
            
        Returns:
            分类结果
        """
        start_time = time.time()
        full_text = f"{title} {content}"
        
        # 依次应用规则
        for rule in self.rules:
            try:
                is_matched, result = rule.evaluate(full_text)
                
                if is_matched and result:
                    logger.debug(f"规则匹配: {rule.name} -> {result.result.label}")
                    return result
                    
            except Exception as e:
                logger.error(f"规则 {rule.name} 执行失败: {e}")
                continue
        
        # 没有规则匹配，返回不确定
        processing_time = time.time() - start_time
        return ClassificationResult(
            result=FilterResult.UNCERTAIN,
            confidence=0.0,
            matched_patterns=[],
            processing_time=processing_time,
            filter_name="ModernRuleEngine",
            rule_trace=["没有规则匹配"]
        )
    
    def add_rule(self, rule: ModernRule):
        """添加新规则"""
        self.rules.append(rule)
        self.rules.sort(key=lambda r: r.priority)
        logger.info(f"✅ 添加规则: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """移除规则"""
        self.rules = [r for r in self.rules if r.name != rule_name]
        logger.info(f"✅ 移除规则: {rule_name}")
    
    def get_engine_status(self) -> Dict:
        """获取引擎状态"""
        return {
            'engine_type': 'modern_rule_engine',
            'rules_count': len(self.rules),
            'rules': [
                {
                    'name': rule.name,
                    'priority': rule.priority,
                    'result_type': rule.result_type.name,
                    'confidence': rule.confidence,
                    'conditions_count': len(rule.conditions),
                    'exclusions_count': len(rule.exclusion_conditions)
                }
                for rule in self.rules
            ],
            'config': self.config
        }
    
    def update_rule_from_config(self, filter_config):
        """从配置更新规则"""
        try:
            # 根据过滤器名称确定结果类型
            if "Negative" in filter_config.name:
                result_type = FilterResult.NEGATIVE
            elif "Positive" in filter_config.name:
                result_type = FilterResult.POSITIVE
            elif "Neutral" in filter_config.name:
                result_type = FilterResult.NEUTRAL
            else:
                result_type = FilterResult.UNCERTAIN
            
            # 创建条件
            conditions = [
                RuleCondition("main", filter_config.patterns, "any")
            ]
            
            # 创建排除条件
            exclusion_conditions = []
            if filter_config.negative_check_patterns:
                exclusion_conditions.append(
                    RuleCondition("exclusion", filter_config.negative_check_patterns, "any")
                )
            
            # 创建规则
            rule = ModernRule(
                name=filter_config.name,
                priority=filter_config.priority,
                result_type=result_type,
                confidence=filter_config.confidence_base,
                conditions=conditions,
                exclusion_conditions=exclusion_conditions
            )
            
            # 移除同名的旧规则
            self.remove_rule(filter_config.name)
            
            # 添加新规则
            self.add_rule(rule)
            
            logger.info(f"✅ 从配置更新规则: {filter_config.name}")
            
        except Exception as e:
            logger.error(f"❌ 从配置更新规则失败: {e}")

def test_modern_rule_engine():
    """测试现代化规则引擎"""
    print("🧪 测试现代化规则引擎")
    print("="*50)
    
    # 创建规则引擎
    engine = ModernRuleEngine()
    
    # 显示引擎状态
    status = engine.get_engine_status()
    print(f"📊 引擎状态:")
    print(f"  引擎类型: {status['engine_type']}")
    print(f"  规则数量: {status['rules_count']}")
    
    for rule_info in status['rules']:
        print(f"  - {rule_info['name']}: 优先级{rule_info['priority']}, "
              f"结果{rule_info['result_type']}, 置信度{rule_info['confidence']}")
    
    # 测试案例
    test_cases = [
        ("业绩预警公告", "公司预计2023年度净利润亏损5000万元"),
        ("重大合同签署", "公司与客户签署15亿元销售合同"),
        ("股东大会通知", "公司定于2024年3月15日召开年度股东大会"),
        ("一般性公告", "公司发布一般性业务公告")
    ]
    
    print(f"\n🔍 分类测试:")
    for title, content in test_cases:
        result = engine.classify(title, content)
        print(f"📄 {title}:")
        print(f"  结果: {result.result.label}")
        print(f"  置信度: {result.confidence:.3f}")
        print(f"  匹配模式: {result.matched_patterns}")
        print(f"  过滤器: {result.filter_name}")
        if result.rule_trace:
            print(f"  规则追踪: {result.rule_trace}")
        print(f"  处理时间: {result.processing_time:.4f}秒")
        print()

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    test_modern_rule_engine()
