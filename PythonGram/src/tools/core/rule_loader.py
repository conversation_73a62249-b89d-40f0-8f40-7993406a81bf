#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
共享规则库加载器
"""

import json
import re
from pathlib import Path

class RuleLoader:
    def __init__(self, rule_file="shared_rule_library.json"):
        self.rule_file = Path(rule_file)
        self.rules = {}
        self.load_rules()
    
    def load_rules(self):
        """加载规则库"""
        try:
            with open(self.rule_file, 'r', encoding='utf-8') as f:
                self.rules = json.load(f)
            print(f"✅ 规则库加载成功: {self.rules['metadata']['version']}")
        except Exception as e:
            print(f"❌ 加载规则库失败: {e}")
    
    def classify(self, title, content=""):
        """分类公告"""
        text = f"{title} {content}".strip()
        result = {'classification': 'uncertain', 'confidence': 0.0, 'matched_rules': []}
        
        # 检查负面规则
        for rule_name, rule_data in self.rules['negative_rules'].items():
            for pattern in rule_data['patterns']:
                if re.search(pattern, text):
                    result['classification'] = 'negative'
                    result['matched_rules'].append(f"negative_{rule_name}")
                    result['confidence'] = 0.9
                    break
        
        # 检查正面规则
        for rule_name, rule_data in self.rules['positive_rules'].items():
            for pattern in rule_data['patterns']:
                if re.search(pattern, text):
                    result['classification'] = 'positive'
                    result['matched_rules'].append(f"positive_{rule_name}")
                    result['confidence'] = 0.9
                    break
        
        # 检查中性规则
        for rule_name, rule_data in self.rules['neutral_rules'].items():
            for pattern in rule_data['patterns']:
                if re.search(pattern, text):
                    result['classification'] = 'neutral'
                    result['matched_rules'].append(f"neutral_{rule_name}")
                    result['confidence'] = 0.9
                    break
        
        return result

def main():
    """测试规则加载器"""
    loader = RuleLoader()
    
    test_cases = [
        "关于收到证监会警示函的公告",
        "2023年业绩预亏公告", 
        "2023年业绩预增公告",
        "关于召开股东大会的通知"
    ]
    
    for title in test_cases:
        result = loader.classify(title)
        print(f"{title} -> {result['classification']} (置信度: {result['confidence']})")

if __name__ == "__main__":
    main()




