#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化配置管理系统
使用Pydantic进行类型安全的配置管理
"""

import json
import logging
from pathlib import Path
from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field, field_validator
from datetime import datetime

logger = logging.getLogger(__name__)

class PathMappingConfig(BaseModel):
    """路径映射配置"""
    name: str
    description: str = ""
    network_prefix: str
    local_prefix: str
    enabled: bool = True

class FilterConfig(BaseModel):
    """过滤器配置"""
    name: str
    priority: int = Field(default=999, ge=1, le=9999, description="优先级，数字越小优先级越高")
    confidence_base: float = Field(default=0.95, ge=0.0, le=1.0, description="基础置信度")
    confidence_increment: float = Field(default=0.02, ge=0.0, le=0.1, description="置信度增量")
    confidence_max: float = Field(default=0.99, ge=0.0, le=1.0, description="最大置信度")
    enabled: bool = True
    patterns: List[str] = Field(default_factory=list, description="匹配模式列表")
    negative_check_patterns: List[str] = Field(default_factory=list, description="负面检查模式")
    
    @field_validator('confidence_max')
    @classmethod
    def confidence_max_must_be_greater_than_base(cls, v, info):
        if info.data and 'confidence_base' in info.data and v < info.data['confidence_base']:
            raise ValueError('confidence_max must be greater than confidence_base')
        return v

class VersioningConfig(BaseModel):
    """版本管理配置"""
    student_version: str = "1.0"
    rule_version: str = "1.0"
    code_version: str = "1.0"
    version_format: str = "v{major}.{minor}"
    auto_increment: bool = False

class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = Field(default="INFO", pattern="^(DEBUG|INFO|WARNING|ERROR|CRITICAL)$")
    format: str = "%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s"
    file_enabled: bool = False
    file_path: str = "python_student_c.log"
    file_max_size: int = Field(default=10*1024*1024, description="日志文件最大大小（字节）")
    file_backup_count: int = Field(default=5, ge=1, le=10)

class PerformanceConfig(BaseModel):
    """性能配置"""
    enable_timing: bool = True
    enable_caching: bool = True
    max_content_length: int = Field(default=100000, ge=1000)
    timeout_seconds: int = Field(default=30, ge=1, le=300)

class PDFExtractionConfig(BaseModel):
    """PDF提取配置"""
    preferred_library: str = Field(default="pdfminer", pattern="^(pdfminer|PyPDF2|pypdf)$")
    fallback_libraries: List[str] = Field(default=["PyPDF2", "pypdf"])
    max_file_size_mb: int = Field(default=50, ge=1, le=500)
    encoding: str = "utf-8"

class OutputDirectoriesConfig(BaseModel):
    """输出目录配置"""
    base_output_dir: str = "E:\\LLMData\\"
    test_results: str = "E:\\LLMData\\test_results\\"
    analysis_output: str = "E:\\LLMData\\analysis_output\\"
    logs: str = "E:\\LLMData\\logs\\"
    processed_data: str = "E:\\LLMData\\processed_data\\"
    reports: str = "E:\\LLMData\\reports\\"

class KnowledgeBaseConfig(BaseModel):
    """知识库配置"""
    version: str = "1.0"
    created_at: datetime = Field(default_factory=datetime.now)
    last_updated: datetime = Field(default_factory=datetime.now)
    source_data_count: int = 0
    extraction_method: str = "llm_analysis"
    confidence_threshold: float = Field(default=0.8, ge=0.0, le=1.0)

    # 知识库策略配置
    include_neutral_patterns: bool = True  # 是否包含中性模式
    neutral_strategy: str = Field(default="strong_signals_only",
                                pattern="^(strong_signals_only|comprehensive|disabled)$")
    # strong_signals_only: 只包含强中性信号（如"股东大会"）
    # comprehensive: 包含所有中性模式
    # disabled: 完全禁用中性知识库

class RuleEngineConfig(BaseModel):
    """规则引擎配置"""
    engine_type: str = Field(default="business_rules", pattern="^(business_rules|native)$")
    enable_semantic_analysis: bool = False
    enable_entity_recognition: bool = False
    rule_cache_enabled: bool = True
    rule_cache_size: int = Field(default=1000, ge=100, le=10000)
    knowledge_base: KnowledgeBaseConfig = Field(default_factory=KnowledgeBaseConfig)

class MetadataConfig(BaseModel):
    """元数据配置"""
    version: str = "1.0.0"
    description: str = "Python学生C配置文件"
    created_by: str = "ModernConfigManager"
    created_at: datetime = Field(default_factory=datetime.now)
    last_updated: datetime = Field(default_factory=datetime.now)

class ModernSystemConfig(BaseModel):
    """现代化系统配置"""
    metadata: MetadataConfig = Field(default_factory=MetadataConfig)
    versioning: VersioningConfig = Field(default_factory=VersioningConfig)
    path_mappings: List[PathMappingConfig] = Field(default_factory=list)
    filters: List[FilterConfig] = Field(default_factory=list)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    pdf_extraction: PDFExtractionConfig = Field(default_factory=PDFExtractionConfig)
    output_directories: OutputDirectoriesConfig = Field(default_factory=OutputDirectoriesConfig)
    rule_engine: RuleEngineConfig = Field(default_factory=RuleEngineConfig)
    
    class Config:
        extra = "forbid"  # 禁止未定义的字段
        validate_assignment = True  # 赋值时验证
        
    @field_validator('filters')
    @classmethod
    def validate_filter_priorities(cls, v):
        """验证过滤器优先级不重复"""
        priorities = [f.priority for f in v if f.enabled]
        if len(priorities) != len(set(priorities)):
            raise ValueError('Filter priorities must be unique among enabled filters')
        return v

class ModernConfigManager:
    """现代化配置管理器"""
    
    def __init__(self, config_path: str = "modern_config.json"):
        """
        初始化现代化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.config: Optional[ModernSystemConfig] = None
        self._load_config()
    
    def _load_config(self) -> None:
        """加载配置文件"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    raw_config = json.load(f)
                
                # 使用Pydantic进行验证和解析
                self.config = ModernSystemConfig.model_validate(raw_config)
                logger.info(f"✅ 现代化配置文件加载成功: {self.config_path}")
                
            else:
                logger.warning(f"⚠️ 配置文件不存在: {self.config_path}，将创建默认配置")
                self.config = ModernSystemConfig()
                self._save_config()
                
        except Exception as e:
            logger.error(f"❌ 配置文件加载失败: {e}")
            logger.info("使用默认配置")
            self.config = ModernSystemConfig()
    
    def _save_config(self) -> None:
        """保存配置到文件"""
        try:
            # 更新最后修改时间
            self.config.metadata.last_updated = datetime.now()
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(
                    self.config.model_dump(),
                    f,
                    ensure_ascii=False,
                    indent=2,
                    default=str  # 处理datetime序列化
                )
            logger.info(f"✅ 配置已保存: {self.config_path}")
            
        except Exception as e:
            logger.error(f"❌ 保存配置失败: {e}")
            raise
    
    def get_filter_configs(self) -> List[FilterConfig]:
        """获取启用的过滤器配置，按优先级排序"""
        enabled_filters = [f for f in self.config.filters if f.enabled]
        return sorted(enabled_filters, key=lambda x: x.priority)
    
    def get_path_mappings(self) -> List[PathMappingConfig]:
        """获取启用的路径映射配置"""
        return [m for m in self.config.path_mappings if m.enabled]
    
    def get_current_version(self) -> str:
        """获取当前版本号"""
        return f"v{self.config.versioning.student_version}"
    
    def get_rule_version(self) -> str:
        """获取规则库版本号"""
        return self.config.versioning.rule_version
    
    def increment_version(self, version_type: str = "minor") -> str:
        """
        递增版本号
        
        Args:
            version_type: 版本类型 ("major" 或 "minor")
            
        Returns:
            新的版本号
        """
        try:
            current_version = self.config.versioning.student_version
            parts = current_version.split('.')
            major = int(parts[0]) if len(parts) > 0 else 1
            minor = int(parts[1]) if len(parts) > 1 else 0
            
            if version_type == "major":
                major += 1
                minor = 0
            else:  # minor
                minor += 1
            
            new_version = f"{major}.{minor}"
            
            # 更新配置
            self.config.versioning.student_version = new_version
            self.config.versioning.rule_version = new_version
            
            # 保存配置
            self._save_config()
            
            logger.info(f"✅ 版本号已更新: {current_version} → {new_version}")
            return new_version
            
        except Exception as e:
            logger.error(f"❌ 版本号递增失败: {e}")
            return self.get_current_version()
    
    def validate_config(self) -> List[str]:
        """
        验证配置的有效性
        
        Returns:
            验证错误列表，空列表表示配置有效
        """
        errors = []
        
        try:
            # Pydantic已经在加载时进行了基本验证
            # 这里可以添加额外的业务逻辑验证
            
            # 验证路径映射
            for mapping in self.config.path_mappings:
                if mapping.enabled:
                    if not Path(mapping.local_prefix).exists():
                        errors.append(f"路径映射 {mapping.name}: 本地路径不存在 {mapping.local_prefix}")
            
            # 验证输出目录
            base_dir = Path(self.config.output_directories.base_output_dir)
            if not base_dir.exists():
                errors.append(f"基础输出目录不存在: {base_dir}")
            
        except Exception as e:
            errors.append(f"配置验证异常: {e}")
        
        return errors
    
    def get_config_summary(self) -> Dict:
        """获取配置摘要"""
        return {
            "config_file": str(self.config_path),
            "config_exists": self.config_path.exists(),
            "student_version": self.config.versioning.student_version,
            "rule_version": self.config.versioning.rule_version,
            "enabled_filters_count": len(self.get_filter_configs()),
            "enabled_path_mappings_count": len(self.get_path_mappings()),
            "rule_engine_type": self.config.rule_engine.engine_type,
            "semantic_analysis_enabled": self.config.rule_engine.enable_semantic_analysis,
            "last_updated": self.config.metadata.last_updated
        }

def create_default_config_file(output_path: str = "modern_config.json"):
    """创建默认配置文件"""
    config_manager = ModernConfigManager(output_path)
    
    # 添加一些默认的过滤器配置
    default_filters = [
        FilterConfig(
            name="HighConfidenceNegativeFilter",
            priority=1,
            confidence_base=0.95,
            patterns=[
                "警示函", "业绩预亏", "亏损", "违规", "处罚",
                "风险提示", "停牌", "退市", "诉讼", "仲裁"
            ]
        ),
        FilterConfig(
            name="HighConfidencePositiveFilter", 
            priority=2,
            confidence_base=0.90,
            patterns=[
                "重大合同", "技术突破", "业绩增长", "盈利", "分红",
                "收购", "合作协议", "新产品发布"
            ],
            negative_check_patterns=["亏损", "违规", "风险", "警示"]
        ),
        FilterConfig(
            name="HighConfidenceNeutralFilter",
            priority=3,
            confidence_base=0.99,
            patterns=[
                "^.*股东大会.*$", "^.*工商变更.*$", "^.*董事会决议.*$",
                "^.*监事会决议.*$", "^.*年度报告.*$", "^.*季度报告.*$"
            ],
            negative_check_patterns=["亏损", "违规", "风险", "警示"]
        )
    ]
    
    # 添加默认路径映射
    default_path_mappings = [
        PathMappingConfig(
            name="supmicro4_F_mapping",
            description="supmicro4服务器F盘映射",
            network_prefix="\\\\supmicro4\\F\\",
            local_prefix="F:\\"
        )
    ]
    
    config_manager.config.filters = default_filters
    config_manager.config.path_mappings = default_path_mappings
    config_manager._save_config()
    
    print(f"✅ 默认配置文件已创建: {output_path}")
    return config_manager

if __name__ == "__main__":
    # 创建默认配置文件
    create_default_config_file()
    
    # 测试配置管理器
    config_manager = ModernConfigManager()
    
    print("📊 配置摘要:")
    summary = config_manager.get_config_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    # 验证配置
    errors = config_manager.validate_config()
    if errors:
        print(f"\n❌ 配置验证错误:")
        for error in errors:
            print(f"  - {error}")
    else:
        print(f"\n✅ 配置验证通过")
