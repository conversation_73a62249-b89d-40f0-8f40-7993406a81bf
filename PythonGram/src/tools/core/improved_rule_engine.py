#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版规则引擎
基于学生B的实际经验，重新设计中性知识库策略
"""

import re
import time
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class FilterResult(Enum):
    """过滤结果枚举"""
    POSITIVE = ("正面", "明显正面公告")
    NEGATIVE = ("负面", "明显负面公告") 
    NEUTRAL = ("中性", "明确中性公告")
    UNCERTAIN = ("不确定", "需要LLM进一步分析")
    
    def __init__(self, label: str, description: str):
        self.label = label
        self.description = description

@dataclass
class ClassificationResult:
    """分类结果数据类"""
    result: FilterResult
    confidence: float
    matched_patterns: List[str]
    processing_time: float
    filter_name: str
    rule_trace: List[str] = None
    priority_level: str = None  # 新增：优先级层级

class LayeredRuleEngine:
    """分层规则引擎 - 基于学生B的经验"""
    
    def __init__(self):
        """初始化分层规则引擎"""
        
        # 基于学生B的分层规则设计
        self.rule_layers = {
            # 第1层：强制中性（最高优先级，类似学生B的[IGNORE]）
            "FORCE_NEUTRAL": {
                "priority": 1,
                "result": FilterResult.NEUTRAL,
                "confidence": 0.99,
                "patterns": [
                    r".*中签.*公告.*",                    # 中签公告
                    r".*招股.*(意向|说明).*",              # 招股说明
                    r"((?!.*(风险提示)).)*(董事会).*(决议公告).*",  # 董事会决议（排除风险提示）
                    r"((?!.*(风险提示)).)*(股东大会).*",    # 股东大会（排除风险提示）
                    r"((?!.*(风险提示)).)*([年季]度报告).*", # 年度/季度报告（排除风险提示）
                    r".*聘任.*((公告)|(意见)).*",          # 聘任公告
                    r".*意见书.*",                        # 意见书
                ]
            },
            
            # 第2层：高风险负面（类似学生B的[RISK]）
            "HIGH_RISK": {
                "priority": 2,
                "result": FilterResult.NEGATIVE,
                "confidence": 0.95,
                "patterns": [
                    r"((^退市).*)|((.((?!解除|撤销).)*)(退市)(.*(风险警示|风险提示).*))",  # 退市
                    r".*破产.*",                          # 破产
                    r".*暂停上市.*",                      # 暂停上市
                    r".*(终止|暂停).*(上市|重组).*",       # 终止上市或重组
                    r"(.*(预计|业绩|盈利|利润).*(下降|预降|下滑).*)|.*(亏损|预亏|转亏).*",  # 预亏
                    r"(((?!(.*股票交易|.*股价异动|.*资产重组|.*上市首日)).)*)((风险提示).*)",  # 风险提示
                    r"(.((?!不).)*)(减持)(.((?!完成|完毕).)*))",  # 减持
                    r".((?!不存在|未有|未因).)*(违法|违规).*",  # 违法违规
                    r".((?!不存在|未有).)*(立案|证监会|证券监督管理委员).*(调查).*",  # 立案调查
                    r".*(触及平仓线|平仓风险|遭遇平仓|强制平仓).*",  # 平仓风险
                    r".*(收到|出具).*(警示函).*",          # 警示函
                    r".*(刑事拘留|刑事强制).*",            # 刑事拘留
                    r"(((?!.*撤销))((^(S|\\*S)).*))|(((?!.*撤销).)*(\\*S|S).*)|^退市.*",  # ST类型
                    r".*(收到).*(行政处罚).*"             # 行政处罚
                ]
            },
            
            # 第3层：警告级负面（类似学生B的[WARNING]）
            "WARNING": {
                "priority": 3,
                "result": FilterResult.NEGATIVE,
                "confidence": 0.85,
                "patterns": [
                    r".((?!不存在).)*(非经营性资金占用).*",  # 非经营性资金占用
                    r"(.*(涉及).*(诉讼).*)|(.*(诉讼).*(进展).*)",  # 涉及诉讼
                    r".*(收入).*(下降|减少).*",            # 收入下降
                    r".*(问询函|关注函).*"                # 问询函
                ]
            },
            
            # 第4层：可疑事件（类似学生B的[SUSPICIOUS]）
            "SUSPICIOUS": {
                "priority": 4,
                "result": FilterResult.UNCERTAIN,
                "confidence": 0.70,
                "patterns": [
                    r".*业绩.*预告",                      # 业绩预告
                    r".*关注函.*",                        # 关注函
                    r".*关联交易.*",                      # 关联交易
                    r".*(融资|融券).*",                   # 融资融券
                    r".*调解协议.*",                      # 调解协议
                    r".*经营数据.*",                      # 经营数据公告
                    r".*停牌.*"                          # 停牌
                ]
            },
            
            # 第5层：正面信号
            "POSITIVE": {
                "priority": 5,
                "result": FilterResult.POSITIVE,
                "confidence": 0.90,
                "patterns": [
                    r".*重大合同.*",                      # 重大合同
                    r".*技术突破.*",                      # 技术突破
                    r".*业绩增长.*",                      # 业绩增长
                    r".*盈利.*",                         # 盈利
                    r".*分红.*",                         # 分红
                    r".*收购.*",                         # 收购
                    r".*合作协议.*",                      # 合作协议
                    r".*新产品发布.*",                    # 新产品发布
                    r".*业绩预增.*",                      # 业绩预增
                    r".*净利润.*增长.*",                  # 净利润增长
                    r".*营业收入.*增长.*",                # 营业收入增长
                    r".*签署.*合同.*",                    # 签署合同
                    r".*中标.*"                          # 中标
                ]
            }
        }
        
        # 编译正则表达式
        self._compile_patterns()
        
        logger.info(f"✅ 分层规则引擎初始化完成，加载 {sum(len(layer['patterns']) for layer in self.rule_layers.values())} 条规则")
    
    def _compile_patterns(self):
        """编译正则表达式模式"""
        for layer_name, layer_config in self.rule_layers.items():
            compiled_patterns = []
            for pattern in layer_config['patterns']:
                try:
                    compiled_patterns.append(re.compile(pattern, re.IGNORECASE))
                except re.error as e:
                    logger.error(f"正则表达式编译失败 [{layer_name}]: {pattern} - {e}")
            
            layer_config['compiled_patterns'] = compiled_patterns
    
    def classify(self, title: str, content: str = "") -> ClassificationResult:
        """
        使用分层规则引擎进行分类
        
        Args:
            title: 文档标题
            content: 文档内容
            
        Returns:
            分类结果
        """
        start_time = time.time()
        full_text = f"{title} {content}"
        
        # 按优先级顺序检查各层规则
        sorted_layers = sorted(self.rule_layers.items(), key=lambda x: x[1]['priority'])
        
        for layer_name, layer_config in sorted_layers:
            matched_patterns = []
            
            # 检查该层的所有模式
            for pattern in layer_config['compiled_patterns']:
                if pattern.search(full_text):
                    matched_patterns.append(pattern.pattern)
            
            # 如果有匹配，返回该层的结果
            if matched_patterns:
                processing_time = time.time() - start_time
                
                return ClassificationResult(
                    result=layer_config['result'],
                    confidence=layer_config['confidence'],
                    matched_patterns=matched_patterns,
                    processing_time=processing_time,
                    filter_name=f"LayeredRuleEngine_{layer_name}",
                    rule_trace=[f"匹配 {layer_name} 层规则: {matched_patterns[:3]}"],
                    priority_level=layer_name
                )
        
        # 没有任何规则匹配，返回不确定
        processing_time = time.time() - start_time
        return ClassificationResult(
            result=FilterResult.UNCERTAIN,
            confidence=0.0,
            matched_patterns=[],
            processing_time=processing_time,
            filter_name="LayeredRuleEngine_DEFAULT",
            rule_trace=["没有规则匹配"],
            priority_level="DEFAULT"
        )
    
    def get_engine_status(self) -> Dict:
        """获取引擎状态"""
        layer_stats = {}
        total_patterns = 0
        
        for layer_name, layer_config in self.rule_layers.items():
            pattern_count = len(layer_config['patterns'])
            layer_stats[layer_name] = {
                'priority': layer_config['priority'],
                'result_type': layer_config['result'].name,
                'confidence': layer_config['confidence'],
                'pattern_count': pattern_count
            }
            total_patterns += pattern_count
        
        return {
            'engine_type': 'layered_rule_engine',
            'total_patterns': total_patterns,
            'layer_count': len(self.rule_layers),
            'layer_stats': layer_stats
        }

def test_layered_rule_engine():
    """测试分层规则引擎"""
    print("🧪 测试分层规则引擎")
    print("="*50)
    
    # 创建规则引擎
    engine = LayeredRuleEngine()
    
    # 显示引擎状态
    status = engine.get_engine_status()
    print(f"📊 引擎状态:")
    print(f"  引擎类型: {status['engine_type']}")
    print(f"  总规则数: {status['total_patterns']}")
    print(f"  分层数: {status['layer_count']}")
    
    for layer_name, stats in status['layer_stats'].items():
        print(f"  {layer_name}: 优先级{stats['priority']}, {stats['pattern_count']}条规则, "
              f"结果{stats['result_type']}, 置信度{stats['confidence']}")
    
    # 测试案例
    test_cases = [
        # 强制中性测试
        ("股东大会通知", "公司定于2024年3月15日召开年度股东大会"),
        ("董事会决议公告", "董事会审议通过了年度报告"),
        ("中签率公告", "网上发行中签率公告"),
        
        # 高风险负面测试
        ("业绩预警公告", "公司预计2023年度净利润亏损5000万元"),
        ("退市风险警示", "公司股票可能被实施退市风险警示"),
        ("立案调查公告", "公司收到证监会立案调查通知书"),
        
        # 警告级负面测试
        ("诉讼进展公告", "公司涉及重大诉讼案件进展"),
        ("问询函回复", "关于深交所问询函的回复公告"),
        
        # 可疑事件测试
        ("业绩预告", "2023年度业绩预告"),
        ("停牌公告", "公司股票停牌公告"),
        
        # 正面信号测试
        ("重大合同公告", "公司签署15亿元重大销售合同"),
        ("业绩增长公告", "公司净利润同比增长50%"),
        
        # 无匹配测试
        ("一般性公告", "公司发布一般性业务公告")
    ]
    
    print(f"\n🔍 分层分类测试:")
    for title, content in test_cases:
        result = engine.classify(title, content)
        print(f"📄 {title}:")
        print(f"  结果: {result.result.label}")
        print(f"  置信度: {result.confidence:.2f}")
        print(f"  优先级层: {result.priority_level}")
        print(f"  匹配模式: {result.matched_patterns[:2]}...")
        print(f"  处理时间: {result.processing_time:.4f}秒")
        print()

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    test_layered_rule_engine()
