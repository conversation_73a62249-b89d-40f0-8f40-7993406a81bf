#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于原子信号的分类词谱生成器
使用拆解方法论重新整理高效分类词谱
"""

import pandas as pd
import re
from collections import defaultdict, Counter
from typing import Dict, List, Tuple
import json

class AtomicSignalClassifier:
    """原子信号分类器"""
    
    def __init__(self):
        """初始化原子信号分类器"""
        self.atomic_signals = {
            'E_TERRIBLE': [],
            'D_BAD': [],
            'C_NEUTRAL': [],
            'B_GOOD': [],
            'A_EXCELLENT': []
        }
        
    def extract_atomic_signals_for_category(self, df: pd.DataFrame, category: str, 
                                          min_purity: float = 0.8, 
                                          min_docs: int = 3) -> List[Dict]:
        """为指定类别提取原子信号"""
        print(f"🔍 提取 {category} 类别的原子信号")
        print("-" * 50)
        
        # 获取该类别的所有词汇
        category_words = df[df['primary_category'] == category].copy()
        
        if category_words.empty:
            print(f"❌ {category} 类别没有词汇")
            return []
        
        # 对每个词汇进行拆解
        all_components = []
        word_components_map = {}
        
        for _, row in category_words.iterrows():
            word = row['word']
            components = self._decompose_word(word)
            word_components_map[word] = components
            all_components.extend(components)
        
        # 统计组件频率
        component_counter = Counter(all_components)
        
        # 分析每个组件的效果
        atomic_signals = []
        
        for component, freq_in_category in component_counter.most_common():
            if len(component) < 2 or len(component) > 6:  # 限制长度
                continue
                
            # 在完整数据中查找该组件
            component_data = df[df['word'] == component]
            
            if not component_data.empty:
                row = component_data.iloc[0]
                
                # 检查是否符合原子信号条件
                if (row['purity'] >= min_purity and 
                    row['total_docs'] >= min_docs and
                    row['primary_category'] == category):
                    
                    atomic_signals.append({
                        'component': component,
                        'purity': row['purity'],
                        'total_docs': row['total_docs'],
                        'category_docs': self._get_category_docs(row, category),
                        'freq_in_category_words': freq_in_category,
                        'confidence': min(0.95, row['purity'] + 0.05),
                        'priority': self._calculate_priority(row['purity'], row['total_docs'])
                    })
        
        # 按纯度和文档数排序
        atomic_signals.sort(key=lambda x: (x['purity'], x['total_docs']), reverse=True)
        
        print(f"✅ 找到 {len(atomic_signals)} 个 {category} 原子信号")
        for i, signal in enumerate(atomic_signals[:10], 1):  # 显示前10个
            print(f"  {i:2d}. {signal['component']:<10} 纯度:{signal['purity']:.3f} "
                  f"文档:{signal['total_docs']:2d} 置信度:{signal['confidence']:.3f}")
        
        return atomic_signals
    
    def _decompose_word(self, word: str) -> List[str]:
        """拆解词汇成原子组件"""
        components = []
        
        # 滑动窗口提取2-6字符的组件
        for length in [6, 5, 4, 3, 2]:
            for i in range(len(word) - length + 1):
                component = word[i:i+length]
                if (re.match(r'^[\u4e00-\u9fff]+$', component) and  # 纯中文
                    component not in components):  # 去重
                    components.append(component)
        
        return components
    
    def _get_category_docs(self, row: pd.Series, category: str) -> int:
        """获取指定类别的文档数"""
        category_map = {
            'A_EXCELLENT': 'A_docs',
            'B_GOOD': 'B_docs', 
            'C_NEUTRAL': 'C_docs',
            'D_BAD': 'D_docs',
            'E_TERRIBLE': 'E_docs'
        }
        return row[category_map[category]]
    
    def _calculate_priority(self, purity: float, total_docs: int) -> str:
        """计算优先级"""
        if purity >= 0.95 and total_docs >= 10:
            return 'very_high'
        elif purity >= 0.9 and total_docs >= 5:
            return 'high'
        elif purity >= 0.8 and total_docs >= 3:
            return 'medium'
        else:
            return 'low'
    
    def build_atomic_signal_lexicon(self, df: pd.DataFrame) -> Dict:
        """构建原子信号词谱"""
        print("🏗️ 构建基于原子信号的分类词谱")
        print("=" * 60)
        
        lexicon = {}
        
        # 为每个类别提取原子信号
        categories = [
            ('E_TERRIBLE', 0.8, 3),  # E类：纯度0.8+，至少3个文档
            ('D_BAD', 0.8, 3),       # D类：纯度0.8+，至少3个文档
            ('A_EXCELLENT', 0.7, 2), # A类：纯度0.7+，至少2个文档（数据较少）
            ('B_GOOD', 0.7, 3),      # B类：纯度0.7+，至少3个文档
            ('C_NEUTRAL', 0.6, 5)    # C类：纯度0.6+，至少5个文档（要求更高）
        ]
        
        for category, min_purity, min_docs in categories:
            signals = self.extract_atomic_signals_for_category(df, category, min_purity, min_docs)
            lexicon[category] = signals
            self.atomic_signals[category] = signals
        
        return lexicon
    
    def generate_classification_rules(self, lexicon: Dict) -> List[Dict]:
        """生成分类规则"""
        print(f"\n🔧 生成分类规则")
        print("-" * 40)
        
        rules = []
        rule_id = 1
        
        # 按优先级顺序生成规则：E > D > A > B > C
        priority_order = ['E_TERRIBLE', 'D_BAD', 'A_EXCELLENT', 'B_GOOD', 'C_NEUTRAL']
        
        for category in priority_order:
            signals = lexicon.get(category, [])
            
            for signal in signals:
                rule = {
                    'id': f"atomic_rule_{rule_id:03d}",
                    'pattern': f".*{re.escape(signal['component'])}.*",
                    'component': signal['component'],
                    'category': category,
                    'confidence': signal['confidence'],
                    'purity': signal['purity'],
                    'priority': signal['priority'],
                    'total_docs': signal['total_docs'],
                    'description': f"{category}原子信号: {signal['component']}",
                    'rule_type': 'atomic_signal'
                }
                rules.append(rule)
                rule_id += 1
        
        print(f"✅ 生成了 {len(rules)} 条原子信号规则")
        
        # 按类别统计
        for category in priority_order:
            count = len([r for r in rules if r['category'] == category])
            print(f"  {category}: {count} 条规则")
        
        return rules
    
    def save_atomic_lexicon(self, lexicon: Dict, rules: List[Dict], 
                           output_file: str = "atomic_signal_lexicon.json"):
        """保存原子信号词谱"""
        output_data = {
            'version': '1.0.0',
            'created_at': pd.Timestamp.now().isoformat(),
            'description': '基于原子信号拆解方法论的分类词谱',
            'methodology': '通过词汇拆解找到高纯度原子信号，过滤噪音词汇',
            'statistics': {
                'total_rules': len(rules),
                'categories': len(lexicon),
                'category_distribution': {
                    category: len(signals) for category, signals in lexicon.items()
                }
            },
            'lexicon': lexicon,
            'rules': rules
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"💾 原子信号词谱已保存: {output_file}")
        return output_file
    
    def test_atomic_classifier(self, rules: List[Dict], test_cases: List[str]) -> None:
        """测试原子信号分类器"""
        print(f"\n🧪 测试原子信号分类器")
        print("-" * 40)
        
        for i, text in enumerate(test_cases, 1):
            result = self.classify_text(text, rules)
            print(f"{i}. 文本: {text[:30]}...")
            print(f"   分类: {result['category']}")
            print(f"   置信度: {result['confidence']:.3f}")
            print(f"   匹配信号: {result['matched_signals']}")
            print()
    
    def classify_text(self, text: str, rules: List[Dict]) -> Dict:
        """使用原子信号分类文本"""
        matched_rules = []
        
        for rule in rules:
            if re.search(rule['pattern'], text, re.IGNORECASE):
                matched_rules.append(rule)
        
        if not matched_rules:
            return {
                'category': 'UNCERTAIN',
                'confidence': 0.0,
                'matched_signals': [],
                'rule_trace': ['未匹配任何原子信号']
            }
        
        # 选择最高优先级和置信度的规则
        best_rule = max(matched_rules, key=lambda x: (
            {'very_high': 4, 'high': 3, 'medium': 2, 'low': 1}[x['priority']],
            x['confidence']
        ))
        
        return {
            'category': best_rule['category'],
            'confidence': best_rule['confidence'],
            'matched_signals': [rule['component'] for rule in matched_rules],
            'rule_trace': [f"匹配原子信号: {best_rule['component']} (置信度: {best_rule['confidence']:.3f})"]
        }

def create_atomic_signal_lexicon():
    """创建原子信号词谱"""
    print("🚀 创建基于原子信号的分类词谱")
    print("=" * 80)
    
    try:
        # 读取词频分析数据
        df = pd.read_csv('word_frequency_analysis_strict.csv', encoding='utf-8-sig')
        print(f"📊 加载了 {len(df)} 个词汇")
        
        # 创建原子信号分类器
        classifier = AtomicSignalClassifier()
        
        # 构建词谱
        lexicon = classifier.build_atomic_signal_lexicon(df)
        
        # 生成规则
        rules = classifier.generate_classification_rules(lexicon)
        
        # 保存词谱
        output_file = classifier.save_atomic_lexicon(lexicon, rules)
        
        # 测试分类器
        test_cases = [
            "公司股票进入退市整理期",
            "公司预计净利润亏损5000万元", 
            "公司净利润同比增长25%",
            "公司召开年度股东大会",
            "公司收到监管函警示"
        ]
        
        classifier.test_atomic_classifier(rules, test_cases)
        
        print(f"\n🎉 原子信号词谱创建完成！")
        print(f"📁 输出文件: {output_file}")
        print(f"📊 总规则数: {len(rules)}")
        
        # 显示各类别的顶级原子信号
        print(f"\n🏆 各类别顶级原子信号:")
        for category, signals in lexicon.items():
            if signals:
                top_signal = signals[0]
                print(f"  {category}: {top_signal['component']} "
                      f"(纯度: {top_signal['purity']:.3f}, 文档: {top_signal['total_docs']})")
        
        return classifier, lexicon, rules
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return None, None, None

if __name__ == "__main__":
    classifier, lexicon, rules = create_atomic_signal_lexicon()
    
    if classifier:
        print(f"\n💡 基于您的拆解方法论，我们成功创建了高效的原子信号分类词谱！")
        print(f"🎯 这个词谱只包含真正有区分能力的原子信号，过滤了所有噪音词汇！")
