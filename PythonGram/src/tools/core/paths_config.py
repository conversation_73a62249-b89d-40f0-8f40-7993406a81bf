#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径配置管理
统一管理系统中的所有路径配置
"""

from pathlib import Path
import os

class PathsConfig:
    """路径配置管理器"""
    
    def __init__(self):
        """初始化路径配置"""
        self.version = "3.3.0"  # 🗂️ 路径配置版本
        
        # 基础路径配置
        self.PATHS = {
            # 开发环境路径
            "DEVELOPMENT_ROOT": "D:/LLMData/PythonGram/src/tools/core",
            
            # 研究档案路径 (已移动到E盘)
            "RESEARCH_ARCHIVE": "E:/LLMData/research_archive",
            
            # 学习工作区路径
            "LEARNING_WORKSPACE": "E:/LLMData/atomic_learning_workspace",
            
            # 生产词库路径
            "PRODUCTION_LEXICON": "E:/LLMData/research_archive/01_raw_data",
            
            # LLM数据源路径
            "LLM_DATA_SOURCE": "E:/LLMData/analysis_output",
            
            # 原始数据路径
            "RAW_DATA": "E:/LLMData/PythonGram/src/tools/output",
            
            # 临时工作目录
            "TEMP_WORKSPACE": "temp_workspace"
        }
        
        # 验证关键路径
        self._validate_paths()
    
    def _validate_paths(self):
        """验证关键路径是否存在"""
        critical_paths = [
            "RESEARCH_ARCHIVE",
            "LLM_DATA_SOURCE"
        ]
        
        for path_key in critical_paths:
            path = Path(self.PATHS[path_key])
            if not path.exists():
                print(f"⚠️ 关键路径不存在: {path_key} = {path}")
                # 尝试创建目录
                try:
                    path.mkdir(parents=True, exist_ok=True)
                    print(f"✅ 已创建目录: {path}")
                except Exception as e:
                    print(f"❌ 创建目录失败: {path} - {e}")
    
    def get_path(self, path_key: str) -> Path:
        """
        获取路径对象
        
        Args:
            path_key: 路径键名
            
        Returns:
            Path对象
        """
        if path_key not in self.PATHS:
            raise KeyError(f"未知的路径键: {path_key}")
        
        return Path(self.PATHS[path_key])
    
    def get_path_str(self, path_key: str) -> str:
        """
        获取路径字符串
        
        Args:
            path_key: 路径键名
            
        Returns:
            路径字符串
        """
        return str(self.get_path(path_key))
    
    def ensure_path_exists(self, path_key: str) -> Path:
        """
        确保路径存在，不存在则创建
        
        Args:
            path_key: 路径键名
            
        Returns:
            Path对象
        """
        path = self.get_path(path_key)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    def get_research_archive_subpath(self, subpath: str) -> Path:
        """
        获取研究档案子路径
        
        Args:
            subpath: 子路径，如 "01_raw_data", "04_feature_engineering"
            
        Returns:
            完整路径对象
        """
        return self.get_path("RESEARCH_ARCHIVE") / subpath
    
    def find_latest_production_lexicon(self) -> Path:
        """
        查找最新的生产词库文件
        优先使用固定名称的当前版本，如果不存在则查找带时间戳的版本

        Returns:
            最新生产词库文件路径
        """
        production_dir = self.get_path("PRODUCTION_LEXICON")

        if not production_dir.exists():
            raise FileNotFoundError(f"生产词库目录不存在: {production_dir}")

        # 优先查找固定名称的当前版本
        current_lexicon = production_dir / "production_lexicon.json"
        if current_lexicon.exists():
            return current_lexicon

        # 如果当前版本不存在，查找带时间戳的历史版本
        lexicon_files = list(production_dir.glob("production_lexicon_*.json"))

        if not lexicon_files:
            raise FileNotFoundError(f"未找到生产词库文件: {production_dir}")

        # 按修改时间排序，返回最新的
        latest_file = max(lexicon_files, key=lambda f: f.stat().st_mtime)
        return latest_file
    
    def get_fallback_paths(self, path_key: str) -> list:
        """
        获取备用路径列表（用于兼容性）
        
        Args:
            path_key: 路径键名
            
        Returns:
            备用路径列表
        """
        fallback_map = {
            "RESEARCH_ARCHIVE": [
                "E:/LLMData/research_archive",
                "research_archive",
                "D:/LLMData/PythonGram/src/tools/core/research_archive"  # 旧路径
            ],
            "LEARNING_WORKSPACE": [
                "E:/LLMData/atomic_learning_workspace", 
                "atomic_learning_workspace"
            ],
            "LLM_DATA_SOURCE": [
                "E:/LLMData/analysis_output",
                "E:/LLMData/PythonGram/src/tools/output"
            ]
        }
        
        return fallback_map.get(path_key, [self.PATHS[path_key]])
    
    def find_existing_path(self, path_key: str) -> Path:
        """
        查找存在的路径（从备用路径中）
        
        Args:
            path_key: 路径键名
            
        Returns:
            存在的路径对象
        """
        fallback_paths = self.get_fallback_paths(path_key)
        
        for path_str in fallback_paths:
            path = Path(path_str)
            if path.exists():
                return path
        
        # 如果都不存在，返回默认路径
        return self.get_path(path_key)
    
    def print_paths_status(self):
        """打印所有路径的状态"""
        print(f"🗂️ 路径配置状态 (v{self.version})")
        print("="*60)
        
        for path_key, path_str in self.PATHS.items():
            path = Path(path_str)
            status = "✅ 存在" if path.exists() else "❌ 不存在"
            print(f"{path_key:<20}: {status} - {path}")
        
        print("\n📁 关键文件检查:")
        
        # 检查生产词库
        try:
            latest_lexicon = self.find_latest_production_lexicon()
            print(f"最新生产词库: ✅ {latest_lexicon.name}")
        except FileNotFoundError as e:
            print(f"最新生产词库: ❌ {e}")
        
        # 检查研究档案索引
        index_file = self.get_research_archive_subpath("RESEARCH_INDEX.md")
        status = "✅ 存在" if index_file.exists() else "❌ 不存在"
        print(f"研究档案索引: {status} - {index_file}")

# 全局路径配置实例
paths_config = PathsConfig()

# 便捷函数
def get_path(path_key: str) -> Path:
    """获取路径对象的便捷函数"""
    return paths_config.get_path(path_key)

def get_path_str(path_key: str) -> str:
    """获取路径字符串的便捷函数"""
    return paths_config.get_path_str(path_key)

def ensure_path_exists(path_key: str) -> Path:
    """确保路径存在的便捷函数"""
    return paths_config.ensure_path_exists(path_key)

def find_existing_path(path_key: str) -> Path:
    """查找存在路径的便捷函数"""
    return paths_config.find_existing_path(path_key)

def main():
    """主函数 - 用于测试路径配置"""
    print("🗂️ 路径配置测试")
    print("="*40)
    
    # 打印路径状态
    paths_config.print_paths_status()
    
    # 测试关键功能
    print(f"\n🧪 功能测试:")
    
    try:
        research_archive = find_existing_path("RESEARCH_ARCHIVE")
        print(f"✅ 研究档案路径: {research_archive}")
    except Exception as e:
        print(f"❌ 研究档案路径错误: {e}")
    
    try:
        latest_lexicon = paths_config.find_latest_production_lexicon()
        print(f"✅ 最新生产词库: {latest_lexicon}")
    except Exception as e:
        print(f"❌ 生产词库查找失败: {e}")

if __name__ == "__main__":
    main()
