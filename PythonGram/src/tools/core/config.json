{"metadata": {"version": "1.0.0", "description": "Python学生C配置文件", "created_by": "ConfigManager"}, "versioning": {"student_version": "1.0", "rule_version": "1.0", "code_version": "1.0", "version_format": "v{major}.{minor}", "auto_increment": false}, "path_mappings": [{"name": "supmicro4_F_mapping", "description": "supmicro4服务器F盘映射", "network_prefix": "\\\\supmicro4\\F\\", "local_prefix": "F:\\", "enabled": true}, {"name": "supmicro4_D_mapping", "description": "supmicro4服务器D盘映射", "network_prefix": "\\\\supmicro4\\D\\", "local_prefix": "D:\\", "enabled": true}, {"name": "supmicro4_C_mapping", "description": "supmicro4服务器C盘映射", "network_prefix": "\\\\supmicro4\\C\\", "local_prefix": "C:\\", "enabled": true}], "filters": [{"name": "HighConfidenceNegativeFilter", "priority": 1, "confidence_base": 0.95, "confidence_increment": 0.02, "confidence_max": 0.99, "enabled": true, "patterns": ["警示函", "业绩预亏", "亏损", "违规", "处罚", "风险提示", "停牌", "退市", "诉讼", "仲裁", "立案调查", "监管函", "关注函", "问询函", "行政处罚", "监管处罚", "业绩大幅下滑", "预计亏损", "净利润.*下降.*50%", "营业收入.*下降.*30%", "重大风险提示", "破产.*风险", "债务.*违约", "担保.*风险", "涉及诉讼", "重大诉讼", "仲裁.*纠纷", "合同.*纠纷", "停产.*整顿", "关闭.*工厂", "清算.*程序", "重组.*失败"]}, {"name": "HighConfidencePositiveFilter", "priority": 2, "confidence_base": 0.9, "confidence_increment": 0.02, "confidence_max": 0.98, "enabled": true, "patterns": ["重大合同", "技术突破", "业绩增长", "盈利", "分红", "收购", "合作协议", "新产品发布", "业绩预增", "净利润.*增长", "营业收入.*增长", "签署.*合同", "中标", "获得订单"], "negative_check_patterns": ["亏损", "违规", "风险", "警示"]}, {"name": "HighConfidenceNeutralFilter", "priority": 3, "confidence_base": 0.99, "confidence_increment": 0.0, "confidence_max": 0.99, "enabled": true, "patterns": ["^.*股东大会.*$", "^.*工商变更.*$", "^.*董事会决议.*$", "^.*监事会决议.*$", "^.*年度报告.*$", "^.*季度报告.*$", "召开.*股东大会", "董事会.*决议", "监事会.*决议", "定期报告", "临时股东大会"], "negative_check_patterns": ["亏损", "违规", "风险", "警示"]}], "output_directories": {"base_output_dir": "E:\\LLMData\\", "test_results": "E:\\LLMData\\test_results\\", "analysis_output": "E:\\LLMData\\analysis_output\\", "logs": "E:\\LLMData\\logs\\", "processed_data": "E:\\LLMData\\processed_data\\", "reports": "E:\\LLMData\\reports\\"}, "logging": {"level": "INFO", "format": "%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s", "file_enabled": true, "file_path": "E:\\LLMData\\logs\\python_student_c.log"}, "performance": {"enable_timing": true, "enable_caching": true, "max_content_length": 100000, "timeout_seconds": 30}, "pdf_extraction": {"preferred_library": "pdfminer", "fallback_libraries": ["PyPDF2", "pypdf"], "max_file_size_mb": 50, "encoding": "utf-8"}}