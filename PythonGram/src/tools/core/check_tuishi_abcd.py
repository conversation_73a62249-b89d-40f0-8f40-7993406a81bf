#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细检查"退市"词汇在ABCD类别中的出现情况
"""

import pandas as pd

def check_tuishi_in_abcd():
    """详细检查包含'退市'的词汇在ABCD类别中的出现情况"""
    print("🔍 详细检查包含'退市'的词汇在ABCD类别中的出现情况:")
    print("="*80)
    
    try:
        # 读取CSV文件
        df = pd.read_csv('word_frequency_analysis_strict.csv', encoding='utf-8-sig')
        
        # 查找包含'退市'的词汇
        tuishi_words = df[df['word'].str.contains('退市', na=False)]
        
        total_abcd = 0
        problem_words = []
        
        for i, (_, row) in enumerate(tuishi_words.iterrows(), 1):
            abcd_count = row['A_docs'] + row['B_docs'] + row['C_docs'] + row['D_docs']
            total_abcd += abcd_count
            
            if abcd_count > 0:
                problem_words.append({
                    'word': row['word'],
                    'A': row['A_docs'],
                    'B': row['B_docs'], 
                    'C': row['C_docs'],
                    'D': row['D_docs'],
                    'E': row['E_docs'],
                    'total_abcd': abcd_count
                })
                
                print(f"{i:2d}. 词汇: {row['word']}")
                print(f"    A:{row['A_docs']} B:{row['B_docs']} C:{row['C_docs']} D:{row['D_docs']} E:{row['E_docs']}")
                print(f"    ABCD总计: {abcd_count}")
                print()
        
        print(f"📊 统计结果:")
        print(f"包含'退市'的词汇总数: {len(tuishi_words)}")
        print(f"在ABCD类别中出现的词汇数: {len(problem_words)}")
        print(f"在ABCD类别中的总出现次数: {total_abcd}")
        
        if len(problem_words) == 0:
            print("✅ 用户说得对！所有包含'退市'的词汇都完全不在ABCD类别中出现！")
            print("🎯 这证明了'退市'确实是完美的E类原子信号！")
        else:
            print("❌ 确实有一些词汇在ABCD类别中出现了")
            print("\n具体的问题词汇:")
            for word_info in problem_words:
                print(f"  - {word_info['word']}: A:{word_info['A']} B:{word_info['B']} C:{word_info['C']} D:{word_info['D']}")
        
        return problem_words
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return []

def double_check_pure_tuishi():
    """双重检查纯度为1.0的退市词汇"""
    print(f"\n🔍 双重检查纯度为1.0的'退市'词汇:")
    print("-"*60)
    
    try:
        df = pd.read_csv('word_frequency_analysis_strict.csv', encoding='utf-8-sig')
        
        # 查找包含'退市'且纯度为1.0的词汇
        pure_tuishi = df[
            (df['word'].str.contains('退市', na=False)) &
            (df['purity'] == 1.0)
        ]
        
        print(f"纯度为1.0的'退市'词汇数量: {len(pure_tuishi)}")
        
        total_abcd_pure = 0
        for _, row in pure_tuishi.iterrows():
            abcd_count = row['A_docs'] + row['B_docs'] + row['C_docs'] + row['D_docs']
            total_abcd_pure += abcd_count
            
            if abcd_count > 0:
                print(f"⚠️ 发现问题: {row['word']} 在ABCD中出现 {abcd_count} 次")
        
        print(f"纯度1.0的'退市'词汇在ABCD中的总出现次数: {total_abcd_pure}")
        
        if total_abcd_pure == 0:
            print("✅ 确认：所有纯度1.0的'退市'词汇都完全不在ABCD类别中出现！")
        
    except Exception as e:
        print(f"❌ 双重检查失败: {e}")

if __name__ == "__main__":
    problem_words = check_tuishi_in_abcd()
    double_check_pure_tuishi()
    
    if not problem_words:
        print(f"\n🎉 用户的观察完全正确！")
        print(f"💡 '退市'相关词汇确实是完美的E类专属信号！")
        print(f"🎯 这为我们的分类器提供了极其可靠的特征！")
