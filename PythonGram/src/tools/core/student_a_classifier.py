#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学生A分类器 - 基于@PreClassifier.java逻辑的Python实现
使用规则库而不是硬编码正则表达式
"""

import json
import re
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class FilterResult(Enum):
    """过滤结果枚举（对应Java IAnnouncementFilter.FilterResult）"""
    POSITIVE = ("正面", "明显正面公告")
    NEGATIVE = ("负面", "明显负面公告") 
    NEUTRAL = ("中性", "明确中性公告")
    UNCERTAIN = ("不确定", "需要LLM进一步分析")
    
    def __init__(self, label: str, description: str):
        self.label = label
        self.description = description

@dataclass
class ClassificationResult:
    """分类结果（对应Java ClassificationResult）"""
    result: FilterResult
    filter_name: str
    confidence: float
    processing_time_ms: int
    matched_rules: List[str]
    reasoning: List[str]

class StudentAClassifier:
    """学生A分类器 - 基于@PreClassifier.java逻辑"""
    
    def __init__(self, rule_library_path: str = "student_a_rule_library.json"):
        self.rule_library_path = Path(rule_library_path)
        self.rules = {}
        self.compiled_patterns = {}
        self.metadata = {}
        self._load_rule_library()
    
    def _load_rule_library(self):
        """加载规则库"""
        try:
            if not self.rule_library_path.exists():
                print(f"❌ 学生A规则库文件不存在: {self.rule_library_path}")
                return False
            
            with open(self.rule_library_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.metadata = data.get('metadata', {})
            self.rules = {
                'negative': data.get('negative_rules', {}),
                'positive': data.get('positive_rules', {}),
                'neutral': data.get('neutral_rules', {}),
                'negative_check': data.get('negative_check_patterns', {})
            }
            
            self._compile_patterns()
            
            print(f"✅ 学生A规则库加载成功: {self.metadata.get('version', 'unknown')}")
            print(f"   负面规则: {len(self.rules['negative'])} 类")
            print(f"   正面规则: {len(self.rules['positive'])} 类")
            print(f"   中性规则: {len(self.rules['neutral'])} 类")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载学生A规则库失败: {e}")
            return False
    
    def _compile_patterns(self):
        """编译所有正则表达式模式"""
        self.compiled_patterns = {}
        
        for category, rules in self.rules.items():
            if category == 'negative_check':
                # 负面检查模式单独处理
                self.compiled_patterns[category] = {}
                for rule_name, rule_data in rules.items():
                    patterns = rule_data.get('patterns', [])
                    compiled_patterns = []
                    
                    for pattern in patterns:
                        try:
                            compiled_patterns.append(re.compile(pattern))
                        except re.error as e:
                            print(f"⚠️ 正则表达式编译失败: {pattern}, 错误: {e}")
                    
                    self.compiled_patterns[category][rule_name] = compiled_patterns
            else:
                # 主要分类规则
                self.compiled_patterns[category] = {}
                for rule_name, rule_data in rules.items():
                    patterns = rule_data.get('patterns', [])
                    compiled_patterns = []
                    
                    for pattern in patterns:
                        try:
                            compiled_patterns.append(re.compile(pattern))
                        except re.error as e:
                            print(f"⚠️ 正则表达式编译失败: {pattern}, 错误: {e}")
                    
                    self.compiled_patterns[category][rule_name] = compiled_patterns
    
    def classify(self, title: str, content: str = "") -> ClassificationResult:
        """分类公告（对应Java PreClassifier.classify）"""
        start_time = time.time()
        
        try:
            # 获取公告文本
            full_text = f"{title} {content}".strip()
            
            # 按优先级顺序检查：负面(1) -> 正面(2) -> 中性(3)
            
            # 1. 检查负面规则（最高优先级）
            negative_result = self._check_negative_rules(full_text)
            if negative_result:
                processing_time = int((time.time() - start_time) * 1000)
                return ClassificationResult(
                    result=FilterResult.NEGATIVE,
                    filter_name="HighConfidenceNegativeFilter",
                    confidence=negative_result['confidence'],
                    processing_time_ms=processing_time,
                    matched_rules=negative_result['matched_rules'],
                    reasoning=negative_result['reasoning']
                )
            
            # 2. 检查正面规则（第二优先级）
            positive_result = self._check_positive_rules(full_text)
            if positive_result:
                processing_time = int((time.time() - start_time) * 1000)
                return ClassificationResult(
                    result=FilterResult.POSITIVE,
                    filter_name="HighConfidencePositiveFilter",
                    confidence=positive_result['confidence'],
                    processing_time_ms=processing_time,
                    matched_rules=positive_result['matched_rules'],
                    reasoning=positive_result['reasoning']
                )
            
            # 3. 检查中性规则（第三优先级）
            neutral_result = self._check_neutral_rules(full_text)
            if neutral_result:
                processing_time = int((time.time() - start_time) * 1000)
                return ClassificationResult(
                    result=FilterResult.NEUTRAL,
                    filter_name="HighConfidenceNeutralFilter",
                    confidence=neutral_result['confidence'],
                    processing_time_ms=processing_time,
                    matched_rules=neutral_result['matched_rules'],
                    reasoning=neutral_result['reasoning']
                )
            
            # 所有过滤器都无法确定
            processing_time = int((time.time() - start_time) * 1000)
            return ClassificationResult(
                result=FilterResult.UNCERTAIN,
                filter_name="ALL_FILTERS",
                confidence=0.0,
                processing_time_ms=processing_time,
                matched_rules=[],
                reasoning=["所有过滤器都无法确定，建议交给LLM处理"]
            )
            
        except Exception as e:
            processing_time = int((time.time() - start_time) * 1000)
            return ClassificationResult(
                result=FilterResult.UNCERTAIN,
                filter_name="ERROR",
                confidence=0.0,
                processing_time_ms=processing_time,
                matched_rules=[],
                reasoning=[f"分类过程中发生异常: {e}"]
            )
    
    def _check_negative_rules(self, text: str) -> Optional[Dict]:
        """检查负面规则"""
        if 'negative' not in self.compiled_patterns:
            return None
        
        matches = []
        for rule_name, patterns in self.compiled_patterns['negative'].items():
            for pattern in patterns:
                if pattern.search(text):
                    matches.append(f"negative_{rule_name}")
                    break  # 一个规则类匹配即可
        
        if matches:
            confidence = max(0.95, len(matches) * 0.02)  # 基础置信度0.95
            return {
                'confidence': min(confidence, 0.99),
                'matched_rules': matches,
                'reasoning': [f"匹配 {len(matches)} 个负面规则"]
            }
        
        return None
    
    def _check_positive_rules(self, text: str) -> Optional[Dict]:
        """检查正面规则（包含负面词汇检查）"""
        if 'positive' not in self.compiled_patterns:
            return None
        
        # 首先检查是否包含负面词汇
        if self._contains_negative_keywords(text):
            return None
        
        matches = []
        for rule_name, patterns in self.compiled_patterns['positive'].items():
            for pattern in patterns:
                if pattern.search(text):
                    matches.append(f"positive_{rule_name}")
                    break
        
        if matches:
            confidence = max(0.92, len(matches) * 0.02)  # 基础置信度0.92
            return {
                'confidence': min(confidence, 0.99),
                'matched_rules': matches,
                'reasoning': [f"匹配 {len(matches)} 个正面规则"]
            }
        
        return None
    
    def _check_neutral_rules(self, text: str) -> Optional[Dict]:
        """检查中性规则"""
        if 'neutral' not in self.compiled_patterns:
            return None
        
        matches = []
        for rule_name, patterns in self.compiled_patterns['neutral'].items():
            for pattern in patterns:
                if pattern.search(text):
                    matches.append(f"neutral_{rule_name}")
                    break
        
        if matches:
            return {
                'confidence': 0.9,
                'matched_rules': matches,
                'reasoning': [f"匹配 {len(matches)} 个中性规则"]
            }
        
        return None
    
    def _contains_negative_keywords(self, text: str) -> bool:
        """检查是否包含负面词汇（避免正面误判）"""
        if 'negative_check' not in self.compiled_patterns:
            return False
        
        for rule_name, patterns in self.compiled_patterns['negative_check'].items():
            for pattern in patterns:
                if pattern.search(text):
                    return True
        
        return False

def main():
    """测试学生A分类器"""
    classifier = StudentAClassifier()
    
    test_cases = [
        "关于收到证监会警示函的公告",
        "2023年业绩预亏公告", 
        "2023年业绩预增公告",
        "关于召开股东大会的通知",
        "重大合同签订公告"
    ]
    
    for title in test_cases:
        result = classifier.classify(title)
        print(f"\n{title}")
        print(f"  结果: {result.result.label}")
        print(f"  置信度: {result.confidence:.2f}")
        print(f"  匹配规则: {result.matched_rules}")
        print(f"  推理: {result.reasoning}")

if __name__ == "__main__":
    main()










