#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行改进后的JSON特征分析
使用ratingLevel作为真实标签，非交互式运行
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from simple_json_analyzer import SimpleJsonAnalyzer
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    print("🚀 运行改进后的JSON特征分析...")
    print("使用ratingLevel作为真实标签，处理500个文件进行测试")
    
    # 创建分析器
    analyzer = SimpleJsonAnalyzer()
    
    # 运行分析（限制500个文件）
    success = analyzer.run_analysis(max_files=500)
    
    if success:
        print("\n🎉 分析成功完成！")
        
        # 获取Top特征
        top_features = analyzer.get_top_features(top_n=50, min_score=0.2)
        
        print(f"\n💡 生成了 {len(top_features)} 个高质量特征")
        
        # 分析特征质量
        print(f"\n🔍 特征质量分析:")
        
        # 按类别分组显示
        category_features = {'negative': [], 'positive': [], 'neutral': []}
        
        for word, scores in top_features:
            dist = scores['category_distribution']
            main_category = max(dist.items(), key=lambda x: x[1])[0]
            main_ratio = dist[main_category] / scores['total_frequency']
            
            if main_ratio > 0.6:  # 主要类别占比超过60%
                category_features[main_category].append((word, scores, main_ratio))
        
        for category, features in category_features.items():
            if features:
                print(f"\n📊 {category.upper()} 类别特征 (Top 10):")
                features.sort(key=lambda x: x[1]['comprehensive_score'], reverse=True)
                for i, (word, scores, ratio) in enumerate(features[:10], 1):
                    print(f"  {i:2d}. {word}: 分数={scores['comprehensive_score']:.3f}, "
                          f"主类别占比={ratio:.1%}, 总频次={scores['total_frequency']}")
        
        # 检查是否有真正的风险词汇
        risk_keywords = ['亏损', '下降', '风险', '违规', '处罚', '诉讼', '警示', '退市']
        found_risk_words = []
        
        for word, scores in top_features:
            for risk_word in risk_keywords:
                if risk_word in word:
                    found_risk_words.append((word, scores))
        
        if found_risk_words:
            print(f"\n⚠️ 发现的风险相关词汇:")
            for word, scores in found_risk_words:
                print(f"  {word}: 分数={scores['comprehensive_score']:.3f}")
        else:
            print(f"\n⚠️ 未发现明显的风险词汇，可能需要扩大样本或检查数据源")
        
    else:
        print("\n❌ 分析失败！")

if __name__ == "__main__":
    main()
