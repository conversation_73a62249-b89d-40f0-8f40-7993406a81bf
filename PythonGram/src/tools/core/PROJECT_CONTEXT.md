# 师生协同规则优化系统 - 项目背景

## 🎯 项目目标
构建师生协同的规则优化系统，减少LLM API调用成本，提高中性公告过滤效率。

## 🏗️ 系统架构

### 角色定义
- **Java学生**: `java_regex_filter` - 快速、高效的规则执行引擎
- **Python老师**: 深度分析、规则优化、质量评估引擎
- **共同规则库**: `shared_rule_library.json` - 两者之间的知识传递媒介

### 三层过滤体系
```
第1层：legacy_db_rules (v_2022_q4) → 历史经验规则
第2层：java_regex_filter (v1.0+) → 基于共享规则库的新规则  
第3层：LLM API (Prompt 3.0) → 最终的智能分析
```

## 📊 数据资源

### 文本数据
- **总公告数**: 70万份
- **已分析**: 10万份（LLM Prompt 3.0分析结果）
- **日处理量**: 1000-5000份
- **数据字段**: ratingLevel, eventTags, sentimentScore, keyFactors, reasoning等

### 市场数据
- **K线数据**: tick级别，完整复权
- **覆盖范围**: 与公告对应的股票价格数据
- **应用**: 规则验证、隐藏信号发现

## 🎯 当前优先级

### 最高优先级: Python老师学习系统
目标：从10万份LLM结果中学习规则模式，生成java_regex_filter规则

### 核心任务
1. 基于ratingLevel分类提取词频特征
2. 执行集合运算：词频集非中性 = excluded_negative + excluded_positive - definitely_neutral
3. 生成简洁的正则表达式规则
4. 更新shared_rule_library.json

### 次要优先级
1. 宽容度测算系统（A/B测试机制）
2. 市场数据验证增强
3. 扩展到其他文本资料甄别

## 🔄 师生协同工作流

```
1. Java学生 → 应用当前规则库 → 生成分类结果
2. Python老师 → 分析LLM结果 → 发现新模式
3. Python老师 → 更新shared_rule_library.json → 留痕记录
4. Java学生 → 加载新规则 → 改进过滤效果
5. 循环迭代 → 持续优化
```

## 💡 技术要点

### LLM Prompt 3.0 输出结构
```json
{
  "ratingLevel": "优/好/中/差/劣",
  "eventTags": ["业绩预增", "重大合同", ...],
  "sentimentScore": -1.0 to 1.0,
  "keyFactors": ["关键因素1", "关键因素2"],
  "reasoning": "事实-分析-结论三步结构",
  "confidence": 0.0 to 1.0,
  "version": "3.0"
}
```

### 现有规则库结构
- `legacy_db_rules`: 复杂但精确的历史规则
- `java_regex_filter`: 简洁易维护的新规则
- `shared_rule_library.json`: 标准化的规则格式

## 🎯 成功指标

### 主要KPI
- **过滤率**: 目标过滤50-80%的中性公告
- **准确率**: 负面信息漏网率<2%
- **成本节省**: 减少LLM API调用30-80%

### 质量指标
- **规则可维护性**: 简洁易懂的正则表达式
- **学习能力**: 持续从新数据中改进
- **扩展性**: 可应用到其他文本资料

## 📝 下一步行动

1. **立即**: 实现Python老师学习系统
2. **短期**: 建立宽容度测算机制
3. **中期**: 集成市场数据验证
4. **长期**: 扩展到通用文本甄别系统

---
*最后更新: 2025-08-24*
*讨论记录: 已与AI助手深度讨论系统架构和实施方案*
