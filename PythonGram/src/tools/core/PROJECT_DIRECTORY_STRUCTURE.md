# 原子信号拆解系统 - 项目目录结构

**更新时间**: 2025-08-27  
**版本**: v3.3.0 - 路径配置优化版本

## 📁 目录结构概览

### 🔧 开发环境 (D盘)
```
D:\LLMData\PythonGram\src\tools\core\
├── 📄 核心代码文件
│   ├── atomic_signal_learning_system.py          # 原子信号学习系统
│   ├── learning_system_manager.py                # 学习系统管理器
│   ├── intermediate_files_manager.py             # 中间文件管理器
│   ├── enhanced_lexicon_with_controversial_words.py  # 增强词库系统
│   ├── paths_config.py                          # 路径配置管理 ⭐ 新增
│   └── ...其他核心代码
├── 📄 配置文件
│   ├── final_production_lexicon.json            # 最终生产词库
│   ├── enhanced_financial_lexicon.json          # 增强词库
│   └── ...其他配置文件
└── 📁 临时工作文件
    ├── atomic_learning_workspace/                # 学习工作区
    └── temp_workspace/                          # 临时目录
```

### 📚 研究档案 (E盘) - 已迁移
```
E:\LLMData\research_archive\
├── 📁 01_原始数据/                    (57个文件)
│   ├── production_lexicon_*.json      # 🏆 最终生产词库
│   ├── atomic_signal_lexicon.json     # 原子信号词库
│   ├── enhanced_financial_lexicon.json # 增强词库
│   └── ...LLM结果、配置文件等
├── 📁 02_词频分析/                    (0个文件)
├── 📁 03_统计分析/                    (2个文件)
│   └── information_gain_scorer.py     # 信息增益计算
├── 📁 04_特征工程/                    (13个文件)
│   ├── negative_purity_analysis.csv   # 纯度分析 (5.96MB)
│   ├── atomic_signal_learning_system.py # 学习系统
│   └── ...特征提取相关文件
├── 📁 05_词库演进/                    (0个文件)
├── 📁 06_测试验证/                    (3个文件)
│   └── enhanced_test_results.csv      # 测试结果
├── 📁 07_学习日志/                    (1个文件)
├── 📁 08_配置文件/                    (17个文件)
│   └── ...项目文档和配置
├── 📁 09_可视化结果/                  (0个文件)
├── 📁 10_研究笔记/                    (0个文件)
├── 📄 RESEARCH_INDEX.md               # 📋 研究档案索引
└── 📄 organization_report_*.json      # 整理报告
```

### 🔄 学习工作区 (E盘)
```
E:\LLMData\atomic_learning_workspace\
├── 📁 llm_answers/                    # LLM答案提取结果
├── 📁 low_recall_cases/               # 负召回案例
├── 📁 segmented_texts/                # 分词结果
├── 📁 atomic_signals/                 # 原子信号
├── 📁 production_lexicon/             # 生产词库版本
└── 📁 logs/                          # 学习日志
```

### 📊 数据源 (E盘)
```
E:\LLMData\analysis_output\            # LLM分析结果 (40,421个文件)
E:\LLMData\PythonGram\src\tools\output\ # 原始数据输出
```

## 🎯 路径配置管理

### 核心配置文件: `paths_config.py`
```python
PATHS = {
    "DEVELOPMENT_ROOT": "D:/LLMData/PythonGram/src/tools/core",
    "RESEARCH_ARCHIVE": "E:/LLMData/research_archive",        # ⭐ 已迁移
    "LEARNING_WORKSPACE": "E:/LLMData/atomic_learning_workspace",
    "PRODUCTION_LEXICON": "E:/LLMData/research_archive/01_raw_data",
    "LLM_DATA_SOURCE": "E:/LLMData/analysis_output",
    "RAW_DATA": "E:/LLMData/PythonGram/src/tools/output",
    "TEMP_WORKSPACE": "temp_workspace"
}
```

### 主要功能
- ✅ **统一路径管理** - 所有系统组件使用统一配置
- ✅ **自动路径验证** - 启动时检查关键路径是否存在
- ✅ **备用路径支持** - 提供向后兼容性
- ✅ **智能路径查找** - 自动查找最新的生产词库文件

## 🚀 系统优势

### 1. IDE性能优化
- **减少扫描负担** - 研究档案不在开发目录中
- **提升响应速度** - 开发环境更轻量
- **清晰项目结构** - 开发代码与研究数据分离

### 2. 存储管理优化
- **E盘空间更大** - 研究档案可持续增长
- **分离备份策略** - 代码与数据采用不同备份方案
- **权限管理** - 可对研究档案设置特殊访问权限

### 3. 维护便利性
- **统一配置管理** - 路径变更只需修改一个文件
- **自动兼容处理** - 支持旧路径的平滑迁移
- **错误处理完善** - 路径不存在时自动创建或提示

## 📋 使用指南

### 开发环境启动
```bash
cd D:\LLMData\PythonGram\src\tools\core
python learning_system_manager.py    # 启动管理器
python paths_config.py              # 检查路径配置
```

### 关键文件位置
- **🏆 最终生产词库**: `E:\LLMData\research_archive\01_raw_data\production_lexicon_20250827_105452.json`
- **📋 研究档案索引**: `E:\LLMData\research_archive\RESEARCH_INDEX.md`
- **🎛️ 路径配置**: `D:\LLMData\PythonGram\src\tools\core\paths_config.py`

### 系统管理
- **查看系统状态**: 管理器选项 1
- **运行学习周期**: 管理器选项 2 (自动使用配置路径)
- **导出生产词库**: 管理器选项 5 (自动查找最新版本)

## 🔄 版本更新记录

### v3.3.0 - 路径配置优化版本 (2025-08-27)
- ✅ 创建统一路径配置管理系统
- ✅ 研究档案迁移到 E:\LLMData\research_archive\
- ✅ 更新所有系统组件使用新路径配置
- ✅ 保持向后兼容性和错误处理
- ✅ 优化IDE性能和存储管理

### v3.2.0 - 中间文件管理版本
- ✅ 实现中间文件统一分类管理
- ✅ 创建10大类别的研究档案结构

### v3.0.0 - 闭环学习系统版本
- ✅ 实现完整的自学习优化流程
- ✅ 原子信号拆解方法论

## 💡 最佳实践

### 开发工作流
1. **开发阶段** - 在 D 盘开发环境中编码和测试
2. **数据处理** - 系统自动使用 E 盘路径处理大数据
3. **结果归档** - 研究成果自动保存到 E 盘研究档案

### 备份策略
- **开发代码** - 频繁备份，版本控制
- **研究档案** - 定期备份，长期保存
- **生产词库** - 实时备份，多版本保留

### 性能优化
- **IDE配置** - 排除 E 盘目录的索引扫描
- **磁盘管理** - E 盘专用于大数据存储
- **内存使用** - 大文件处理时监控内存占用

---

**🎉 路径配置优化完成！系统现在具有更好的性能、更清晰的结构和更便捷的管理方式！**
