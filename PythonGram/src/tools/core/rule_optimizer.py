#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
规则优化器
分析误判案例，生成更精确的规则
"""

import os
import sys
import pandas as pd
import re
from collections import defaultdict, Counter
from backtester import RuleBacktester

class RuleOptimizer:
    def __init__(self):
        self.backtester = RuleBacktester()
        
    def analyze_false_positives(self):
        """分析误判案例，找出规则过于宽泛的问题"""
        
        print("🔍 开始分析误判案例...")
        
        # 加载数据
        neutral_csv = "neutral_cases.csv"
        if not os.path.exists(neutral_csv):
            print("❌ 中性案例文件不存在")
            return
        
        neutral_df = pd.read_csv(neutral_csv, encoding='utf-8')
        print(f"📊 加载了 {len(neutral_df)} 个中性案例")
        
        # 分析误判案例
        false_positives = []
        for idx, row in neutral_df.iterrows():
            full_text = self.backtester._get_full_text(row)
            is_match, matched_rules = self.backtester._check_match(full_text)
            
            if is_match:
                false_positives.append({
                    'index': idx,
                    'title': row.get('title', ''),
                    'summary': row.get('summary', ''),
                    'matched_rules': matched_rules,
                    'text_sample': full_text[:200] + '...' if len(full_text) > 200 else full_text
                })
        
        print(f"📈 发现 {len(false_positives)} 个误判案例")
        
        # 分析误判原因
        self._analyze_false_positive_patterns(false_positives)
        
        return false_positives
    
    def _analyze_false_positive_patterns(self, false_positives):
        """分析误判案例的模式"""
        
        print("\n🔍 误判案例分析:")
        print("=" * 80)
        
        # 按规则分类统计
        rule_fp_counts = defaultdict(int)
        rule_fp_examples = defaultdict(list)
        
        for fp in false_positives:
            for rule in fp['matched_rules']:
                rule_fp_counts[rule] += 1
                rule_fp_examples[rule].append(fp)
        
        # 显示各规则的误判情况
        for rule_name, count in sorted(rule_fp_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"\n📋 {rule_name}: {count} 个误判")
            
            # 显示前3个误判案例
            examples = rule_fp_examples[rule_name][:3]
            for i, example in enumerate(examples, 1):
                print(f"   {i}. 标题: {example['title']}")
                print(f"      文本样本: {example['text_sample'][:100]}...")
                print()
        
        # 分析误判的常见模式
        self._identify_common_patterns(false_positives)
    
    def _identify_common_patterns(self, false_positives):
        """识别误判的常见模式"""
        
        print("\n🔍 误判模式分析:")
        print("=" * 80)
        
        # 分析标题中的关键词
        title_keywords = []
        for fp in false_positives:
            title = fp['title'].lower()
            # 提取可能的关键词
            words = re.findall(r'[a-zA-Z\u4e00-\u9fff]+', title)
            title_keywords.extend(words)
        
        # 统计关键词频率
        keyword_counts = Counter(title_keywords)
        common_keywords = keyword_counts.most_common(20)
        
        print("📊 误判案例标题中的常见词汇:")
        for keyword, count in common_keywords:
            if count > 1:  # 只显示出现多次的词汇
                print(f"   {keyword}: {count} 次")
        
        # 分析误判的文本特征
        self._analyze_text_features(false_positives)
    
    def _analyze_text_features(self, false_positives):
        """分析误判案例的文本特征"""
        
        print("\n🔍 文本特征分析:")
        print("=" * 80)
        
        # 分析包含特定词汇的误判案例
        suspicious_patterns = [
            '公告', '通知', '提示', '说明', '披露', '报告', '会议', '决议',
            '变更', '登记', '聘任', '辞职', '选举', '审议', '通过', '批准'
        ]
        
        pattern_matches = defaultdict(list)
        for fp in false_positives:
            text = fp['text_sample'].lower()
            for pattern in suspicious_patterns:
                if pattern in text:
                    pattern_matches[pattern].append(fp)
        
        print("📊 包含特定词汇的误判案例:")
        for pattern, matches in sorted(pattern_matches.items(), key=lambda x: len(x[1]), reverse=True):
            if len(matches) > 1:
                print(f"   {pattern}: {len(matches)} 个案例")
                # 显示一个例子
                example = matches[0]
                print(f"      示例: {example['title']}")
    
    def generate_optimized_rules(self):
        """基于分析结果生成优化后的规则"""
        
        print("\n🚀 生成优化后的规则...")
        print("=" * 80)
        
        # 基于误判分析，生成更精确的规则
        optimized_rules = {
            # 监管处罚类 - 更精确，避免误判
            '监管处罚类': [
                r'收到.*证监会.*警示函',
                r'收到.*证监会.*监管函',
                r'收到.*证监会.*关注函',
                r'收到.*证监会.*问询函',
                r'被.*证监会.*立案调查',
                r'被.*证监会.*行政处罚',
                r'被.*证监会.*监管处罚'
            ],
            
            # 业绩风险类 - 更精确的业绩指标
            '业绩风险类': [
                r'业绩预亏.*公告',
                r'业绩.*大幅下滑.*公告',
                r'预计.*亏损.*公告',
                r'净利润.*同比下降.*50%',
                r'营业收入.*同比下降.*30%',
                r'业绩.*预警.*公告'
            ],
            
            # 经营风险类 - 更具体的风险描述
            '经营风险类': [
                r'重大风险提示.*公告',
                r'退市风险.*提示',
                r'破产.*风险.*公告',
                r'债务.*违约.*公告',
                r'担保.*风险.*公告'
            ],
            
            # 诉讼纠纷类 - 更明确的诉讼信息
            '诉讼纠纷类': [
                r'涉及.*重大诉讼.*公告',
                r'重大诉讼.*公告',
                r'仲裁.*纠纷.*公告',
                r'合同.*纠纷.*公告',
                r'诉讼.*公告'
            ],
            
            # 经营异常类 - 更具体的异常情况
            '经营异常类': [
                r'停产.*整顿.*公告',
                r'关闭.*工厂.*公告',
                r'清算.*程序.*公告',
                r'重组.*失败.*公告'
            ]
        }
        
        # 生成优化后的正则表达式
        optimized_patterns = {}
        for rule_name, patterns in optimized_rules.items():
            # 将多个模式组合成一个正则表达式
            combined_pattern = '|'.join(f'({pattern})' for pattern in patterns)
            optimized_patterns[rule_name] = re.compile(combined_pattern)
            print(f"✅ {rule_name}: {len(patterns)} 个精确模式")
        
        return optimized_patterns
    
    def test_optimized_rules(self, optimized_patterns):
        """测试优化后的规则效果"""
        
        print("\n🧪 测试优化后的规则...")
        print("=" * 80)
        
        # 创建测试用的回测器
        test_tester = RuleBacktester()
        
        # 临时替换规则
        original_rules = test_tester.rules
        test_tester.rules = optimized_patterns
        
        # 执行回测
        results = test_tester.backtest()
        
        if not results:
            print("❌ 优化规则回测失败")
            return
        
        # 分析结果
        stats = results['performance_stats']
        metrics = results['overall_metrics']
        
        print(f"📊 优化后规则性能:")
        print(f"   真正例 (TP): {stats['true_positives']}")
        print(f"   假正例 (FP): {stats['false_positives']}")
        print(f"   召回率: {metrics['recall']:.2%}")
        print(f"   精确率: {metrics['precision']:.2%}")
        print(f"   F1分数: {metrics['f1_score']:.3f}")
        
        # 恢复原始规则
        test_tester.rules = original_rules
        
        return results
    
    def create_java_rules(self, optimized_patterns):
        """生成Java代码格式的规则"""
        
        print("\n💻 生成Java代码格式的规则...")
        print("=" * 80)
        
        java_code = """// 优化后的高置信度负面过滤器规则
// 基于误判案例分析生成，提高精确率

private static final List<Pattern> negativePatterns = List.of(
"""
        
        for rule_name, pattern in optimized_patterns.items():
            java_code += f"    // {rule_name}\n"
            # 将Python正则转换为Java格式
            java_pattern = pattern.pattern()
            # 处理转义字符
            java_pattern = java_pattern.replace('\\', '\\\\')
            java_code += f"    Pattern.compile(\"{java_pattern}\"),\n"
        
        java_code += ");"
        
        # 保存到文件
        with open('optimized_java_rules.java', 'w', encoding='utf-8') as f:
            f.write(java_code)
        
        print("✅ Java规则代码已保存到: optimized_java_rules.java")
        return java_code

def main():
    """主函数"""
    print("🚀 开始规则优化流程...")
    
    optimizer = RuleOptimizer()
    
    try:
        # 1. 分析误判案例
        false_positives = optimizer.analyze_false_positives()
        
        if not false_positives:
            print("✅ 没有发现误判案例，规则已经很精确了")
            return
        
        # 2. 生成优化后的规则
        optimized_patterns = optimizer.generate_optimized_rules()
        
        # 3. 测试优化后的规则
        test_results = optimizer.test_optimized_rules(optimized_patterns)
        
        # 4. 生成Java代码
        java_rules = optimizer.create_java_rules(optimized_patterns)
        
        print(f"\n🎉 规则优化完成！")
        print(f"💡 建议：")
        print(f"   1. 检查优化后的规则性能")
        print(f"   2. 将新规则同步到Java系统")
        print(f"   3. 监控新规则的误判情况")
        
    except Exception as e:
        print(f"\n❌ 规则优化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()




