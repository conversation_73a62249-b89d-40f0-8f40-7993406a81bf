{"verification_timestamp": "2025-08-25T19:16:55.417426", "analysis_output_dir": "E:\\\\LLMData\\\\analysis_output", "total_files_verified": 100, "verification_results": [{"file_name": "9355832.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9355832.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9358667.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9358667.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9393504.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9393504.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9369256.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9369256.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9347062.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9347062.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9372344.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9372344.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9365945.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9365945.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9378673.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9378673.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9330041.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9330041.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9324378.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9324378.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9362926.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9362926.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9368945.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9368945.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9362506.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9362506.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9339653.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9339653.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9372120.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9372120.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9329365.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9329365.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9336817.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9336817.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9343468.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9343468.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9377757.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9377757.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9341321.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9341321.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9380479.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9380479.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9376168.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9376168.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9367892.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9367892.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9370122.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9370122.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9390821.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9390821.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9353681.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9353681.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9372494.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9372494.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9351945.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9351945.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9353046.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9353046.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9371926.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9371926.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9352725.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9352725.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9366760.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9366760.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9339348.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9339348.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9342227.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9342227.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9377314.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9377314.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9326134.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9326134.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9366106.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9366106.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9391222.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9391222.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9333070.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9333070.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9378998.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9378998.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9350138.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9350138.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9337910.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9337910.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9334788.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9334788.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9333865.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9333865.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9358716.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9358716.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9337170.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9337170.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9369820.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9369820.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9361804.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9361804.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9358845.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9358845.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9331037.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9331037.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9379579.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9379579.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9357855.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9357855.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9367309.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9367309.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9376666.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9376666.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9329452.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9329452.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9357448.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9357448.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9323955.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9323955.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9335680.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9335680.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9379635.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9379635.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9382833.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9382833.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9395988.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9395988.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9380377.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9380377.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9358303.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9358303.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9341339.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9341339.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9376815.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9376815.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9344462.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9344462.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9346526.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9346526.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9335762.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9335762.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9326591.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9326591.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9350130.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9350130.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9337059.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9337059.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9329742.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9329742.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9369338.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9369338.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9374169.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9374169.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9341496.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9341496.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9380699.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9380699.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9394448.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9394448.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9341578.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9341578.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9394746.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9394746.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "600720_20230713_3QT0.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\600720_20230713_3QT0.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "1217359357.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\1217359357.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9357802.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9357802.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9355254.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9355254.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9393652.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9393652.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9330198.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9330198.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9364064.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9364064.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9371846.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9371846.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9333615.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9333615.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9335952.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9335952.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9362702.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9362702.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "603060_20230724_ZFIV.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\603060_20230724_ZFIV.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9331291.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9331291.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9331310.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9331310.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9390422.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9390422.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9352950.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9352950.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9352679.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9352679.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9396366.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9396366.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9343449.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9343449.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9381298.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9381298.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}, {"file_name": "9338388.json", "file_path": "E:\\\\LLMData\\\\analysis_output\\9338388.json", "has_decision_logic": true, "has_python_student_c": true, "python_student_c_version": "v1", "decision_logic_keys": ["java_regex_filter", "legacy_db_rules", "PythonStudentC"], "python_student_c_fields": ["decision", "confidence", "stock_code", "stock_name", "announcement_title", "analysis_timestamp"], "verification_status": "成功", "error_message": ""}], "summary": {"total_files": 100, "successful_files": 100, "partial_files": 0, "failed_files": 0, "error_files": 0, "has_decision_logic": 100, "has_python_student_c": 100, "versions": {"v1": 100}}}