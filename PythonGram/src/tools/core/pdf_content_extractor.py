#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF内容提取器
从python_student_c.py中外移的PDF处理功能
支持多种PDF库，提供统一的接口
"""

import logging
from typing import Optional, Dict, List
from pathlib import Path

logger = logging.getLogger(__name__)

class PDFContentExtractor:
    """PDF内容提取器"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化PDF提取器
        
        Args:
            config: 配置字典，包含路径映射等信息
        """
        self.config = config or {}
        self.path_mappings = self.config.get("path_mappings", [])
        
        # 检查可用的PDF库
        self.available_libraries = self._check_available_libraries()
        logger.info(f"可用的PDF库: {list(self.available_libraries.keys())}")
    
    def _check_available_libraries(self) -> Dict[str, bool]:
        """检查可用的PDF处理库"""
        libraries = {}
        
        # 检查PyPDF2
        try:
            import PyPDF2
            libraries['PyPDF2'] = True
            logger.debug("✅ PyPDF2 可用")
        except ImportError:
            libraries['PyPDF2'] = False
            logger.debug("❌ PyPDF2 不可用")
        
        # 检查pdfminer
        try:
            from pdfminer.high_level import extract_text
            libraries['pdfminer'] = True
            logger.debug("✅ pdfminer 可用")
        except ImportError:
            libraries['pdfminer'] = False
            logger.debug("❌ pdfminer 不可用")
        
        # 检查pypdf
        try:
            import pypdf
            libraries['pypdf'] = True
            logger.debug("✅ pypdf 可用")
        except ImportError:
            libraries['pypdf'] = False
            logger.debug("❌ pypdf 不可用")
        
        return libraries
    
    def extract_content_from_json(self, json_data: Dict) -> str:
        """
        从JSON数据中提取PDF内容
        
        Args:
            json_data: 包含PDF路径信息的JSON数据
            
        Returns:
            提取的PDF文本内容
        """
        try:
            # 尝试从sourceFile.pdfPath获取PDF路径
            source_file = json_data.get('sourceFile', {})
            pdf_path = source_file.get('pdfPath', '')
            
            if not pdf_path:
                logger.warning("未找到PDF路径")
                return ""
            
            # 转换路径并提取内容
            local_pdf_path = self._convert_network_path_to_local(pdf_path)
            content = self.extract_content_from_file(local_pdf_path)
            
            if content:
                logger.info(f"成功从PDF读取内容，长度: {len(content)} 字符")
                return content
            else:
                logger.warning("PDF内容读取失败")
                return ""
                
        except Exception as e:
            logger.error(f"PDF内容提取失败: {e}")
            return ""
    
    def extract_content_from_file(self, pdf_path: str) -> str:
        """
        从PDF文件提取内容
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            提取的文本内容
        """
        if not Path(pdf_path).exists():
            logger.error(f"PDF文件不存在: {pdf_path}")
            return ""
        
        # 按优先级尝试不同的PDF库
        extraction_methods = [
            ('pdfminer', self._extract_with_pdfminer),
            ('PyPDF2', self._extract_with_pypdf2),
            ('pypdf', self._extract_with_pypdf)
        ]
        
        for lib_name, extract_method in extraction_methods:
            if self.available_libraries.get(lib_name, False):
                try:
                    content = extract_method(pdf_path)
                    if content and content.strip():
                        logger.info(f"使用 {lib_name} 成功提取内容，长度: {len(content)} 字符")
                        return content.strip()
                except Exception as e:
                    logger.warning(f"{lib_name} 提取失败: {e}")
                    continue
        
        logger.error("所有PDF提取方法都失败了")
        return ""
    
    def _extract_with_pypdf2(self, pdf_path: str) -> str:
        """使用PyPDF2提取内容"""
        import PyPDF2
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            return text
    
    def _extract_with_pdfminer(self, pdf_path: str) -> str:
        """使用pdfminer提取内容"""
        from pdfminer.high_level import extract_text
        return extract_text(pdf_path)
    
    def _extract_with_pypdf(self, pdf_path: str) -> str:
        """使用pypdf提取内容"""
        import pypdf
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = pypdf.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            return text
    
    def _convert_network_path_to_local(self, network_path: str) -> str:
        """
        将网络路径转换为本地路径（配置驱动）
        
        Args:
            network_path: 网络路径
            
        Returns:
            转换后的本地路径
        """
        try:
            # 使用配置的路径映射
            for mapping in self.path_mappings:
                net_prefix = mapping.get("network_prefix", "")
                loc_prefix = mapping.get("local_prefix", "")
                
                if net_prefix and loc_prefix and network_path.startswith(net_prefix):
                    # 替换前缀
                    local_path = network_path.replace(net_prefix, loc_prefix, 1)
                    # 统一路径分隔符
                    local_path = local_path.replace('\\', '/')
                    logger.debug(f"路径映射: {network_path} → {local_path}")
                    return local_path
            
            # 如果没有匹配的映射，尝试默认转换
            if network_path.startswith('\\\\'):
                logger.warning(f"未找到路径映射配置，使用默认转换: {network_path}")
                return self._default_network_path_conversion(network_path)
            
            # 已经是本地路径，统一分隔符
            return network_path.replace('\\', '/')
            
        except Exception as e:
            logger.error(f"路径转换失败: {e}")
            return network_path
    
    def _default_network_path_conversion(self, network_path: str) -> str:
        """
        默认的网络路径转换（向后兼容）
        
        Args:
            network_path: 网络路径
            
        Returns:
            转换后的路径
        """
        try:
            # 移除开头的双反斜杠
            path_without_prefix = network_path[2:]
            
            # 分割路径部分
            parts = path_without_prefix.split('\\')
            
            if len(parts) >= 3:
                # 假设第一个部分是服务器名，第二个部分是共享名
                # 默认映射到F盘（向后兼容）
                drive_letter = "F:"
                file_path = "\\".join(parts[2:])  # 跳过服务器名和共享名
                
                local_path = f"{drive_letter}\\{file_path}"
                logger.warning(f"使用默认映射: {network_path} → {local_path}")
                return local_path.replace('\\', '/')
            else:
                logger.error(f"网络路径格式异常: {network_path}")
                return network_path
                
        except Exception as e:
            logger.error(f"默认路径转换失败: {e}")
            return network_path
    
    def get_extraction_stats(self) -> Dict:
        """获取提取器状态信息"""
        return {
            'available_libraries': self.available_libraries,
            'path_mappings_count': len(self.path_mappings),
            'config_loaded': bool(self.config)
        }

def test_pdf_extractor():
    """测试PDF提取器"""
    print("🧪 测试PDF内容提取器...")
    
    # 测试配置
    test_config = {
        "path_mappings": [
            {
                "network_prefix": "\\\\supmicro4\\F\\",
                "local_prefix": "D:\\LLMData\\"
            }
        ]
    }
    
    extractor = PDFContentExtractor(test_config)
    
    # 显示状态
    stats = extractor.get_extraction_stats()
    print(f"📊 提取器状态: {stats}")
    
    # 测试路径转换
    test_paths = [
        "\\\\supmicro4\\F\\test\\document.pdf",
        "D:\\local\\document.pdf",
        "\\\\unknown\\share\\document.pdf"
    ]
    
    for path in test_paths:
        converted = extractor._convert_network_path_to_local(path)
        print(f"路径转换: {path} → {converted}")

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
    )
    
    test_pdf_extractor()
