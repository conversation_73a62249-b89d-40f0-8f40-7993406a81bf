#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志设置模块
为Python学生C提供统一的日志配置
支持控制台输出、文件输出、日志级别控制等
"""

import logging
import logging.handlers
from pathlib import Path
from typing import Optional, Dict

def setup_logger(name: str = "python_student_c", 
                config: Optional[Dict] = None) -> logging.Logger:
    """
    设置统一的日志记录器
    
    Args:
        name: 日志记录器名称
        config: 日志配置字典
        
    Returns:
        配置好的日志记录器
    """
    # 默认配置
    default_config = {
        "level": "INFO",
        "format": "%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s",
        "file_enabled": False,
        "file_path": "python_student_c.log",
        "file_max_size": 10 * 1024 * 1024,  # 10MB
        "file_backup_count": 5,
        "console_enabled": True
    }
    
    # 合并配置
    if config:
        default_config.update(config)
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 设置日志级别
    level = getattr(logging, default_config["level"].upper(), logging.INFO)
    logger.setLevel(level)
    
    # 创建格式化器
    formatter = logging.Formatter(default_config["format"])
    
    # 控制台处理器
    if default_config["console_enabled"]:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # 文件处理器
    if default_config["file_enabled"]:
        file_path = Path(default_config["file_path"])
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 使用RotatingFileHandler支持日志轮转
        file_handler = logging.handlers.RotatingFileHandler(
            filename=file_path,
            maxBytes=default_config["file_max_size"],
            backupCount=default_config["file_backup_count"],
            encoding='utf-8'
        )
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def get_logger(name: str = "python_student_c") -> logging.Logger:
    """
    获取已配置的日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器
    """
    return logging.getLogger(name)

class LoggerMixin:
    """日志记录器混入类"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._logger = None

    @property
    def logger(self):
        """获取日志记录器"""
        if self._logger is None:
            self._logger = get_logger(self.__class__.__name__)
        return self._logger

def log_execution_time(func):
    """装饰器：记录函数执行时间"""
    import time
    import functools
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = get_logger()
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.debug(f"⏱️ {func.__name__} 执行时间: {execution_time:.3f}秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ {func.__name__} 执行失败 (耗时: {execution_time:.3f}秒): {e}")
            raise
    
    return wrapper

def log_method_calls(cls):
    """类装饰器：记录方法调用"""
    import functools
    
    for attr_name in dir(cls):
        attr = getattr(cls, attr_name)
        if callable(attr) and not attr_name.startswith('_'):
            setattr(cls, attr_name, log_execution_time(attr))
    
    return cls

class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self, logger_name: str = "performance"):
        self.logger = get_logger(logger_name)
        self.timers = {}
    
    def start_timer(self, name: str) -> None:
        """开始计时"""
        import time
        self.timers[name] = time.time()
        self.logger.debug(f"⏱️ 开始计时: {name}")
    
    def end_timer(self, name: str) -> float:
        """结束计时并返回耗时"""
        import time
        if name not in self.timers:
            self.logger.warning(f"⚠️ 计时器 {name} 未启动")
            return 0.0
        
        elapsed = time.time() - self.timers[name]
        del self.timers[name]
        self.logger.info(f"⏱️ {name} 耗时: {elapsed:.3f}秒")
        return elapsed
    
    def log_memory_usage(self, description: str = "") -> None:
        """记录内存使用情况"""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            self.logger.info(f"💾 内存使用 {description}: {memory_mb:.2f}MB")
        except ImportError:
            self.logger.debug("psutil未安装，无法记录内存使用情况")
        except Exception as e:
            self.logger.error(f"记录内存使用失败: {e}")

def test_logger_setup():
    """测试日志设置"""
    print("🧪 测试日志设置...")
    
    # 测试基本日志功能
    test_config = {
        "level": "DEBUG",
        "file_enabled": True,
        "file_path": "test_log.log"
    }
    
    logger = setup_logger("test_logger", test_config)
    
    # 测试各种日志级别
    logger.debug("🐛 这是调试信息")
    logger.info("ℹ️ 这是信息")
    logger.warning("⚠️ 这是警告")
    logger.error("❌ 这是错误")
    
    # 测试性能日志
    perf_logger = PerformanceLogger()
    perf_logger.start_timer("test_operation")
    
    import time
    time.sleep(0.1)  # 模拟操作
    
    elapsed = perf_logger.end_timer("test_operation")
    print(f"测试操作耗时: {elapsed:.3f}秒")
    
    # 测试内存记录
    perf_logger.log_memory_usage("测试后")
    
    # 测试装饰器
    @log_execution_time
    def test_function():
        time.sleep(0.05)
        return "测试完成"
    
    result = test_function()
    print(f"装饰器测试结果: {result}")
    
    # 清理测试文件
    import os
    test_files = ["test_log.log"]
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🧹 清理测试文件: {file}")

if __name__ == "__main__":
    test_logger_setup()
