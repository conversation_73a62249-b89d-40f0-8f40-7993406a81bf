{"metadata": {"version": "2.0.0", "created_date": "2025-08-25", "description": "学生A规则库 - 基于@PreClassifier.java的正则表达式规则", "source": "Java PreClassifier.java", "confidence_threshold": 0.95}, "negative_rules": {"监管处罚类": {"confidence": "very_high", "priority": 1, "patterns": ["立案调查", "收到.*警示函", "收到.*监管函", "收到.*关注函", "收到.*问询函", "行政处罚", "监管处罚"]}, "业绩风险类": {"confidence": "high", "priority": 1, "patterns": ["业绩预亏", "业绩大幅下滑", "预计亏损", "净利润.*下降.*50%", "营业收入.*下降.*30%"]}, "经营风险类": {"confidence": "high", "priority": 1, "patterns": ["重大风险提示", "退市风险", "破产.*风险", "债务.*违约", "担保.*风险"]}, "诉讼纠纷类": {"confidence": "high", "priority": 1, "patterns": ["涉及诉讼", "重大诉讼", "仲裁.*纠纷", "合同.*纠纷"]}, "经营异常类": {"confidence": "high", "priority": 1, "patterns": ["停产.*整顿", "关闭.*工厂", "清算.*程序", "重组.*失败"]}}, "positive_rules": {"业绩增长类": {"confidence": "high", "priority": 2, "patterns": ["业绩.*大幅增长", "净利润.*增长.*50%", "营业收入.*增长.*30%", "业绩.*预增", "扭亏为盈"]}, "重大利好类": {"confidence": "high", "priority": 2, "patterns": ["重大合同.*签订", "重大.*合同.*中标", "获得.*重大.*订单", "重大.*合作.*协议"]}, "技术突破类": {"confidence": "high", "priority": 2, "patterns": ["技术.*突破", "研发.*成功", "获得.*专利", "技术.*创新"]}, "政策利好类": {"confidence": "high", "priority": 2, "patterns": ["获得.*政策.*支持", "享受.*税收.*优惠", "获得.*政府.*补贴", "政策.*利好"]}, "市场拓展类": {"confidence": "high", "priority": 2, "patterns": ["市场.*份额.*提升", "产品.*供不应求", "产能.*扩张", "新.*产品.*上市"]}}, "neutral_rules": {"例行公告类": {"confidence": "very_high", "priority": 3, "patterns": ["关于召开.*股东大会的通知", "关于聘任.*会计师事务所的公告", "关于召开.*董事会会议的通知", "关于.*年度报告.*的公告"]}}, "negative_check_patterns": {"负面词汇检查": {"description": "避免正面误判的负面词汇", "patterns": ["风险", "下降", "亏损", "处罚", "纠纷", "违约"]}}}