#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中间文件统一管理系统
分类整理和集中管理处理过程中的所有中间文件
"""

import json
import shutil
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Set
import logging
from paths_config import paths_config, find_existing_path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IntermediateFilesManager:
    """中间文件统一管理器"""
    
    def __init__(self, archive_root: str = None):
        """
        初始化管理器

        Args:
            archive_root: 归档根目录，如果为None则使用配置的路径
        """
        self.version = "3.3.0"  # 📚 中间文件管理版本 - 路径配置更新

        # 使用新的路径配置
        if archive_root is None:
            self.archive_root = find_existing_path("RESEARCH_ARCHIVE")
        else:
            self.archive_root = Path(archive_root)
        
        # 创建分类目录结构
        self.categories = {
            "01_原始数据": {
                "path": self.archive_root / "01_raw_data",
                "description": "LLM分析结果、公告原文等原始数据",
                "patterns": ["*.json", "analysis_output/*", "*.txt", "*.pdf"]
            },
            "02_词频分析": {
                "path": self.archive_root / "02_word_frequency",
                "description": "词频统计、jieba分词结果",
                "patterns": ["word_frequency_*.xlsx", "word_frequency_*.csv", "*jieba*.json", "segmentation_*.json"]
            },
            "03_统计分析": {
                "path": self.archive_root / "03_statistical_analysis", 
                "description": "信息增益、卡方检验、P值分析",
                "patterns": ["*information_gain*", "*chi2*", "*pvalue*", "*statistics*", "word_scores_*.csv"]
            },
            "04_特征工程": {
                "path": self.archive_root / "04_feature_engineering",
                "description": "特征提取、纯度分析、原子信号拆解",
                "patterns": ["*purity*", "*feature*", "*atomic*", "extraction_*.json"]
            },
            "05_词库演进": {
                "path": self.archive_root / "05_lexicon_evolution",
                "description": "词库版本演进历史",
                "patterns": ["*lexicon*.json", "*negative_words*.json", "production_lexicon_*.json"]
            },
            "06_测试验证": {
                "path": self.archive_root / "06_testing_validation",
                "description": "测试结果、验证报告、召回率分析",
                "patterns": ["test_*.json", "*test_results*", "*validation*", "*recall*", "low_recall_*.txt"]
            },
            "07_学习日志": {
                "path": self.archive_root / "07_learning_logs",
                "description": "学习周期日志、系统运行记录",
                "patterns": ["learning_cycle_*.json", "*.log", "*report*.json", "*analysis*.txt"]
            },
            "08_配置文件": {
                "path": self.archive_root / "08_configurations",
                "description": "系统配置、参数设置",
                "patterns": ["config*.json", "*config*.py", "requirements.txt", "*.md"]
            },
            "09_可视化结果": {
                "path": self.archive_root / "09_visualizations",
                "description": "图表、报告、可视化输出",
                "patterns": ["*.png", "*.jpg", "*.html", "*chart*", "*plot*"]
            },
            "10_研究笔记": {
                "path": self.archive_root / "10_research_notes",
                "description": "研究发现、分析笔记、洞察记录",
                "patterns": ["README*.md", "NOTES*.md", "*SUMMARY*.md", "*ANALYSIS*.md"]
            }
        }
        
        # 创建目录结构
        self._create_directory_structure()
        
        logger.info(f"📚 中间文件管理器 v{self.version} 初始化完成")
        logger.info(f"📁 归档根目录: {self.archive_root}")
    
    def _create_directory_structure(self):
        """创建目录结构"""
        self.archive_root.mkdir(exist_ok=True)
        
        for category, info in self.categories.items():
            info["path"].mkdir(parents=True, exist_ok=True)
            
            # 创建说明文件
            readme_file = info["path"] / "README.md"
            if not readme_file.exists():
                with open(readme_file, 'w', encoding='utf-8') as f:
                    f.write(f"# {category}\n\n")
                    f.write(f"**描述**: {info['description']}\n\n")
                    f.write(f"**文件类型**: {', '.join(info['patterns'])}\n\n")
                    f.write(f"**创建时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
    
    def scan_and_classify_files(self, source_dirs: List[str]) -> Dict:
        """
        扫描并分类中间文件
        
        Args:
            source_dirs: 源目录列表
            
        Returns:
            分类结果统计
        """
        logger.info("🔍 开始扫描和分类中间文件...")
        
        classification_results = {
            "total_files_found": 0,
            "classified_files": 0,
            "unclassified_files": 0,
            "categories": {},
            "unclassified_list": []
        }
        
        all_files = []
        
        # 扫描所有源目录
        for source_dir in source_dirs:
            source_path = Path(source_dir)
            if source_path.exists():
                logger.info(f"📁 扫描目录: {source_dir}")
                files = list(source_path.rglob("*"))
                files = [f for f in files if f.is_file()]
                all_files.extend(files)
                logger.info(f"   发现 {len(files)} 个文件")
        
        classification_results["total_files_found"] = len(all_files)
        logger.info(f"📊 总计发现 {len(all_files)} 个文件")
        
        # 分类文件
        for file_path in all_files:
            classified = False
            
            for category, info in self.categories.items():
                if self._matches_category(file_path, info["patterns"]):
                    if category not in classification_results["categories"]:
                        classification_results["categories"][category] = []
                    
                    classification_results["categories"][category].append({
                        "file": str(file_path),
                        "size": file_path.stat().st_size,
                        "modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
                    })
                    
                    classified = True
                    break
            
            if classified:
                classification_results["classified_files"] += 1
            else:
                classification_results["unclassified_files"] += 1
                classification_results["unclassified_list"].append(str(file_path))
        
        logger.info(f"✅ 分类完成: {classification_results['classified_files']} 个已分类, {classification_results['unclassified_files']} 个未分类")
        
        return classification_results
    
    def _matches_category(self, file_path: Path, patterns: List[str]) -> bool:
        """检查文件是否匹配类别模式"""
        file_name = file_path.name.lower()
        file_path_str = str(file_path).lower()
        
        for pattern in patterns:
            pattern = pattern.lower()
            
            # 简单的通配符匹配
            if "*" in pattern:
                if pattern.startswith("*") and pattern.endswith("*"):
                    # *keyword*
                    keyword = pattern[1:-1]
                    if keyword in file_name or keyword in file_path_str:
                        return True
                elif pattern.startswith("*"):
                    # *suffix
                    suffix = pattern[1:]
                    if file_name.endswith(suffix):
                        return True
                elif pattern.endswith("*"):
                    # prefix*
                    prefix = pattern[:-1]
                    if file_name.startswith(prefix) or prefix in file_path_str:
                        return True
            else:
                # 精确匹配
                if pattern == file_name or pattern in file_path_str:
                    return True
        
        return False
    
    def organize_files(self, classification_results: Dict, copy_mode: bool = True) -> Dict:
        """
        整理文件到分类目录
        
        Args:
            classification_results: 分类结果
            copy_mode: True=复制文件, False=移动文件
            
        Returns:
            整理结果统计
        """
        logger.info(f"📦 开始整理文件 ({'复制' if copy_mode else '移动'} 模式)...")
        
        organization_results = {
            "total_organized": 0,
            "categories": {},
            "errors": []
        }
        
        for category, files in classification_results["categories"].items():
            category_path = self.categories[category]["path"]
            organized_count = 0
            
            logger.info(f"📂 整理类别: {category} ({len(files)} 个文件)")
            
            for file_info in files:
                source_file = Path(file_info["file"])
                
                if not source_file.exists():
                    continue
                
                # 生成目标文件名（避免重名）
                target_file = category_path / source_file.name
                counter = 1
                while target_file.exists():
                    stem = source_file.stem
                    suffix = source_file.suffix
                    target_file = category_path / f"{stem}_{counter:03d}{suffix}"
                    counter += 1
                
                try:
                    if copy_mode:
                        shutil.copy2(source_file, target_file)
                    else:
                        shutil.move(str(source_file), str(target_file))
                    
                    organized_count += 1
                    
                except Exception as e:
                    error_msg = f"处理文件失败 {source_file}: {e}"
                    logger.error(error_msg)
                    organization_results["errors"].append(error_msg)
            
            organization_results["categories"][category] = organized_count
            organization_results["total_organized"] += organized_count
            
            logger.info(f"   ✅ {category}: {organized_count} 个文件已整理")
        
        logger.info(f"🎉 文件整理完成! 总计整理 {organization_results['total_organized']} 个文件")
        
        return organization_results
    
    def generate_research_index(self) -> str:
        """生成研究档案索引"""
        logger.info("📋 生成研究档案索引...")
        
        index_file = self.archive_root / "RESEARCH_INDEX.md"
        
        with open(index_file, 'w', encoding='utf-8') as f:
            f.write("# 原子信号拆解研究档案索引\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**管理器版本**: v{self.version}\n\n")
            
            f.write("## 📚 档案结构\n\n")
            
            for category, info in self.categories.items():
                f.write(f"### {category}\n")
                f.write(f"**路径**: `{info['path'].relative_to(self.archive_root)}`\n\n")
                f.write(f"**描述**: {info['description']}\n\n")
                
                # 统计文件数量
                if info["path"].exists():
                    files = list(info["path"].glob("*"))
                    files = [f for f in files if f.is_file() and f.name != "README.md"]
                    f.write(f"**文件数量**: {len(files)} 个\n\n")
                    
                    if files:
                        f.write("**主要文件**:\n")
                        for file in sorted(files)[:10]:  # 只显示前10个
                            size_mb = file.stat().st_size / (1024 * 1024)
                            f.write(f"- `{file.name}` ({size_mb:.2f} MB)\n")
                        
                        if len(files) > 10:
                            f.write(f"- ... 还有 {len(files) - 10} 个文件\n")
                        f.write("\n")
                else:
                    f.write("**文件数量**: 0 个\n\n")
            
            f.write("## 🔍 研究价值说明\n\n")
            f.write("### 数据流转过程\n")
            f.write("```\n")
            f.write("原始数据 → 词频分析 → 统计分析 → 特征工程 → 词库演进\n")
            f.write("    ↓         ↓         ↓         ↓         ↓\n")
            f.write("  测试验证 ← 学习日志 ← 配置文件 ← 可视化结果 ← 研究笔记\n")
            f.write("```\n\n")
            
            f.write("### 关键研究发现\n")
            f.write("1. **原子信号拆解方法论** - 从复杂表达到最小语义单元\n")
            f.write("2. **争议词汇处理策略** - \"宁可错杀不可放过\"的风险控制\n")
            f.write("3. **自学习优化机制** - 基于负召回案例的持续改进\n")
            f.write("4. **统计显著性验证** - 卡方检验和P值分析的应用\n\n")
            
            f.write("### 使用建议\n")
            f.write("- **学术研究**: 可用于NLP、金融文本分析相关论文\n")
            f.write("- **方法复现**: 完整的处理流程和中间结果\n")
            f.write("- **系统优化**: 基于历史数据进行进一步改进\n")
            f.write("- **知识传承**: 完整的研发过程记录\n\n")
        
        logger.info(f"📋 研究档案索引已生成: {index_file}")
        return str(index_file)
    
    def create_research_package(self, package_name: str = None) -> str:
        """创建研究包（压缩档案）"""
        if package_name is None:
            package_name = f"atomic_signal_research_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        logger.info(f"📦 创建研究包: {package_name}")
        
        import zipfile
        
        package_file = f"{package_name}.zip"
        
        with zipfile.ZipFile(package_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in self.archive_root.rglob("*"):
                if root.is_file():
                    arcname = root.relative_to(self.archive_root.parent)
                    zipf.write(root, arcname)
        
        logger.info(f"✅ 研究包已创建: {package_file}")
        return package_file
    
    def run_complete_organization(self, source_dirs: List[str] = None, copy_mode: bool = True) -> Dict:
        """运行完整的文件整理流程"""
        logger.info("🚀 开始完整的中间文件整理流程...")
        
        if source_dirs is None:
            source_dirs = [
                ".",  # 当前目录
                "atomic_learning_workspace",
                "output",
                "teacher_output",
                "enhanced_test_training",
                "data"
            ]
        
        # 步骤1: 扫描和分类
        classification_results = self.scan_and_classify_files(source_dirs)
        
        # 步骤2: 整理文件
        organization_results = self.organize_files(classification_results, copy_mode)
        
        # 步骤3: 生成索引
        index_file = self.generate_research_index()
        
        # 步骤4: 保存整理报告
        report = {
            "timestamp": datetime.now().isoformat(),
            "manager_version": self.version,
            "source_directories": source_dirs,
            "copy_mode": copy_mode,
            "classification_results": classification_results,
            "organization_results": organization_results,
            "index_file": index_file
        }
        
        report_file = self.archive_root / f"organization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📊 整理报告已保存: {report_file}")
        
        return report

def main():
    """主函数"""
    print("📚 中间文件统一管理系统")
    print("="*60)
    
    # 创建管理器
    manager = IntermediateFilesManager()
    
    # 运行完整整理流程
    report = manager.run_complete_organization()
    
    print(f"\n🎉 中间文件整理完成!")
    print(f"📊 整理统计:")
    print(f"   发现文件: {report['classification_results']['total_files_found']} 个")
    print(f"   已分类: {report['classification_results']['classified_files']} 个")
    print(f"   未分类: {report['classification_results']['unclassified_files']} 个")
    print(f"   已整理: {report['organization_results']['total_organized']} 个")
    
    print(f"\n📂 分类统计:")
    for category, count in report['organization_results']['categories'].items():
        print(f"   {category}: {count} 个文件")
    
    print(f"\n📁 研究档案位置: {manager.archive_root}")
    print(f"📋 索引文件: {report['index_file']}")
    
    # 询问是否创建研究包
    create_package = input("\n是否创建研究包 (zip文件)? (y/N): ").strip().lower()
    if create_package == 'y':
        package_file = manager.create_research_package()
        print(f"📦 研究包已创建: {package_file}")

if __name__ == "__main__":
    main()
