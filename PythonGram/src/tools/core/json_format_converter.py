#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON格式转换器 - 完全保持原始LLM API数据不变
"""

import json
import re
import shutil
from pathlib import Path
from datetime import datetime, timezone
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class JSONFormatConverter:
    # --- 标准化版本管理 ---
    # 将版本号定义为常量，便于统一管理和更新
    JAVA_REGEX_FILTER_VERSION = "v1.0"
    LEGACY_DB_RULES_VERSION = "v_2022_q4"

    # --- 标准化规则分类体系 ---
    # 定义决策映射，将中文映射到标准英文术语
    DECISION_MAP = {"中性": "NEUTRAL", "负面": "NEGATIVE", "正面": "POSITIVE"}

    def __init__(self, input_dir="D:/LLMData/analysis_output", backup=True):
        self.input_dir = Path(input_dir)
        self.backup = backup
        
        if self.backup:
            self.backup_dir = self.input_dir / f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.backup_dir.mkdir(exist_ok=True)
            logger.info(f"备份目录: {self.backup_dir}")

    def _parse_combined_analysis(self, analysis_string: str, timestamp: str) -> dict:
        """
        解析旧的 combinedAnalysis 字符串，并将其转换为新的结构化格式。
        例如: "新系统:UNCERTAIN(0%) + 老系统:中性(UNKNOWN风险)"
        """
        new_decision_logic = {}

        # 正则表达式，用于精确匹配和提取信息
        java_pattern = re.compile(r"新系统:([A-Z_]+)\(([\d\.]+)%\)")
        legacy_pattern = re.compile(r"老系统:([\u4e00-\u9fa5]+)\((.*?)\)")

        java_match = java_pattern.search(analysis_string)
        legacy_match = legacy_pattern.search(analysis_string)

        if java_match:
            decision = java_match.group(1)
            confidence = float(java_match.group(2)) / 100.0
            new_decision_logic["java_regex_filter"] = {
                self.JAVA_REGEX_FILTER_VERSION: {
                    "decision": decision,
                    "confidence": confidence,
                    "matchedRules": [],
                    "timestamp": timestamp
                }
            }

        if legacy_match:
            decision_cn = legacy_match.group(1)
            rules_str = legacy_match.group(2)
            new_decision_logic["legacy_db_rules"] = {
                self.LEGACY_DB_RULES_VERSION: {
                    "decision": self.DECISION_MAP.get(decision_cn, "UNKNOWN"),
                    "confidence": None,
                    "matchedRules": [rules_str] if rules_str else [],
                    "timestamp": timestamp
                }
            }
        
        return new_decision_logic

    def convert_single_file(self, input_file):
        """转换单个文件，将其更新为新的结构化 decisionLogic 格式"""
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # --- 时间戳处理：保持文件原始时间 ---
            # 获取文件原始修改时间作为时间戳，确保数据可追溯性
            original_mtime = input_file.stat().st_mtime
            timestamp_iso = datetime.fromtimestamp(original_mtime, tz=timezone.utc).isoformat()

            # 检查是否需要转换：只处理包含旧格式 decisionLogic 的文件
            old_decision_logic = data.get('decisionLogic')
            if not (isinstance(old_decision_logic, dict) and 'combinedAnalysis' in old_decision_logic):
                logger.debug(f"文件 {input_file.name} 无需转换，跳过。")
                return None # 返回None表示未做修改

            # 解析旧的字符串
            analysis_string = old_decision_logic.get('combinedAnalysis', '')
            new_structured_logic = self._parse_combined_analysis(analysis_string, timestamp_iso)

            if not new_structured_logic:
                logger.warning(f"文件 {input_file.name} 中的 combinedAnalysis 无法解析，跳过。")
                return None

            # 删除冗余的旧字段
            keys_to_delete = ['localClassification', 'legacySystem', 'sourceFile']
            for key in keys_to_delete:
                if key in data:
                    del data[key]
            
            # 用新的结构化对象替换旧的 decisionLogic
            data['decisionLogic'] = new_structured_logic
            return data
            
        # --- 强化错误处理：解析失败时保留原始数据 ---
        # 捕获所有异常，记录错误并返回 None，主程序将不会覆盖源文件
        except Exception as e:
            logger.error(f"转换文件 {input_file.name} 时发生错误: {e}")
            return None
    
    def convert_all_files(self):
        """转换所有文件"""
        json_files = list(self.input_dir.glob("*.json"))
        logger.info(f"找到 {len(json_files)} 个文件进行检查和转换")
        
        converted_count = 0
        processed_count = 0
        for json_file in json_files:
            processed_count += 1
            # 备份
            if self.backup:
                backup_file = self.backup_dir / json_file.name
                shutil.copy2(json_file, backup_file)
            
            # 转换
            # convert_single_file 内部处理了解析错误，失败时返回 None
            new_data = self.convert_single_file(json_file)
            
            # 只有在成功转换后才写回文件
            if new_data is not None:
                logger.info(f"正在转换: {json_file.name}")
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(new_data, f, ensure_ascii=False, indent=2)
                converted_count += 1
        
        skipped_count = processed_count - converted_count
        logger.info(f"转换完成: 共处理 {processed_count} 个文件。")
        logger.info(f"  - 成功转换: {converted_count} 个")
        logger.info(f"  - 跳过（无需转换或解析失败）: {skipped_count} 个")
        return converted_count

def main():
    print("🚀 JSON格式转换器")
    print("⚠️  将原地覆盖文件，自动备份")
    
    confirm = input("确认开始？(y/N): ").strip().lower()
    if confirm != 'y':
        print("已取消")
        return
    
    converter = JSONFormatConverter()
    converter.convert_all_files()
    print("✅ 转换完成")

if __name__ == "__main__":
    main()
