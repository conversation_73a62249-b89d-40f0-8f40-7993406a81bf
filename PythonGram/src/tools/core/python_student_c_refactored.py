#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python学生C - 重构版本
基于Java Filter系统的规则引擎，重构后具备以下特性：
- 配置化路径映射和规则
- 外移PDF处理到独立模块
- 统一日志记录
- 提升可移植性和可维护性
"""

import re
import time
import json
from enum import Enum
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

# 导入重构后的模块
try:
    from config_manager import ConfigManager
    from pdf_content_extractor import PDFContentExtractor
    from logger_setup import setup_logger, LoggerMixin, log_execution_time
except ImportError as e:
    print(f"⚠️ 导入重构模块失败: {e}")
    print("请确保config_manager.py, pdf_content_extractor.py, logger_setup.py存在")
    # 提供基础功能的回退
    ConfigManager = None
    PDFContentExtractor = None
    setup_logger = None
    LoggerMixin = object
    log_execution_time = lambda x: x

# 导入抬头识别器
try:
    from student_c_header_analyzer import HeaderAnalyzer
except ImportError:
    print("⚠️ 无法导入HeaderAnalyzer，将使用基础功能")
    HeaderAnalyzer = None

class FilterResult(Enum):
    """过滤结果枚举（对应Java IAnnouncementFilter.FilterResult）"""
    POSITIVE = ("正面", "明显正面公告")
    NEGATIVE = ("负面", "明显负面公告") 
    NEUTRAL = ("中性", "明确中性公告")
    UNCERTAIN = ("不确定", "需要LLM进一步分析")
    
    def __init__(self, label: str, description: str):
        self.label = label
        self.description = description

@dataclass
class ClassificationResult:
    """分类结果数据类"""
    result: FilterResult
    confidence: float
    matched_patterns: List[str]
    processing_time: float
    filter_name: str
    header_info: Optional[Dict] = None

class BaseFilter:
    """基础过滤器类（配置化版本）"""
    
    def __init__(self, config: Dict):
        """
        初始化过滤器
        
        Args:
            config: 过滤器配置字典
        """
        self.config = config
        self.name = config.get("name", self.__class__.__name__)
        self.priority = config.get("priority", 999)
        self.confidence_base = config.get("confidence_base", 0.95)
        self.confidence_increment = config.get("confidence_increment", 0.02)
        self.confidence_max = config.get("confidence_max", 0.99)
        self.enabled = config.get("enabled", True)

        # 设置日志
        import logging
        self.logger = logging.getLogger(self.__class__.__name__)

        # 编译正则表达式模式
        self.patterns = []
        for pattern in config.get("patterns", []):
            try:
                self.patterns.append(re.compile(pattern, re.IGNORECASE))
            except re.error as e:
                self.logger.error(f"正则表达式编译失败: {pattern} - {e}")
        
        # 负面检查模式（用于排除）
        self.negative_check_patterns = []
        for pattern in config.get("negative_check_patterns", []):
            try:
                self.negative_check_patterns.append(re.compile(pattern, re.IGNORECASE))
            except re.error as e:
                self.logger.error(f"负面检查模式编译失败: {pattern} - {e}")
        
        self.logger.info(f"✅ {self.name} 初始化完成，模式数量: {len(self.patterns)}")
    
    def classify(self, title: str, content: str) -> FilterResult:
        """分类方法（子类需要实现）"""
        raise NotImplementedError("子类必须实现classify方法")
    
    def get_confidence(self, title: str, content: str) -> float:
        """计算置信度"""
        full_text = f"{title} {content}"
        match_count = sum(1 for pattern in self.patterns if pattern.search(full_text))
        
        if match_count == 0:
            return 0.0
        
        # 基础置信度 + 匹配数量加成
        confidence = self.confidence_base + (match_count - 1) * self.confidence_increment
        return min(confidence, self.confidence_max)
    
    def get_priority(self) -> int:
        """获取优先级"""
        return self.priority
    
    def is_enabled(self) -> bool:
        """是否启用"""
        return self.enabled
    
    def get_matched_patterns(self, text: str) -> List[str]:
        """获取匹配的模式"""
        matched = []
        for pattern in self.patterns:
            if pattern.search(text):
                matched.append(pattern.pattern)
        return matched

class HighConfidenceNegativeFilter(BaseFilter):
    """高置信度负面过滤器（配置化版本）"""
    
    def classify(self, title: str, content: str) -> FilterResult:
        """分类负面公告"""
        full_text = f"{title} {content}"
        
        # 检查是否匹配负面模式
        for pattern in self.patterns:
            if pattern.search(full_text):
                self.logger.debug(f"负面模式匹配: {pattern.pattern}")
                return FilterResult.NEGATIVE
        
        return FilterResult.UNCERTAIN

class HighConfidencePositiveFilter(BaseFilter):
    """高置信度正面过滤器（配置化版本）"""
    
    def classify(self, title: str, content: str) -> FilterResult:
        """分类正面公告"""
        full_text = f"{title} {content}"
        
        # 首先检查是否包含负面信号
        for neg_pattern in self.negative_check_patterns:
            if neg_pattern.search(full_text):
                self.logger.debug(f"包含负面信号，跳过正面分类: {neg_pattern.pattern}")
                return FilterResult.UNCERTAIN
        
        # 检查正面模式
        for pattern in self.patterns:
            if pattern.search(full_text):
                self.logger.debug(f"正面模式匹配: {pattern.pattern}")
                return FilterResult.POSITIVE
        
        return FilterResult.UNCERTAIN

class HighConfidenceNeutralFilter(BaseFilter):
    """高置信度中性过滤器（配置化版本）"""
    
    def classify(self, title: str, content: str) -> FilterResult:
        """分类中性公告"""
        full_text = f"{title} {content}"
        
        # 首先检查是否包含负面信号
        for neg_pattern in self.negative_check_patterns:
            if neg_pattern.search(full_text):
                self.logger.debug(f"包含负面信号，跳过中性分类: {neg_pattern.pattern}")
                return FilterResult.UNCERTAIN
        
        # 检查中性模式
        for pattern in self.patterns:
            if pattern.search(full_text):
                self.logger.debug(f"中性模式匹配: {pattern.pattern}")
                return FilterResult.NEUTRAL
        
        return FilterResult.UNCERTAIN

class PythonStudentC:
    """Python学生C - 重构版本"""
    
    def __init__(self, config_path: str = "config.json"):
        """
        初始化Python学生C
        
        Args:
            config_path: 配置文件路径
        """
        # 加载配置
        if ConfigManager:
            self.config_manager = ConfigManager(config_path)
            self.config = self.config_manager.config
            
            # 设置日志
            if setup_logger:
                log_config = self.config_manager.get_logging_config()
                setup_logger("python_student_c", log_config)

            import logging
            self.logger = logging.getLogger("python_student_c")
            
            # 初始化PDF提取器
            if PDFContentExtractor:
                pdf_config = {
                    "path_mappings": self.config_manager.get_path_mappings()
                }
                self.pdf_extractor = PDFContentExtractor(pdf_config)
            else:
                self.pdf_extractor = None
            
            # 初始化过滤器
            self._init_filters_from_config()
            
        else:
            # 回退到基础配置
            self.config_manager = None
            self.config = {}
            self.pdf_extractor = None
            self.filters = []
            print("⚠️ 使用基础配置模式")
        
        # 初始化抬头识别器
        self.header_analyzer = HeaderAnalyzer() if HeaderAnalyzer else None
        
        self.logger.info("✅ Python学生C初始化完成")
    
    def _init_filters_from_config(self):
        """从配置初始化过滤器"""
        self.filters = []
        
        if not self.config_manager:
            return
        
        filter_configs = self.config_manager.get_filter_configs()
        
        for filter_config in filter_configs:
            filter_name = filter_config.get("name", "")
            
            try:
                if filter_name == "HighConfidenceNegativeFilter":
                    filter_obj = HighConfidenceNegativeFilter(filter_config)
                elif filter_name == "HighConfidencePositiveFilter":
                    filter_obj = HighConfidencePositiveFilter(filter_config)
                elif filter_name == "HighConfidenceNeutralFilter":
                    filter_obj = HighConfidenceNeutralFilter(filter_config)
                else:
                    self.logger.warning(f"未知的过滤器类型: {filter_name}")
                    continue
                
                if filter_obj.is_enabled():
                    self.filters.append(filter_obj)
                    self.logger.info(f"✅ 加载过滤器: {filter_name}")
                else:
                    self.logger.info(f"⏭️ 跳过禁用的过滤器: {filter_name}")
                    
            except Exception as e:
                self.logger.error(f"❌ 加载过滤器失败 {filter_name}: {e}")
        
        # 按优先级排序
        self.filters.sort(key=lambda f: f.get_priority())
        self.logger.info(f"✅ 共加载 {len(self.filters)} 个过滤器")
    
    @log_execution_time
    def classify(self, title: str, content: str = "") -> ClassificationResult:
        """
        分类公告
        
        Args:
            title: 公告标题
            content: 公告内容
            
        Returns:
            分类结果
        """
        start_time = time.time()
        
        # 提取抬头信息
        header_info = None
        if self.header_analyzer:
            try:
                header_info = self.header_analyzer.extract_header_info(content)
            except Exception as e:
                self.logger.warning(f"抬头信息提取失败: {e}")
        
        # 组合文本（只组合一次）
        full_text = f"{title}\n{content}"
        
        # 依次应用过滤器
        for filter_obj in self.filters:
            try:
                result = filter_obj.classify(title, content)
                
                if result != FilterResult.UNCERTAIN:
                    # 找到确定的分类结果
                    confidence = filter_obj.get_confidence(title, content)
                    matched_patterns = filter_obj.get_matched_patterns(full_text)
                    processing_time = time.time() - start_time
                    
                    classification_result = ClassificationResult(
                        result=result,
                        confidence=confidence,
                        matched_patterns=matched_patterns,
                        processing_time=processing_time,
                        filter_name=filter_obj.name,
                        header_info=header_info
                    )
                    
                    self.logger.info(f"✅ 分类完成: {result.label} (置信度: {confidence:.3f})")
                    return classification_result
                    
            except Exception as e:
                self.logger.error(f"过滤器 {filter_obj.name} 处理异常: {e}")
                continue
        
        # 所有过滤器都无法确定，返回不确定结果
        processing_time = time.time() - start_time
        return ClassificationResult(
            result=FilterResult.UNCERTAIN,
            confidence=0.0,
            matched_patterns=[],
            processing_time=processing_time,
            filter_name="None",
            header_info=header_info
        )
    
    def classify_json_file(self, json_file_path: str, write_back: bool = True) -> Optional[ClassificationResult]:
        """
        分类JSON文件中的公告

        Args:
            json_file_path: JSON文件路径
            write_back: 是否将结果写回JSON文件

        Returns:
            分类结果，失败时返回None
        """
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            # 提取标题和内容
            title = json_data.get('title', '')
            content = json_data.get('content', '')
            content_extracted = False

            # 如果内容为空，尝试从PDF提取
            if not content and self.pdf_extractor:
                pdf_content = self.pdf_extractor.extract_content_from_json(json_data)
                if pdf_content:
                    content = pdf_content
                    content_extracted = True
                    self.logger.info("✅ 从PDF提取内容成功")

            # 执行分类
            result = self.classify(title, content)
            result.source_file = json_file_path

            # 写回结果到JSON文件
            if write_back:
                self._write_results_back_to_json(json_file_path, json_data, result, content if content_extracted else None)

            return result

        except Exception as e:
            self.logger.error(f"❌ 处理JSON文件失败 {json_file_path}: {e}")
            return None

    def _write_results_back_to_json(self, json_file_path: str, json_data: dict,
                                   classification_result: ClassificationResult,
                                   extracted_content: Optional[str] = None):
        """
        将分类结果和提取的内容写回JSON文件

        Args:
            json_file_path: JSON文件路径
            json_data: JSON数据
            classification_result: 分类结果
            extracted_content: 提取的PDF内容（如果有）
        """
        try:
            # 写入提取的内容
            if extracted_content:
                json_data['content'] = extracted_content
                json_data['contentExtractedBy'] = 'PythonStudentC'
                json_data['contentExtractedAt'] = time.strftime('%Y-%m-%dT%H:%M:%S')

            # 确保decisionLogic字段存在
            if 'decisionLogic' not in json_data:
                json_data['decisionLogic'] = {}

            # 确保python_student_c字段存在
            if 'python_student_c' not in json_data['decisionLogic']:
                json_data['decisionLogic']['python_student_c'] = {}

            # 从配置管理器获取版本号
            if self.config_manager:
                version = self.config_manager.get_current_version()
                student_version = self.config_manager.get_versioning_config().get("student_version", "1.0")
                rule_version = self.config_manager.get_rule_version()
            else:
                version = "v1.0"
                student_version = "1.0"
                rule_version = "1.0"

            # 写入学生C的答券
            json_data['decisionLogic']['python_student_c'][version] = {
                "decision": classification_result.result.name,
                "confidence": classification_result.confidence,
                "matchedPatterns": classification_result.matched_patterns,
                "filterName": classification_result.filter_name,
                "processingTime": classification_result.processing_time,
                "timestamp": time.strftime('%Y-%m-%dT%H:%M:%S'),
                "studentVersion": student_version,  # 学生C版本
                "ruleVersion": rule_version         # 规则库版本（与shared_rule_library.json一致）
            }

            # 写回文件
            with open(json_file_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)

            self.logger.debug(f"✅ 结果已写回JSON文件: {json_file_path}")

        except Exception as e:
            self.logger.error(f"❌ 写回结果失败 {json_file_path}: {e}")
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        status = {
            "config_loaded": self.config_manager is not None,
            "filters_count": len(self.filters),
            "pdf_extractor_available": self.pdf_extractor is not None,
            "header_analyzer_available": self.header_analyzer is not None
        }
        
        if self.config_manager:
            status.update(self.config_manager.get_config_summary())
        
        if self.pdf_extractor:
            status.update(self.pdf_extractor.get_extraction_stats())
        
        return status

def test_refactored_student_c():
    """测试重构版本的Python学生C"""
    print("🧪 测试重构版本的Python学生C...")
    
    # 创建实例
    student_c = PythonStudentC()
    
    # 显示系统状态
    status = student_c.get_system_status()
    print(f"📊 系统状态: {status}")
    
    # 测试分类功能
    test_cases = [
        ("业绩预警公告", "公司预计2023年度净利润亏损5000万元"),
        ("重大合同签署", "公司与客户签署15亿元销售合同"),
        ("股东大会通知", "公司定于2024年3月15日召开年度股东大会")
    ]
    
    for title, content in test_cases:
        result = student_c.classify(title, content)
        print(f"📄 {title}: {result.result.label} (置信度: {result.confidence:.3f})")

if __name__ == "__main__":
    test_refactored_student_c()
