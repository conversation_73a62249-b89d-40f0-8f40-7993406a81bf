#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
负面案例数据预处理工具
将2600个负面描述文件整合成结构化的CSV格式
"""

import os
import re
import csv
import json
from pathlib import Path
from typing import Dict, List, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NegativeCasePreprocessor:
    def __init__(self, input_dir: str = "output/negative_descriptions", output_file: str = "negative_cases.csv"):
        self.input_dir = Path(input_dir)
        self.output_file = output_file
        self.processed_count = 0
        self.error_count = 0
        
    def parse_negative_file(self, file_path: Path) -> Optional[Dict]:
        """解析单个负面描述文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取关键信息
            case_data = {
                'file_name': file_path.stem,
                'stock_code': self._extract_field(content, '股票代码'),
                'title': self._extract_field(content, '标题'),
                'llm_rating': self._extract_field(content, 'LLM评级'),
                'confidence': self._extract_field(content, '置信度'),
                'summary': self._extract_field(content, '摘要'),
                'key_factors': self._extract_field(content, '关键因素'),
                'risk_factors': self._extract_field(content, '风险因素'),
                'key_figures': self._extract_key_figures(content),
                'reasoning': self._extract_field(content, 'reasoning'),
                'raw_text_snippet': self._extract_raw_text(content)
            }
            
            return case_data
            
        except Exception as e:
            logger.error(f"解析文件失败 {file_path}: {e}")
            self.error_count += 1
            return None
    
    def _extract_field(self, content: str, field_name: str) -> str:
        """提取指定字段的值"""
        patterns = {
            '股票代码': r'股票代码:\s*(.+)',
            '标题': r'标题:\s*(.+)',
            'LLM评级': r'LLM评级:\s*(.+)',
            '置信度': r'置信度:\s*([\d.]+)',
            '摘要': r'摘要:\s*(.+)',
            '关键因素': r'关键因素:\s*(.+)',
            '风险因素': r'风险因素:\s*(.+)',
            'reasoning': r'"reasoning":\s*"([^"]+)"'
        }
        
        if field_name in patterns:
            match = re.search(patterns[field_name], content, re.MULTILINE | re.DOTALL)
            if match:
                return match.group(1).strip()
        
        return ""
    
    def _extract_key_figures(self, content: str) -> str:
        """提取关键数据"""
        key_figures_section = re.search(r'关键数据:(.*?)(?=\n---|\n===|\Z)', content, re.DOTALL)
        if key_figures_section:
            return key_figures_section.group(1).strip()
        return ""
    
    def _extract_raw_text(self, content: str) -> str:
        """提取原始文本片段"""
        raw_text_section = re.search(r'原始文本片段:(.*?)(?=\n===|\Z)', content, re.DOTALL)
        if raw_text_section:
            text = raw_text_section.group(1).strip()
            # 限制长度，避免CSV文件过大
            return text[:1000] if len(text) > 1000 else text
        return ""
    
    def process_all_files(self) -> List[Dict]:
        """处理所有负面描述文件"""
        if not self.input_dir.exists():
            logger.error(f"输入目录不存在: {self.input_dir}")
            return []
        
        txt_files = list(self.input_dir.glob("*.txt"))
        logger.info(f"找到 {len(txt_files)} 个txt文件")
        
        cases = []
        for txt_file in txt_files:
            case_data = self.parse_negative_file(txt_file)
            if case_data:
                cases.append(case_data)
                self.processed_count += 1
                
                if self.processed_count % 100 == 0:
                    logger.info(f"已处理 {self.processed_count} 个文件...")
        
        logger.info(f"处理完成: 成功 {self.processed_count} 个, 失败 {self.error_count} 个")
        return cases
    
    def save_to_csv(self, cases: List[Dict]):
        """保存到CSV文件"""
        if not cases:
            logger.warning("没有数据可保存")
            return
        
        fieldnames = [
            'file_name', 'stock_code', 'title', 'llm_rating', 'confidence',
            'summary', 'key_factors', 'risk_factors', 'key_figures', 
            'reasoning', 'raw_text_snippet'
        ]
        
        with open(self.output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(cases)
        
        logger.info(f"数据已保存到 {self.output_file}")
    
    def generate_summary_report(self, cases: List[Dict]):
        """生成数据摘要报告"""
        if not cases:
            return
        
        # 统计信息
        total_cases = len(cases)
        stock_codes = set(case['stock_code'] for case in cases if case['stock_code'])
        llm_ratings = {}
        confidence_values = []
        
        for case in cases:
            # LLM评级统计
            rating = case['llm_rating']
            if rating:
                llm_ratings[rating] = llm_ratings.get(rating, 0) + 1
            
            # 置信度统计
            try:
                conf = float(case['confidence']) if case['confidence'] else 0
                confidence_values.append(conf)
            except ValueError:
                pass
        
        # 生成报告
        report = f"""
=== 负面案例数据摘要报告 ===
总案例数: {total_cases}
涉及股票数: {len(stock_codes)}
LLM评级分布: {dict(llm_ratings)}
置信度统计:
  平均值: {sum(confidence_values)/len(confidence_values):.2f if confidence_values else 0}
  最小值: {min(confidence_values) if confidence_values else 0}
  最大值: {max(confidence_values) if confidence_values else 0}

数据质量检查:
  有标题的案例: {sum(1 for case in cases if case['title'])}/{total_cases}
  有摘要的案例: {sum(1 for case in cases if case['summary'])}/{total_cases}
  有关键因素的案例: {sum(1 for case in cases if case['key_factors'])}/{total_cases}
  有推理过程的案例: {sum(1 for case in cases if case['reasoning'])}/{total_cases}
"""
        
        # 保存报告
        report_file = "negative_cases_summary_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"摘要报告已保存到 {report_file}")
        print(report)

def main():
    """主函数"""
    logger.info("开始负面案例数据预处理...")
    
    preprocessor = NegativeCasePreprocessor()
    
    # 处理所有文件
    cases = preprocessor.process_all_files()
    
    if cases:
        # 保存到CSV
        preprocessor.save_to_csv(cases)
        
        # 生成摘要报告
        preprocessor.generate_summary_report(cases)
        
        logger.info("数据预处理完成！")
        logger.info(f"输出文件: {preprocessor.output_file}")
        logger.info(f"处理文件数: {preprocessor.processed_count}")
    else:
        logger.error("没有成功处理任何文件")

if __name__ == "__main__":
    main()
