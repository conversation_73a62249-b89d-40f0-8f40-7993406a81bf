#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速版本比较
"""

import json

def main():
    print("📊 词库版本快速比较")
    print("="*50)
    
    # 加载v2.6版本
    try:
        with open('E:/LLMData/research_archive/01_raw_data/production_lexicon_20250827_195041.json', 'r', encoding='utf-8') as f:
            v26 = json.load(f)
        print('✅ v2.6版本加载成功')
        print(f'   版本: {v26.get("version", "unknown")}')
        
        v26_negative = set(v26.get('word_lists', {}).get('all_negative_words', []))
        v26_positive = set(v26.get('word_lists', {}).get('all_positive_words', []))
        v26_controversial = set(v26.get('word_lists', {}).get('controversial_words', []))
        
        print(f'   负面词汇: {len(v26_negative)} 个')
        print(f'   正面词汇: {len(v26_positive)} 个')
        print(f'   争议词汇: {len(v26_controversial)} 个')
        
    except Exception as e:
        print(f'❌ v2.6加载失败: {e}')
        return

    # 加载v2.7版本
    try:
        with open('E:/LLMData/research_archive/01_raw_data/production_lexicon.json', 'r', encoding='utf-8') as f:
            v27 = json.load(f)
        print('\n✅ v2.7版本加载成功')
        print(f'   版本: {v27.get("version", "unknown")}')
        
        v27_negative = set(v27.get('word_lists', {}).get('all_negative_words', []))
        v27_positive = set(v27.get('word_lists', {}).get('all_positive_words', []))
        v27_controversial = set(v27.get('word_lists', {}).get('controversial_words', []))
        
        print(f'   负面词汇: {len(v27_negative)} 个')
        print(f'   正面词汇: {len(v27_positive)} 个')
        print(f'   争议词汇: {len(v27_controversial)} 个')
        
    except Exception as e:
        print(f'❌ v2.7加载失败: {e}')
        return
    
    # 比较变化
    print('\n📊 详细变化分析:')
    print('='*50)
    
    # 负面词汇变化
    added_negative = v27_negative - v26_negative
    removed_negative = v26_negative - v27_negative
    
    print(f'\n🔴 负面词汇变化:')
    print(f'   v2.6: {len(v26_negative)} 个')
    print(f'   v2.7: {len(v27_negative)} 个')
    print(f'   变化: {len(v27_negative) - len(v26_negative):+d} 个')
    
    if added_negative:
        print(f'\n   ➕ 新增负面词汇 ({len(added_negative)} 个):')
        for word in sorted(added_negative):
            print(f'      - "{word}"')
    
    if removed_negative:
        print(f'\n   ➖ 删除负面词汇 ({len(removed_negative)} 个):')
        for word in sorted(removed_negative):
            print(f'      - "{word}"')
    
    # 争议词汇变化
    added_controversial = v27_controversial - v26_controversial
    removed_controversial = v26_controversial - v27_controversial
    
    print(f'\n⚠️ 争议词汇变化:')
    print(f'   v2.6: {len(v26_controversial)} 个')
    print(f'   v2.7: {len(v27_controversial)} 个')
    print(f'   变化: {len(v27_controversial) - len(v26_controversial):+d} 个')
    
    if added_controversial:
        print(f'\n   ➕ 新增争议词汇 ({len(added_controversial)} 个):')
        for word in sorted(added_controversial):
            print(f'      - "{word}"')
    
    if removed_controversial:
        print(f'\n   ➖ 删除争议词汇 ({len(removed_controversial)} 个):')
        for word in sorted(removed_controversial):
            print(f'      - "{word}"')
    
    # 正面词汇变化
    added_positive = v27_positive - v26_positive
    removed_positive = v26_positive - v27_positive
    
    print(f'\n🟢 正面词汇变化:')
    print(f'   v2.6: {len(v26_positive)} 个')
    print(f'   v2.7: {len(v27_positive)} 个')
    print(f'   变化: {len(v27_positive) - len(v26_positive):+d} 个')
    
    if added_positive:
        print(f'\n   ➕ 新增正面词汇 ({len(added_positive)} 个):')
        for word in sorted(added_positive)[:10]:  # 只显示前10个
            print(f'      - "{word}"')
        if len(added_positive) > 10:
            print(f'      ... 还有 {len(added_positive) - 10} 个')
    
    # 质量评估
    print(f'\n🎯 质量变化评估:')
    print('='*30)
    
    # 分析新增的负面词汇
    if added_negative:
        print(f'\n✅ 积极变化:')
        good_words = []
        questionable_words = []
        
        for word in added_negative:
            if word in ['处罚', '违规', '风险', '损失', '亏损', '退市', '立案', '调查']:
                good_words.append(word)
            elif word in ['减持', '下降', '降低'] or ',' in word:
                questionable_words.append(word)
        
        if good_words:
            print(f'   - 新增明确负面词汇: {good_words}')
        
        if questionable_words:
            print(f'\n⚠️ 需要关注的变化:')
            for word in questionable_words:
                if ',' in word:
                    print(f'   - 复合词汇 "{word}" 可能过于宽泛')
                else:
                    print(f'   - 争议词汇 "{word}" 可能在某些语境下误判')
    
    # 总体评估
    print(f'\n📈 总体影响预测:')
    if len(added_negative) > len(removed_negative):
        print(f'   - 负面召回率可能提升 (新增 {len(added_negative)} 个负面词)')
    
    if added_controversial:
        print(f'   - 争议词汇处理增强 (新增 {len(added_controversial)} 个争议词)')
    
    if any(',' in word for word in added_negative):
        print(f'   - ⚠️ 注意复合词汇可能导致误判')

if __name__ == "__main__":
    main()
