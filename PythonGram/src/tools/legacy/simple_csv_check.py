#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的CSV文件检查工具（不依赖pandas）
"""

import os
import csv

def check_csv_simple(csv_file="negative_cases.csv"):
    """简单检查CSV文件"""
    if not os.path.exists(csv_file):
        print(f"错误: 文件 {csv_file} 不存在")
        return
    
    print(f"正在检查文件: {csv_file}")
    print(f"文件大小: {os.path.getsize(csv_file) / (1024*1024):.2f} MB")
    
    try:
        # 读取前几行查看结构
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            
            # 读取标题行
            header = next(reader)
            print(f"\n=== 文件结构 ===")
            print(f"列数: {len(header)}")
            print(f"列名: {header}")
            
            # 读取前5行数据
            print(f"\n=== 前5行数据预览 ===")
            row_count = 0
            for i, row in enumerate(reader):
                if i >= 5:
                    break
                print(f"\n--- 第{i+1}行 ---")
                for j, value in enumerate(row):
                    col_name = header[j] if j < len(header) else f"列{j}"
                    # 限制显示长度
                    display_value = value[:100] if len(value) > 100 else value
                    if len(value) > 100:
                        display_value += "..."
                    print(f"  {col_name}: {display_value}")
                row_count += 1
            
            # 计算总行数
            print(f"\n=== 文件统计 ===")
            print(f"已显示行数: {row_count}")
            
            # 检查数据质量
            print(f"\n=== 数据质量检查 ===")
            
            # 重新打开文件进行完整统计
            with open(csv_file, 'r', encoding='utf-8') as f2:
                total_lines = sum(1 for line in f2)
            
            print(f"总行数: {total_lines - 1} (减去标题行)")
            
            # 检查列数一致性
            print(f"\n=== 列数一致性检查 ===")
            with open(csv_file, 'r', encoding='utf-8') as f3:
                reader = csv.reader(f3)
                next(reader)  # 跳过标题行
                
                inconsistent_rows = 0
                for i, row in enumerate(reader):
                    if len(row) != len(header):
                        if inconsistent_rows < 5:  # 只显示前5个不一致的行
                            print(f"  第{i+1}行列数不一致: 期望{len(header)}, 实际{len(row)}")
                        inconsistent_rows += 1
                
                if inconsistent_rows == 0:
                    print("  ✅ 所有行列数一致")
                else:
                    print(f"  ⚠️  发现 {inconsistent_rows} 行列数不一致")
            
            print("\n✅ CSV文件检查完成！")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_csv_simple()
