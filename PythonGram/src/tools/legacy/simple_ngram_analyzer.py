#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的N-gram分析工具（不依赖pandas）
从负面案例中提取高价值的负面信号词组
"""

import csv
import re
from collections import Counter, defaultdict
import os

class SimpleNgramAnalyzer:
    def __init__(self, csv_file="D:\\LLMData\\negative_cases.csv"):
        self.csv_file = csv_file
        self.negative_data = []
        self.stop_words = self._load_stop_words()
        
    def _load_stop_words(self):
        """加载停用词"""
        return {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',
            '与', '或', '及', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等', '等'
        }
    
    def load_data(self):
        """加载CSV数据"""
        if not os.path.exists(self.csv_file):
            print(f"错误: 文件 {self.csv_file} 不存在")
            return False
        
        print(f"正在加载数据: {self.csv_file}")
        
        try:
            with open(self.csv_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                header = next(reader)
                
                # 找到关键列的索引
                col_indices = {}
                for i, col in enumerate(header):
                    if col in ['title', 'summary', 'key_factors', 'reasoning']:
                        col_indices[col] = i
                
                print(f"找到关键列: {list(col_indices.keys())}")
                
                # 读取数据
                row_count = 0
                for row in reader:
                    if len(row) >= max(col_indices.values()):
                        data_item = {}
                        for col, idx in col_indices.items():
                            data_item[col] = row[idx] if idx < len(row) else ""
                        self.negative_data.append(data_item)
                        row_count += 1
                        
                        if row_count % 500 == 0:
                            print(f"已加载 {row_count} 行...")
                
                print(f"数据加载完成: {row_count} 行")
                return True
                
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def preprocess_text(self, text):
        """文本预处理"""
        if not text:
            return ""
        
        # 去除特殊字符，保留中文、英文、数字
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', str(text))
        # 去除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    def extract_ngrams(self, text, n=2):
        """提取N-gram（简单版本）"""
        if not text:
            return []
        
        # 简单的按空格和标点分割
        words = re.split(r'[\s,，。！？；：""''（）【】]+', text)
        # 过滤停用词和短词
        words = [w for w in words if w and w not in self.stop_words and len(w) > 1]
        
        # 生成N-gram
        ngrams = []
        for i in range(len(words) - n + 1):
            ngram = tuple(words[i:i+n])
            ngrams.append('_'.join(ngram))
        
        return ngrams
    
    def analyze_ngram_frequency(self, n=2):
        """分析N-gram频率"""
        print(f"开始分析 {n}-gram 频率...")
        
        ngram_counter = Counter()
        text_columns = ['title', 'summary', 'key_factors', 'reasoning']
        
        for i, item in enumerate(self.negative_data):
            for col in text_columns:
                if col in item and item[col]:
                    text = self.preprocess_text(item[col])
                    ngrams = self.extract_ngrams(text, n)
                    ngram_counter.update(ngrams)
            
            if (i + 1) % 500 == 0:
                print(f"已处理 {i + 1} 个案例...")
        
        print(f"{n}-gram 分析完成，共找到 {len(ngram_counter)} 个不同的 {n}-gram")
        return ngram_counter
    
    def find_top_patterns(self, n=2, top_k=100):
        """找出top K的模式"""
        ngram_counter = self.analyze_ngram_frequency(n)
        
        # 按频率排序
        top_patterns = ngram_counter.most_common(top_k)
        
        print(f"\n=== Top {top_k} {n}-gram 模式 ===")
        for i, (pattern, freq) in enumerate(top_patterns):
            print(f"{i+1:3d}. {pattern} (频率: {freq})")
        
        return top_patterns
    
    def categorize_patterns(self, bigram_patterns, trigram_patterns):
        """按主题分类模式"""
        print("\n=== 按主题分类模式 ===")
        
        categories = {
            '业绩类': ['业绩', '亏损', '下滑', '下降', '减少', '预警', '预亏', '下降', '减少'],
            '监管类': ['警示', '监管', '问询', '立案', '调查', '处罚', '函', '通知'],
            '合同类': ['合同', '协议', '终止', '纠纷', '违约', '解除', '变更'],
            '股东类': ['减持', '质押', '冻结', '拍卖', '转让', '变更'],
            '财务类': ['债务', '逾期', '违约', '担保', '诉讼', '赔偿'],
            '经营类': ['停产', '关闭', '破产', '重组', '清算', '风险']
        }
        
        categorized = defaultdict(list)
        uncategorized = []
        
        # 分类2-gram
        for pattern, freq in bigram_patterns:
            categorized_flag = False
            for category, keywords in categories.items():
                if any(keyword in pattern for keyword in keywords):
                    categorized[category].append((pattern, freq, '2-gram'))
                    categorized_flag = True
                    break
            if not categorized_flag:
                uncategorized.append((pattern, freq, '2-gram'))
        
        # 分类3-gram
        for pattern, freq in trigram_patterns:
            categorized_flag = False
            for category, keywords in categories.items():
                if any(keyword in pattern for keyword in keywords):
                    categorized[category].append((pattern, freq, '3-gram'))
                    categorized_flag = True
                    break
            if not categorized_flag:
                uncategorized.append((pattern, freq, '3-gram'))
        
        # 显示分类结果
        for category, patterns in categorized.items():
            print(f"\n【{category}】")
            for pattern, freq, ntype in patterns[:15]:  # 每个类别显示前15个
                print(f"  {pattern} (频率: {freq}, 类型: {ntype})")
        
        if uncategorized:
            print(f"\n【其他类】")
            for pattern, freq, ntype in uncategorized[:20]:  # 显示前20个
                print(f"  {pattern} (频率: {freq}, 类型: {ntype})")
        
        return categorized, uncategorized
    
    def save_results(self, bigram_patterns, trigram_patterns, categorized):
        """保存分析结果"""
        # 保存所有模式
        with open("ngram_analysis_results.txt", "w", encoding="utf-8") as f:
            f.write("=== N-gram 分析结果 ===\n\n")
            
            f.write("【2-gram Top 100】\n")
            for i, (pattern, freq) in enumerate(bigram_patterns[:100]):
                f.write(f"{i+1:3d}. {pattern} (频率: {freq})\n")
            
            f.write("\n【3-gram Top 100】\n")
            for i, (pattern, freq) in enumerate(trigram_patterns[:100]):
                f.write(f"{i+1:3d}. {pattern} (频率: {freq})\n")
            
            f.write("\n【按主题分类】\n")
            for category, patterns in categorized.items():
                f.write(f"\n{category}:\n")
                for pattern, freq, ntype in patterns:
                    f.write(f"  {pattern} (频率: {freq}, 类型: {ntype})\n")
        
        print(f"\n分析结果已保存到: ngram_analysis_results.txt")
    
    def run_analysis(self):
        """运行完整分析"""
        print("开始N-gram分析...")
        
        # 加载数据
        if not self.load_data():
            return
        
        # 分析2-gram
        print("\n" + "="*50)
        bigram_patterns = self.find_top_patterns(n=2, top_k=100)
        
        # 分析3-gram
        print("\n" + "="*50)
        trigram_patterns = self.find_top_patterns(n=3, top_k=100)
        
        # 分类模式
        print("\n" + "="*50)
        categorized, uncategorized = self.categorize_patterns(bigram_patterns, trigram_patterns)
        
        # 保存结果
        self.save_results(bigram_patterns, trigram_patterns, categorized)
        
        print("\n✅ N-gram分析完成！")

def main():
    analyzer = SimpleNgramAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
