#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速检查CSV文件质量的工具
"""

import pandas as pd
import os

def check_csv_quality(csv_file="negative_cases.csv"):
    """检查CSV文件质量"""
    if not os.path.exists(csv_file):
        print(f"错误: 文件 {csv_file} 不存在")
        return
    
    print(f"正在检查文件: {csv_file}")
    print(f"文件大小: {os.path.getsize(csv_file) / (1024*1024):.2f} MB")
    
    try:
        # 读取前几行查看结构
        df = pd.read_csv(csv_file, encoding='utf-8', nrows=5)
        
        print("\n=== 文件结构 ===")
        print(f"列数: {len(df.columns)}")
        print(f"列名: {list(df.columns)}")
        
        print("\n=== 前5行数据预览 ===")
        for i, row in df.iterrows():
            print(f"\n--- 第{i+1}行 ---")
            for col in df.columns:
                value = str(row[col])[:100]  # 限制显示长度
                if len(str(row[col])) > 100:
                    value += "..."
                print(f"  {col}: {value}")
        
        # 检查完整文件的行数
        print("\n=== 完整文件统计 ===")
        total_rows = sum(1 for line in open(csv_file, 'r', encoding='utf-8'))
        print(f"总行数: {total_rows - 1} (减去标题行)")
        
        # 检查每列的数据完整性
        print("\n=== 数据完整性检查 ===")
        df_sample = pd.read_csv(csv_file, encoding='utf-8', nrows=100)  # 读取100行样本
        
        for col in df_sample.columns:
            non_null_count = df_sample[col].notna().sum()
            empty_count = (df_sample[col] == '').sum()
            print(f"  {col}: 非空值={non_null_count}, 空字符串={empty_count}, 总样本=100")
        
        # 检查是否有明显的解析问题
        print("\n=== 潜在问题检查 ===")
        
        # 检查股票代码格式
        if 'stock_code' in df_sample.columns:
            stock_codes = df_sample['stock_code'].dropna()
            if not stock_codes.empty:
                print(f"  股票代码示例: {stock_codes.head(3).tolist()}")
        
        # 检查标题长度
        if 'title' in df_sample.columns:
            titles = df_sample['title'].dropna()
            if not titles.empty:
                avg_title_length = titles.str.len().mean()
                print(f"  标题平均长度: {avg_title_length:.1f} 字符")
        
        print("\n✅ CSV文件检查完成！")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_csv_quality()
