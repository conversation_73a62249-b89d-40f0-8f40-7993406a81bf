@echo off
chcp 65001 >nul
echo 🚀 开始编译和运行TermGainAnalyzer...

echo.
echo 📦 步骤1: 检查Maven是否安装...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Maven未安装或未配置PATH环境变量
    echo 请先安装Maven: https://maven.apache.org/download.cgi
    pause
    exit /b 1
)
echo ✅ Maven已安装

echo.
echo 🔨 步骤2: 编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)
echo ✅ 编译成功

echo.
echo 📋 步骤3: 打包项目...
mvn package
if %errorlevel% neq 0 (
    echo ❌ 打包失败
    pause
    exit /b 1
)
echo ✅ 打包成功

echo.
echo 🎯 步骤4: 运行分析工具...
echo 使用示例数据进行分析...
java -cp target/term-gain-analyzer-1.0.0.jar com.example.analysis.TermGainAnalyzer sample_llm_results.json term_analysis.xlsx

echo.
echo 🎉 分析完成！
echo 📊 请查看生成的Excel文件: term_analysis.xlsx
echo.
pause
