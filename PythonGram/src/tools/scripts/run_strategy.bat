@echo off
chcp 936 >nul
echo.
echo ========================================
echo    三步走策略执行脚本
echo    基于昨天讨论的融合策略
echo ========================================
echo.

cd /d "%~dp0"

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    echo 请先安装Python并确保在PATH中
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在检查必要文件...
if not exist "create_neutral_cases.py" (
    echo ❌ 缺少文件: create_neutral_cases.py
    pause
    exit /b 1
)

if not exist "backtester.py" (
    echo ❌ 缺少文件: backtester.py
    pause
    exit /b 1
)

if not exist "run_three_step_strategy.py" (
    echo ❌ 缺少文件: run_three_step_strategy.py
    pause
    exit /b 1
)

echo ✅ 所有必要文件检查通过
echo.

echo 开始执行三步走策略...
echo 1. 创建中性案例数据集
echo 2. 回测规则集性能  
echo 3. 生成最终策略建议
echo.

python run_three_step_strategy.py

if errorlevel 1 (
    echo.
    echo ❌ 策略执行失败
    echo 请检查错误信息并重试
    pause
    exit /b 1
)

echo.
echo 🎉 策略执行完成！
echo 请查看生成的文件：
echo   - neutral_cases.csv: 中性案例数据集
echo   - backtest_results.json: 回测结果
echo   - strategy_implementation_guide.md: 策略实施指南
echo.
echo 🚀 下一步：根据策略建议，在Java应用中实施规则引擎
echo.
pause
