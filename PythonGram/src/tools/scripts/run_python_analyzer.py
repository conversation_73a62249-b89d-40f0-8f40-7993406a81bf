#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python词条分析器运行脚本
快速测试和演示词条信息增益分析功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from term_gain_analyzer import TermGainAnalyzer

def main():
    """主函数"""
    print("🚀 Python词条信息增益分析器")
    print("=" * 50)
    
    # 检查示例数据文件
    sample_file = current_dir / "sample_llm_results.json"
    if not sample_file.exists():
        print(f"❌ 示例数据文件不存在: {sample_file}")
        print("请确保 sample_llm_results.json 文件存在")
        return
    
    # 设置输出文件
    output_file = current_dir / "term_analysis_python.xlsx"
    
    print(f"📁 输入文件: {sample_file}")
    print(f"📁 输出文件: {output_file}")
    print()
    
    try:
        # 创建分析器
        print("🔧 创建分析器...")
        analyzer = TermGainAnalyzer()
        
        # 加载LLM结果
        print("📥 加载LLM结果...")
        llm_results = analyzer.load_llm_results(str(sample_file))
        
        # 构建词条类别计数
        print("🔍 构建词条统计...")
        analyzer.build_term_category_count(llm_results)
        
        # 计算信息增益
        print("🧮 计算信息增益...")
        analyzer.compute_information_gain(llm_results)
        
        # 导出Excel
        print("📊 导出Excel报告...")
        analyzer.export_to_excel(str(output_file))
        
        # 显示Top词条
        print("🏆 显示Top词条...")
        analyzer.show_top_terms(10)
        
        # 生成正则表达式建议
        print("💡 生成正则表达式建议...")
        analyzer.save_regex_suggestions(str(output_file))
        
        print("\n🎉 分析完成！")
        print("📊 生成的文件：")
        print(f"   - {output_file}: 词条分析报告")
        print(f"   - {output_file.stem}_regex_suggestions.json: 正则表达式建议")
        
        # 显示一些统计信息
        print("\n📈 统计摘要：")
        print(f"   总词条数: {len(analyzer.term_frequencies)}")
        print(f"   平均信息增益: {sum(analyzer.info_gain_map.values()) / len(analyzer.info_gain_map):.4f}")
        
        # 显示各类别分布
        print("\n📊 类别分布：")
        for category in analyzer.categories:
            count = sum(1 for result in llm_results if result.get('ratingLevel') == category)
            percentage = (count * 100.0) / len(llm_results)
            print(f"   {category}: {count} ({percentage:.1f}%)")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n💡 使用建议：")
    print("1. 查看Excel报告，了解词条的分布情况")
    print("2. 重点关注信息增益高的词条")
    print("3. 根据正则表达式建议，优化您的规则库")
    print("4. 可以修改示例数据，测试不同场景")

if __name__ == "__main__":
    main()
