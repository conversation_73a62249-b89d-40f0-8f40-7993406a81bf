#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三步走策略执行脚本
基于昨天讨论的融合策略，执行完整的规则优化流程
"""

import os
import sys
import logging
import subprocess
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ThreeStepStrategyExecutor:
    def __init__(self, tools_dir: str = "D:\\LLMData\\classification_output"):
        self.tools_dir = Path(tools_dir)
        self.current_step = 0
        self.total_steps = 3
        
    def print_step_header(self, step: int, title: str) -> None:
        """打印步骤标题"""
        print(f"\n{'='*60}")
        print(f"步骤 {step}/{self.total_steps}: {title}")
        print(f"{'='*60}")
    
    def print_step_footer(self, step: int, success: bool) -> None:
        """打印步骤结果"""
        status = "✅ 完成" if success else "❌ 失败"
        print(f"\n步骤 {step}/{self.total_steps} {status}")
    
    def step1_create_neutral_cases(self) -> bool:
        """
        第一步：创建 neutral_cases.csv（反例数据集）
        """
        self.print_step_header(1, "创建中性案例数据集")
        
        try:
            # 检查是否有create_neutral_cases.py
            script_path = self.tools_dir / "create_neutral_cases.py"
            if not script_path.exists():
                logger.error(f"脚本不存在: {script_path}")
                return False
            
            # 检查是否有negative_cases.csv
            negative_csv = self.tools_dir / "negative_cases.csv"
            if not negative_csv.exists():
                # 尝试在上级目录查找
                parent_negative = self.tools_dir.parent / "negative_cases.csv"
                if parent_negative.exists():
                    logger.info(f"在上级目录找到negative_cases.csv: {parent_negative}")
                    # 复制到当前目录
                    import shutil
                    shutil.copy2(parent_negative, negative_csv)
                else:
                    logger.error("未找到negative_cases.csv文件")
                    return False
            
            # 执行脚本
            logger.info("执行中性案例提取脚本...")
            result = subprocess.run([
                sys.executable, str(script_path)
            ], cwd=self.tools_dir, capture_output=True, text=False)
            
            # 手动处理编码
            try:
                stdout = result.stdout.decode('utf-8', errors='ignore') if result.stdout else ""
                stderr = result.stderr.decode('utf-8', errors='ignore') if result.stderr else ""
            except UnicodeDecodeError:
                # 如果UTF-8失败，尝试其他编码
                try:
                    stdout = result.stdout.decode('gbk', errors='ignore') if result.stdout else ""
                    stderr = result.stderr.decode('gbk', errors='ignore') if result.stderr else ""
                except:
                    stdout = result.stdout.decode('latin1', errors='ignore') if result.stdout else ""
                    stderr = result.stderr.decode('latin1', errors='ignore') if result.stderr else ""
            
            if result.returncode == 0:
                logger.info("中性案例提取成功")
                print(stdout)
                return True
            else:
                logger.error(f"脚本执行失败: {stderr}")
                return False
                
        except Exception as e:
            logger.error(f"第一步执行失败: {e}")
            return False
    
    def step2_backtest_rules(self) -> bool:
        """
        第二步：回测规则并量化性能
        """
        self.print_step_header(2, "回测规则集性能")
        
        try:
            # 检查是否有backtester.py
            script_path = self.tools_dir / "backtester.py"
            if not script_path.exists():
                logger.error(f"脚本不存在: {script_path}")
                return False
            
            # 检查是否有neutral_cases.csv
            neutral_csv = self.tools_dir / "neutral_cases.csv"
            if not neutral_csv.exists():
                logger.error("未找到neutral_cases.csv文件，请先执行第一步")
                return False
            
            # 执行回测
            logger.info("执行规则回测...")
            result = subprocess.run([
                sys.executable, str(script_path)
            ], cwd=self.tools_dir, capture_output=True, text=False)
            
            # 手动处理编码
            try:
                stdout = result.stdout.decode('utf-8', errors='ignore') if result.stdout else ""
                stderr = result.stderr.decode('utf-8', errors='ignore') if result.stderr else ""
            except UnicodeDecodeError:
                # 如果UTF-8失败，尝试其他编码
                try:
                    stdout = result.stdout.decode('gbk', errors='ignore') if result.stdout else ""
                    stderr = result.stderr.decode('gbk', errors='ignore') if result.stderr else ""
                except:
                    stdout = result.stdout.decode('latin1', errors='ignore') if result.stdout else ""
                    stderr = result.stderr.decode('latin1', errors='ignore') if result.stderr else ""
            
            if result.returncode == 0:
                logger.info("规则回测成功")
                print(stdout)
                return True
            else:
                logger.error(f"回测执行失败: {stderr}")
                return False
                
        except Exception as e:
            logger.error(f"第二步执行失败: {e}")
            return False
    
    def step3_generate_strategy(self) -> bool:
        """
        第三步：根据回测结果，制定最终提交策略
        """
        self.print_step_header(3, "生成最终策略建议")
        
        try:
            # 检查回测结果文件
            backtest_results = self.tools_dir / "backtest_results.json"
            if not backtest_results.exists():
                logger.error("未找到回测结果文件，请先执行第二步")
                return False
            
            # 读取回测结果
            import json
            with open(backtest_results, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            # 生成策略建议
            self._generate_strategy_recommendations(results)
            
            # 创建策略实施指南
            self._create_strategy_implementation_guide(results)
            
            return True
            
        except Exception as e:
            logger.error(f"第三步执行失败: {e}")
            return False
    
    def _generate_strategy_recommendations(self, results: dict) -> None:
        """生成策略建议"""
        metrics = results.get('overall_metrics', {})
        precision = metrics.get('precision', 0)
        recall = metrics.get('recall', 0)
        
        print("\n=== 基于回测结果的策略建议 ===")
        
        # 精确率策略
        if precision > 0.95:
            print("🎯 高精确率策略 (>95%)")
            print("   - 规则集很少误判，可以直接标记匹配规则的公告为'负面'")
            print("   - 无需提交给LLM，大幅节省成本")
            print("   - 建议：实施方案1的高置信度规则")
            
        elif precision > 0.8:
            print("⚠️ 中精确率策略 (80-95%)")
            print("   - 规则能捕捉大部分负面事件，但也误伤了一些中性事件")
            print("   - 建议：标记为'疑似负面，待LLM确认'")
            print("   - 实施：方案1的中置信度规则 + LLM精加工")
            
        else:
            print("🔍 低精确率策略 (<80%)")
            print("   - 规则误判率较高，需要优化")
            print("   - 建议：回到ngram分析结果，寻找更精确的规则模式")
            print("   - 实施：方案2的二元过滤策略")
        
        # 召回率策略
        if recall < 0.8:
            print("\n📈 召回率优化建议 (<80%)")
            print("   - 规则遗漏了较多负面事件")
            print("   - 建议：增加更多规则模式，避免遗漏重要负面事件")
            print("   - 考虑：扩展ngram分析，发现新的负面信号模式")
        
        # 规则性能分析
        rule_perf = results.get('rule_performance', {})
        if rule_perf:
            print("\n🔍 各规则性能分析")
            for rule_name, perf in rule_perf.items():
                status = "✅" if perf['precision'] > 0.8 else "⚠️" if perf['precision'] > 0.6 else "❌"
                print(f"   {status} {rule_name}: 精确率 {perf['precision']:.1%}, 召回率 {perf['recall']:.1%}")
    
    def _create_strategy_implementation_guide(self, results: dict) -> None:
        """创建策略实施指南"""
        guide_content = f"""# 规则预过滤策略实施指南

## 回测结果摘要
- 总负面案例数: {results.get('performance_stats', {}).get('total_negatives', 0)}
- 总中性案例数: {results.get('performance_stats', {}).get('total_neutrals', 0)}
- 精确率: {results.get('overall_metrics', {}).get('precision', 0):.1%}
- 召回率: {results.get('overall_metrics', {}).get('recall', 0):.1%}
- F1分数: {results.get('overall_metrics', {}).get('f1_score', 0):.3f}

## 推荐策略

### 当前规则集性能评估
基于回测结果，您的规则集表现如下：

"""
        
        metrics = results.get('overall_metrics', {})
        precision = metrics.get('precision', 0)
        
        if precision > 0.95:
            guide_content += """### 🎯 高精确率策略 (推荐)
- **实施方式**: 直接使用规则预过滤，无需LLM确认
- **成本效益**: 极高，可节省90%+的LLM调用
- **风险**: 极低，误判率<5%
- **适用场景**: 生产环境，高可靠性要求

### 具体实施步骤
1. 在Java应用中集成规则引擎
2. 匹配规则的公告直接标记为"负面"
3. 不匹配规则的公告提交给LLM
4. 定期回测规则性能，持续优化

"""
        elif precision > 0.8:
            guide_content += """### ⚠️ 中精确率策略 (推荐)
- **实施方式**: 规则预过滤 + LLM精加工
- **成本效益**: 高，可节省60-80%的LLM调用
- **风险**: 低，误判率5-20%
- **适用场景**: 生产环境，平衡成本与准确性

### 具体实施步骤
1. 在Java应用中集成规则引擎
2. 匹配规则的公告标记为"疑似负面"
3. 所有"疑似负面"公告提交给LLM确认
4. 不匹配规则的公告也提交给LLM
5. 建立规则性能监控，持续优化

"""
        else:
            guide_content += """### 🔍 低精确率策略 (需要优化)
- **实施方式**: 二元过滤策略
- **成本效益**: 中等，可节省30-50%的LLM调用
- **风险**: 中等，误判率>20%
- **适用场景**: 开发测试环境，规则优化阶段

### 具体实施步骤
1. 使用现有的中性过滤器
2. 老系统中判断为中性的，排除预警信息后仍为中性 → 不提交
3. 其余均提交给LLM
4. 重点优化规则，提高精确率

"""
        
        guide_content += f"""
## 规则优化建议

### 高表现规则 (精确率>80%)
"""
        
        rule_perf = results.get('rule_performance', {})
        high_perf_rules = [(name, perf) for name, perf in rule_perf.items() if perf['precision'] > 0.8]
        for rule_name, perf in high_perf_rules:
            guide_content += f"- **{rule_name}**: 精确率 {perf['precision']:.1%}, 召回率 {perf['recall']:.1%}\n"
        
        guide_content += f"""
### 需要优化的规则 (精确率<80%)
"""
        
        low_perf_rules = [(name, perf) for name, perf in rule_perf.items() if perf['precision'] <= 0.8]
        for rule_name, perf in low_perf_rules:
            guide_content += f"- **{rule_name}**: 精确率 {perf['precision']:.1%}, 召回率 {perf['recall']:.1%}\n"
            guide_content += f"  - 建议: 优化正则表达式，减少误判\n"
        
        guide_content += f"""
## 实施时间表

### 第一阶段 (1-2周)
- 集成规则引擎到Java应用
- 实施推荐的策略
- 建立性能监控

### 第二阶段 (2-4周)
- 收集生产环境数据
- 分析规则性能
- 优化低表现规则

### 第三阶段 (持续)
- 定期回测规则性能
- 根据新数据调整规则
- 持续优化成本效益

## 风险控制

### 监控指标
- 规则命中率
- 误判率变化
- LLM调用成本
- 用户反馈

### 应急预案
- 规则性能下降时快速回退
- 保持LLM作为备选方案
- 建立人工审核机制

---
*本指南基于回测结果生成，建议根据实际情况调整实施策略*
"""
        
        # 保存指南
        guide_path = self.tools_dir / "strategy_implementation_guide.md"
        with open(guide_path, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        logger.info(f"策略实施指南已保存到: {guide_path}")
        print(f"\n📋 策略实施指南已生成: {guide_path}")
    
    def run_strategy(self) -> bool:
        """运行完整的三步走策略"""
        print("🚀 开始执行三步走策略")
        print("基于昨天讨论的融合策略：规则预过滤 + LLM精加工")
        
        success_count = 0
        
        # 执行第一步
        if self.step1_create_neutral_cases():
            success_count += 1
            self.print_step_footer(1, True)
        else:
            self.print_step_footer(1, False)
            return False
        
        # 执行第二步
        if self.step2_backtest_rules():
            success_count += 1
            self.print_step_footer(2, True)
        else:
            self.print_step_footer(2, False)
            return False
        
        # 执行第三步
        if self.step3_generate_strategy():
            success_count += 1
            self.print_step_footer(3, True)
        else:
            self.print_step_footer(3, False)
            return False
        
        # 总结
        print(f"\n🎉 三步走策略执行完成！")
        print(f"成功执行: {success_count}/{self.total_steps} 步")
        
        if success_count == self.total_steps:
            print("\n✅ 所有步骤执行成功！")
            print("📋 请查看生成的文件：")
            print("   - neutral_cases.csv: 中性案例数据集")
            print("   - backtest_results.json: 回测结果")
            print("   - strategy_implementation_guide.md: 策略实施指南")
            print("\n🚀 下一步：根据策略建议，在Java应用中实施规则引擎")
            return True
        else:
            print(f"\n❌ 部分步骤执行失败，请检查错误信息")
            return False

def main():
    """主函数"""
    # 获取脚本所在目录
    script_dir = Path(__file__).parent
    
    # 创建执行器
    executor = ThreeStepStrategyExecutor(tools_dir=script_dir)
    
    # 执行策略
    success = executor.run_strategy()
    
    if success:
        print("\n🎯 策略执行成功！您现在有了：")
        print("1. 客观的规则性能数据")
        print("2. 科学的策略建议")
        print("3. 详细的实施指南")
        print("\n💡 建议：根据回测结果选择最适合的策略，在Java应用中实施")
    else:
        print("\n❌ 策略执行失败，请检查错误信息并重试")
        sys.exit(1)

if __name__ == "__main__":
    main()
