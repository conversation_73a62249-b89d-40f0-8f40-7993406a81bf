// 自动生成的Java规则代码
// 基于共享规则库生成

import java.util.List;
import java.util.regex.Pattern;

public class AutoGeneratedRules {
    
    private static final List<Pattern> negativePatterns = List.of(
        // 监管处罚类
        Pattern.compile("收到.*证监会.*警示函"),
        Pattern.compile("被.*证监会.*立案调查"),

        // 业绩风险类
        Pattern.compile("业绩预亏.*公告"),
        Pattern.compile("净利润.*同比下降.*50%"),

    );
    
    private static final List<Pattern> positivePatterns = List.of(
        // 业绩增长类
        Pattern.compile("业绩预增.*公告"),
        Pattern.compile("净利润.*同比增长.*50%"),

    );
    
    private static final List<Pattern> neutralPatterns = List.of(
        // 例行公告类
        Pattern.compile("关于召开.*股东大会的通知"),
        Pattern.compile("关于聘任.*会计师事务所的公告"),

    );
}
