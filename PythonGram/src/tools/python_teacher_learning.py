#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python老师学习系统
从40,418份LLM分析结果中学习规则模式
实现: 词频集非中性 = excluded_negative + excluded_positive - definitely_neutral
"""

import json
import os
from pathlib import Path
from collections import Counter, defaultdict
import re
import datetime
import pandas as pd

class PythonTeacherLearning:
    def __init__(self, data_dir="D:/LLMData/analysis_output"):
        self.data_dir = Path(data_dir)
        self.word_frequencies = {
            'excluded_negative': Counter(),  # 差+劣
            'excluded_positive': Counter(),  # 好+优  
            'definitely_neutral': Counter(), # 中
            'all_words': Counter()
        }
        self.processed_count = 0
        self.rating_distribution = Counter()
        self.sample_data = []  # 保存样本数据用于人工审查
        
    def load_and_analyze_all_data(self):
        """加载并分析所有40,418份数据"""
        print("🔍 开始加载40,418份LLM分析结果...")
        
        json_files = list(self.data_dir.glob("*.json"))
        total_files = len(json_files)
        
        print(f"📊 找到 {total_files} 个JSON文件")
        
        for i, file_path in enumerate(json_files):
            if i % 5000 == 0:
                print(f"  处理进度: {i}/{total_files} ({i/total_files*100:.1f}%)")
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self._extract_words_from_data(data, file_path.stem)
                self.processed_count += 1
                
            except Exception as e:
                print(f"  ⚠️ 跳过文件 {file_path.name}: {e}")
        
        print(f"✅ 数据加载完成！处理了 {self.processed_count} 份文件")
        
    def _extract_words_from_data(self, data, file_id):
        """从单个数据中提取词汇"""
        if not isinstance(data, dict):
            return
            
        rating_level = data.get('ratingLevel', '')
        key_factors = data.get('keyFactors', [])
        sentiment_score = data.get('sentimentScore', 0)
        
        # 统计评级分布
        self.rating_distribution[rating_level] += 1
        
        # 保存样本数据（前1000个用于人工审查）
        if len(self.sample_data) < 1000:
            self.sample_data.append({
                'file_id': file_id,
                'rating_level': rating_level,
                'sentiment_score': sentiment_score,
                'key_factors': '; '.join(key_factors) if isinstance(key_factors, list) else str(key_factors),
                'factor_count': len(key_factors) if isinstance(key_factors, list) else 0
            })
        
        if not isinstance(key_factors, list):
            return
            
        # 提取所有词汇
        all_words = []
        for factor in key_factors:
            if isinstance(factor, str):
                # 简单分词：提取中文词汇和数字
                words = re.findall(r'[\u4e00-\u9fff]+|\d+(?:\.\d+)?', factor)
                all_words.extend(words)
        
        # 根据评级分类统计
        for word in all_words:
            self.word_frequencies['all_words'][word] += 1
            
            if rating_level in ['差', '劣']:
                self.word_frequencies['excluded_negative'][word] += 1
            elif rating_level in ['好', '优']:
                self.word_frequencies['excluded_positive'][word] += 1
            elif rating_level == '中':
                self.word_frequencies['definitely_neutral'][word] += 1
    
    def calculate_non_neutral_words(self, min_frequency=3):
        """计算非中性词汇集合"""
        print("\n🧮 计算非中性词汇集合...")
        
        # 执行集合运算: excluded_negative + excluded_positive - definitely_neutral
        non_neutral_words = Counter()
        word_analysis = []  # 用于Excel详细分析
        
        # 获取所有唯一词汇
        all_unique_words = set()
        all_unique_words.update(self.word_frequencies['excluded_negative'].keys())
        all_unique_words.update(self.word_frequencies['excluded_positive'].keys())
        all_unique_words.update(self.word_frequencies['definitely_neutral'].keys())
        
        for word in all_unique_words:
            neg_count = self.word_frequencies['excluded_negative'].get(word, 0)
            pos_count = self.word_frequencies['excluded_positive'].get(word, 0)
            neu_count = self.word_frequencies['definitely_neutral'].get(word, 0)
            total_count = self.word_frequencies['all_words'].get(word, 0)
            
            # 计算非中性分数
            non_neutral_score = neg_count + pos_count - neu_count
            
            # 计算各种比例
            neg_ratio = neg_count / total_count if total_count > 0 else 0
            pos_ratio = pos_count / total_count if total_count > 0 else 0
            neu_ratio = neu_count / total_count if total_count > 0 else 0
            
            # 保存详细分析数据
            word_analysis.append({
                '词汇': word,
                '总频次': total_count,
                '消极频次': neg_count,
                '积极频次': pos_count,
                '中性频次': neu_count,
                '非中性分数': non_neutral_score,
                '消极比例': f"{neg_ratio:.3f}",
                '积极比例': f"{pos_ratio:.3f}",
                '中性比例': f"{neu_ratio:.3f}",
                '是否非中性': '是' if non_neutral_score >= min_frequency else '否',
                '词汇长度': len(word),
                '包含数字': '是' if re.search(r'\d', word) else '否'
            })
            
            # 如果符合条件，加入非中性词汇
            if non_neutral_score >= min_frequency:
                non_neutral_words[word] = non_neutral_score
        
        self.word_analysis = word_analysis
        print(f"✅ 计算完成！找到 {len(non_neutral_words)} 个非中性关键词")
        return non_neutral_words
    
    def save_excel_analysis(self, non_neutral_words):
        """保存详细的Excel分析报告"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        excel_file = f"D:/LLMData/python_teacher_analysis_{timestamp}.xlsx"
        
        print(f"\n📊 生成Excel分析报告: {excel_file}")
        
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            
            # 1. 总体统计
            overview_data = {
                '指标': [
                    '处理文件总数', '消极评级数量', '积极评级数量', '中性评级数量', 
                    '未知评级数量', '总词汇数', '非中性词汇数', '平均每文件词汇数'
                ],
                '数值': [
                    self.processed_count,
                    self.rating_distribution.get('差', 0) + self.rating_distribution.get('劣', 0),
                    self.rating_distribution.get('好', 0) + self.rating_distribution.get('优', 0),
                    self.rating_distribution.get('中', 0),
                    sum(count for rating, count in self.rating_distribution.items() 
                        if rating not in ['差', '劣', '好', '优', '中']),
                    len(self.word_frequencies['all_words']),
                    len(non_neutral_words),
                    len(self.word_frequencies['all_words']) / self.processed_count if self.processed_count > 0 else 0
                ]
            }
            pd.DataFrame(overview_data).to_excel(writer, sheet_name='总体统计', index=False)
            
            # 2. 评级分布详情
            rating_data = []
            for rating, count in self.rating_distribution.most_common():
                percentage = count / self.processed_count * 100 if self.processed_count > 0 else 0
                rating_data.append({
                    '评级': rating,
                    '数量': count,
                    '百分比': f"{percentage:.2f}%"
                })
            pd.DataFrame(rating_data).to_excel(writer, sheet_name='评级分布', index=False)
            
            # 3. 词汇详细分析（按非中性分数排序）
            df_words = pd.DataFrame(self.word_analysis)
            df_words = df_words.sort_values('非中性分数', ascending=False)
            df_words.to_excel(writer, sheet_name='词汇详细分析', index=False)
            
            # 4. Top非中性词汇
            top_non_neutral = []
            for word, score in non_neutral_words.most_common(200):
                word_info = next((item for item in self.word_analysis if item['词汇'] == word), {})
                top_non_neutral.append({
                    '排名': len(top_non_neutral) + 1,
                    '词汇': word,
                    '非中性分数': score,
                    '总频次': word_info.get('总频次', 0),
                    '消极频次': word_info.get('消极频次', 0),
                    '积极频次': word_info.get('积极频次', 0),
                    '中性频次': word_info.get('中性频次', 0),
                    '词汇特征': self._analyze_word_features(word)
                })
            pd.DataFrame(top_non_neutral).to_excel(writer, sheet_name='Top非中性词汇', index=False)
            
            # 5. 样本数据（用于人工审查）
            df_samples = pd.DataFrame(self.sample_data)
            df_samples.to_excel(writer, sheet_name='样本数据审查', index=False)
            
            # 6. 词汇分类分析
            category_analysis = self._categorize_words(non_neutral_words)
            pd.DataFrame(category_analysis).to_excel(writer, sheet_name='词汇分类分析', index=False)
            
            # 7. 异常词汇检测
            anomaly_words = self._detect_anomaly_words()
            pd.DataFrame(anomaly_words).to_excel(writer, sheet_name='异常词汇检测', index=False)
        
        print(f"✅ Excel分析报告已保存: {excel_file}")
        return excel_file
    
    def _analyze_word_features(self, word):
        """分析词汇特征"""
        features = []
        if re.search(r'\d', word):
            features.append('含数字')
        if len(word) >= 5:
            features.append('长词汇')
        if any(keyword in word for keyword in ['亿', '万', '元', '股']):
            features.append('金融')
        if any(keyword in word for keyword in ['收购', '合并', '重组']):
            features.append('并购')
        if any(keyword in word for keyword in ['诉讼', '违规', '处罚']):
            features.append('法律')
        return '; '.join(features) if features else '普通'
    
    def _categorize_words(self, non_neutral_words):
        """词汇分类分析"""
        categories = {
            '金融数字': [],
            '业务操作': [],
            '法律风险': [],
            '公司治理': [],
            '其他': []
        }
        
        for word, score in non_neutral_words.most_common(100):
            if any(keyword in word for keyword in ['亿', '万', '元', '股', '资金', '投资']):
                categories['金融数字'].append({'词汇': word, '分数': score})
            elif any(keyword in word for keyword in ['收购', '合并', '重组', '业务', '项目']):
                categories['业务操作'].append({'词汇': word, '分数': score})
            elif any(keyword in word for keyword in ['诉讼', '违规', '处罚', '调查', '风险']):
                categories['法律风险'].append({'词汇': word, '分数': score})
            elif any(keyword in word for keyword in ['董事', '股东', '管理', '决议']):
                categories['公司治理'].append({'词汇': word, '分数': score})
            else:
                categories['其他'].append({'词汇': word, '分数': score})
        
        # 转换为DataFrame格式
        result = []
        for category, words in categories.items():
            for item in words:
                result.append({
                    '分类': category,
                    '词汇': item['词汇'],
                    '非中性分数': item['分数'],
                    '分类词汇数': len(words)
                })
        
        return result
    
    def _detect_anomaly_words(self):
        """检测异常词汇"""
        anomalies = []
        
        for word_info in self.word_analysis:
            word = word_info['词汇']
            total = word_info['总频次']
            neu_count = word_info['中性频次']
            
            # 检测各种异常情况
            issues = []
            
            # 1. 单字符词汇但频次很高
            if len(word) == 1 and total > 50:
                issues.append('单字符高频')
            
            # 2. 纯数字词汇
            if word.isdigit() and total > 20:
                issues.append('纯数字高频')
            
            # 3. 中性比例异常高但被标记为非中性
            neu_ratio = neu_count / total if total > 0 else 0
            if neu_ratio > 0.8 and word_info['是否非中性'] == '是':
                issues.append('中性比例过高')
            
            # 4. 词汇过长可能是句子片段
            if len(word) > 10:
                issues.append('词汇过长')
            
            if issues:
                anomalies.append({
                    '词汇': word,
                    '异常类型': '; '.join(issues),
                    '总频次': total,
                    '中性频次': neu_count,
                    '中性比例': f"{neu_ratio:.3f}",
                    '词汇长度': len(word)
                })
        
        return sorted(anomalies, key=lambda x: x['总频次'], reverse=True)
    
    def generate_java_regex_rules(self, non_neutral_words, top_n=200):
        """生成Java正则表达式规则"""
        print(f"\n🔧 生成Java正则表达式规则 (Top {top_n})...")
        
        # 选择最重要的词汇
        top_words = non_neutral_words.most_common(top_n)
        
        # 按类型分组
        financial_words = []
        business_words = []
        legal_words = []
        other_words = []
        
        for word, count in top_words:
            if any(keyword in word for keyword in ['亿', '万', '元', '股', '资金', '投资', '收益', '利润']):
                financial_words.append(word)
            elif any(keyword in word for keyword in ['收购', '合并', '重组', '业务', '项目', '合作']):
                business_words.append(word)
            elif any(keyword in word for keyword in ['诉讼', '仲裁', '违规', '处罚', '调查']):
                legal_words.append(word)
            else:
                other_words.append(word)
        
        # 生成正则表达式
        regex_rules = {
            "financial_keywords": self._create_regex_pattern(financial_words),
            "business_keywords": self._create_regex_pattern(business_words),
            "legal_keywords": self._create_regex_pattern(legal_words),
            "other_keywords": self._create_regex_pattern(other_words),
            "metadata": {
                "generated_time": datetime.datetime.now().isoformat(),
                "total_files_processed": self.processed_count,
                "total_unique_words": len(non_neutral_words),
                "top_words_selected": top_n,
                "word_counts": {
                    "financial": len(financial_words),
                    "business": len(business_words),
                    "legal": len(legal_words),
                    "other": len(other_words)
                }
            }
        }
        
        return regex_rules
    
    def _create_regex_pattern(self, words):
        """创建正则表达式模式"""
        if not words:
            return ""
        
        # 转义特殊字符并创建模式
        escaped_words = [re.escape(word) for word in words]
        pattern = "|".join(escaped_words)
        return f"({pattern})"

def main():
    print("🚀 Python老师学习系统启动！")
    print("📚 目标：从40,418份LLM结果中学习非中性识别规则")
    print("📊 重点：生成详细Excel报告供人工审查")
    
    teacher = PythonTeacherLearning()
    
    # 1. 加载所有数据
    teacher.load_and_analyze_all_data()
    
    # 2. 计算非中性词汇
    non_neutral_words = teacher.calculate_non_neutral_words(min_frequency=3)
    
    # 3. 生成详细Excel分析报告
    excel_file = teacher.save_excel_analysis(non_neutral_words)
    
    # 4. 生成Java规则
    regex_rules = teacher.generate_java_regex_rules(non_neutral_words, top_n=200)
    
    # 5. 保存Java规则
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    java_rules_file = f"D:/LLMData/shared_rule_library_{timestamp}.json"
    with open(java_rules_file, 'w', encoding='utf-8') as f:
        json.dump(regex_rules, f, ensure_ascii=False, indent=2)
    
    print(f"\n🎉 Python老师学习完成！")
    print(f"📊 Excel分析报告: {excel_file}")
    print(f"🔧 Java规则文件: {java_rules_file}")
    print(f"\n👁️ 请打开Excel文件进行人工审查，寻找规律和异常！")

if __name__ == "__main__":
    main()