#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM分析结果质量检查工具
检查 D:\LLMData\analysis_output\ 下的4万份数据
"""

import os
import json
import pandas as pd
from pathlib import Path
from collections import Counter, defaultdict
import random
import datetime

class LLMDataQualityChecker:
    def __init__(self, data_dir="D:/LLMData/analysis_output"):
        self.data_dir = Path(data_dir)
        self.results = []
        
    def scan_directory(self):
        """扫描目录，统计文件信息"""
        print("🔍 扫描数据目录...")
        
        json_files = list(self.data_dir.glob("**/*.json"))
        txt_files = list(self.data_dir.glob("**/*.txt"))
        csv_files = list(self.data_dir.glob("**/*.csv"))
        
        print(f"📁 目录: {self.data_dir}")
        print(f"📄 JSON文件: {len(json_files)} 个")
        print(f"📄 TXT文件: {len(txt_files)} 个") 
        print(f"📄 CSV文件: {len(csv_files)} 个")
        print(f"📊 总文件数: {len(json_files) + len(txt_files) + len(csv_files)}")
        
        return json_files, txt_files, csv_files
    
    def sample_and_analyze(self, files, sample_size=50):
        """随机抽样分析文件质量"""
        print(f"\n🎯 随机抽取 {min(sample_size, len(files))} 个文件进行质量分析...")
        
        if len(files) < sample_size:
            sample_files = files
        else:
            sample_files = random.sample(files, sample_size)
        
        analysis_results = {
            'valid_files': 0,
            'invalid_files': 0,
            'rating_levels': Counter(),
            'key_factors_stats': Counter(),
            'sentiment_scores': [],
            'file_sizes': [],
            'errors': [],
            'sample_data': []
        }
        
        for i, file_path in enumerate(sample_files):
            try:
                print(f"  检查 {i+1}/{len(sample_files)}: {file_path.name}")
                
                # 检查文件大小
                file_size = file_path.stat().st_size
                analysis_results['file_sizes'].append(file_size)
                
                if file_path.suffix == '.json':
                    self._analyze_json_file(file_path, analysis_results)
                elif file_path.suffix == '.txt':
                    self._analyze_txt_file(file_path, analysis_results)
                elif file_path.suffix == '.csv':
                    self._analyze_csv_file(file_path, analysis_results)
                    
                analysis_results['valid_files'] += 1
                
            except Exception as e:
                analysis_results['invalid_files'] += 1
                analysis_results['errors'].append(f"{file_path.name}: {str(e)}")
                print(f"    ❌ 错误: {str(e)}")
        
        return analysis_results
    
    def _analyze_json_file(self, file_path, results):
        """分析JSON文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        # 保存样本数据用于展示
        if len(results['sample_data']) < 3:
            results['sample_data'].append({
                'file': file_path.name,
                'data': data
            })
            
        # 检查是否是LLM分析结果格式
        if isinstance(data, dict):
            # 统计评级
            if 'ratingLevel' in data:
                results['rating_levels'][data['ratingLevel']] += 1
            
            # 统计关键因子
            if 'keyFactors' in data and isinstance(data['keyFactors'], list):
                for factor in data['keyFactors']:
                    results['key_factors_stats'][factor] += 1
            
            # 统计情感分数
            if 'sentimentScore' in data:
                try:
                    score = float(data['sentimentScore'])
                    results['sentiment_scores'].append(score)
                except:
                    pass
                    
        elif isinstance(data, list):
            for item in data:
                if isinstance(item, dict) and 'ratingLevel' in item:
                    results['rating_levels'][item['ratingLevel']] += 1
    
    def _analyze_txt_file(self, file_path, results):
        """分析TXT文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        results['key_factors_stats'][f'txt_length_{len(content)//1000}k'] += 1
    
    def _analyze_csv_file(self, file_path, results):
        """分析CSV文件"""
        df = pd.read_csv(file_path, encoding='utf-8', nrows=100)
        results['key_factors_stats'][f'csv_rows_{len(df)}'] += 1
        results['key_factors_stats'][f'csv_cols_{len(df.columns)}'] += 1
        
        # 如果是答券数据，特别标记
        if any(col in df.columns for col in ['ratingLevel', 'isNeutral', 'stockCode']):
            results['key_factors_stats']['potential_answer_sheet'] += 1
    
    def generate_report(self, analysis_results, file_type=""):
        """生成质量报告"""
        print(f"\n{'='*60}")
        print(f"📋 {file_type} 数据质量分析报告")
        print(f"{'='*60}")
        
        print(f"✅ 有效文件: {analysis_results['valid_files']}")
        print(f"❌ 无效文件: {analysis_results['invalid_files']}")
        
        if analysis_results['file_sizes']:
            avg_size = sum(analysis_results['file_sizes']) / len(analysis_results['file_sizes'])
            max_size = max(analysis_results['file_sizes'])
            min_size = min(analysis_results['file_sizes'])
            print(f"📏 文件大小: 平均 {avg_size:.0f} bytes, 最大 {max_size} bytes, 最小 {min_size} bytes")
        
        if analysis_results['rating_levels']:
            print(f"\n📊 评级分布:")
            total_ratings = sum(analysis_results['rating_levels'].values())
            for rating, count in analysis_results['rating_levels'].most_common():
                percentage = (count / total_ratings) * 100
                print(f"  {rating}: {count} 个 ({percentage:.1f}%)")
        
        if analysis_results['sentiment_scores']:
            scores = analysis_results['sentiment_scores']
            avg_score = sum(scores) / len(scores)
            print(f"\n💭 情感分数: 平均 {avg_score:.2f}, 范围 [{min(scores):.2f}, {max(scores):.2f}]")
        
        if analysis_results['key_factors_stats']:
            print(f"\n🔑 关键统计Top10:")
            for factor, count in analysis_results['key_factors_stats'].most_common(10):
                print(f"  {factor}: {count} 次")
        
        if analysis_results['errors']:
            print(f"\n⚠️ 错误信息 (前5个):")
            for error in analysis_results['errors'][:5]:
                print(f"  {error}")
        
        # 显示样本数据
        if analysis_results['sample_data']:
            print(f"\n📋 样本数据预览:")
            for i, sample in enumerate(analysis_results['sample_data'][:2]):
                print(f"  样本 {i+1} ({sample['file']}):")
                data = sample['data']
                if isinstance(data, dict):
                    for key in ['ratingLevel', 'sentimentScore', 'keyFactors'][:3]:
                        if key in data:
                            value = data[key]
                            if isinstance(value, list) and len(value) > 3:
                                value = value[:3] + ['...']
                            print(f"    {key}: {value}")
    
    def check_answer_sheets(self):
        """检查本地答券数据"""
        print(f"\n🎯 检查本地答券数据...")
        
        # 查找可能的答券文件
        answer_patterns = ["*答券*", "*answer*", "*golden*", "*标准*", "*neutral*", "*negative*"]
        answer_files = []
        
        for pattern in answer_patterns:
            answer_files.extend(self.data_dir.glob(pattern))
            answer_files.extend(self.data_dir.parent.glob(pattern))  # 也检查上级目录
        
        print(f"📋 找到疑似答券文件: {len(answer_files)} 个")
        for file in answer_files:
            size_mb = file.stat().st_size / 1024 / 1024
            print(f"  📄 {file.name} ({size_mb:.1f} MB)")
            
        return answer_files

def main():
    print(f"🚀 LLM数据质量检查开始 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    checker = LLMDataQualityChecker()
    
    # 扫描目录
    json_files, txt_files, csv_files = checker.scan_directory()
    
    # 检查答券数据
    answer_files = checker.check_answer_sheets()
    
    # 抽样分析主要数据
    if json_files:
        print("\n🔍 分析JSON文件...")
        json_results = checker.sample_and_analyze(json_files, 50)
        checker.generate_report(json_results, "JSON")
    
    if csv_files:
        print("\n🔍 分析CSV文件...")
        csv_results = checker.sample_and_analyze(csv_files, 20)
        checker.generate_report(csv_results, "CSV")
    
    if txt_files:
        print("\n🔍 分析TXT文件...")
        txt_results = checker.sample_and_analyze(txt_files, 10)
        checker.generate_report(txt_results, "TXT")
    
    # 总结建议
    print(f"\n{'='*60}")
    print("🎯 数据质量总结与建议")
    print(f"{'='*60}")
    
    total_files = len(json_files) + len(csv_files) + len(txt_files)
    print(f"📊 数据规模: {total_files} 个文件")
    
    if json_files:
        print(f"✅ JSON数据: {len(json_files)} 个文件，适合作为LLM分析结果")
    if answer_files:
        print(f"📋 答券数据: {len(answer_files)} 个文件，可用作训练标准")
    
    print(f"\n🚀 建议下一步:")
    print(f"  1. 重点分析JSON格式的LLM结果")
    print(f"  2. 验证答券数据的标注质量")
    print(f"  3. 开始实施Python老师学习算法")

if __name__ == "__main__":
    main()