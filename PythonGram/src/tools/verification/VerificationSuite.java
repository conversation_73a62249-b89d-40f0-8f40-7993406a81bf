package com.example.verification;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 回归测试套件 (Verification Suite)
 * 用于反向验证 term_gain_analyzer.py 和 backtester.py 的结果是否正确
 */
public class VerificationSuite {
    
    // 配置常量
    private static final String ROOT_DIR = "src/tools";
    private static final String VERIFICATION_DIR = "verification";
    private static final String GOLDEN_MASTER_DIR = "golden_master";
    private static final String TEMP_OUTPUT_DIR = "temp_outputs";
    
    // 颜色定义，用于美化输出
    private static final String ANSI_RESET = "\u001B[0m";
    private static final String ANSI_HEADER = "\u001B[95m";
    private static final String ANSI_OKBLUE = "\u001B[94m";
    private static final String ANSI_OKCYAN = "\u001B[96m";
    private static final String ANSI_OKGREEN = "\u001B[92m";
    private static final String ANSI_WARNING = "\u001B[93m";
    private static final String ANSI_FAIL = "\u001B[91m";
    private static final String ANSI_BOLD = "\u001B[1m";
    
    private boolean hasFailed = false;
    private Path tempOutputPath;
    private ObjectMapper objectMapper;
    
    public VerificationSuite() {
        this.objectMapper = new ObjectMapper();
        setupTempDirectory();
    }
    
    private void setupTempDirectory() {
        try {
            tempOutputPath = Paths.get(VERIFICATION_DIR, TEMP_OUTPUT_DIR);
            if (Files.exists(tempOutputPath)) {
                deleteDirectory(tempOutputPath);
            }
            Files.createDirectories(tempOutputPath);
        } catch (IOException e) {
            System.err.println("❌ 创建临时目录失败: " + e.getMessage());
            hasFailed = true;
        }
    }
    
    private void deleteDirectory(Path path) throws IOException {
        if (Files.isDirectory(path)) {
            try (DirectoryStream<Path> entries = Files.newDirectoryStream(path)) {
                for (Path entry : entries) {
                    deleteDirectory(entry);
                }
            }
        }
        Files.delete(path);
    }
    
    private void printHeader(String title) {
        System.out.println("\n" + ANSI_HEADER + "=".repeat(20) + " " + title + " " + "=".repeat(20) + ANSI_RESET);
    }
    
    private boolean runCommand(String... command) {
        try {
            System.out.println(ANSI_OKBLUE + "▶️  执行命令: " + String.join(" ", command) + ANSI_RESET);
            
            ProcessBuilder pb = new ProcessBuilder(command);
            pb.directory(new File(ROOT_DIR));
            pb.redirectErrorStream(true);
            
            Process process = pb.start();
            
            // 读取输出
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "UTF-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    // 可以选择是否打印详细输出
                    // System.out.println(line);
                }
            }
            
            // 等待进程完成，设置超时时间
            if (!process.waitFor(60, TimeUnit.SECONDS)) {
                process.destroyForcibly();
                System.out.println(ANSI_FAIL + "❌ 命令执行超时!" + ANSI_RESET);
                hasFailed = true;
                return false;
            }
            
            int exitCode = process.exitValue();
            if (exitCode == 0) {
                return true;
            } else {
                System.out.println(ANSI_FAIL + "❌ 命令执行失败! 返回码: " + exitCode + ANSI_RESET);
                hasFailed = true;
                return false;
            }
            
        } catch (Exception e) {
            System.out.println(ANSI_FAIL + "❌ 执行命令时发生错误: " + e.getMessage() + ANSI_RESET);
            hasFailed = true;
            return false;
        }
    }
    
    private boolean compareJsonFiles(Path file1, Path file2) {
        try {
            JsonNode data1 = objectMapper.readTree(file1.toFile());
            JsonNode data2 = objectMapper.readTree(file2.toFile());
            
            if (data1.equals(data2)) {
                System.out.println("  " + ANSI_OKGREEN + "✅ JSON内容一致: " + file1.getFileName() + ANSI_RESET);
                return true;
            } else {
                System.out.println("  " + ANSI_FAIL + "❌ JSON内容不一致: " + file1.getFileName() + ANSI_RESET);
                hasFailed = true;
                return false;
            }
        } catch (Exception e) {
            System.out.println("  " + ANSI_FAIL + "❌ 比较JSON文件时出错: " + e.getMessage() + ANSI_RESET);
            hasFailed = true;
            return false;
        }
    }
    
    private boolean compareExcelFiles(Path file1, Path file2) {
        try {
            // 读取两个Excel文件
            Map<String, List<Map<String, Object>>> data1 = readExcelFile(file1);
            Map<String, List<Map<String, Object>>> data2 = readExcelFile(file2);
            
            if (data1.equals(data2)) {
                System.out.println("  " + ANSI_OKGREEN + "✅ Excel数据一致: " + file1.getFileName() + ANSI_RESET);
                return true;
            } else {
                System.out.println("  " + ANSI_FAIL + "❌ Excel数据不一致: " + file1.getFileName() + ANSI_RESET);
                // 可以在这里添加更详细的对比输出
                hasFailed = true;
                return false;
            }
        } catch (Exception e) {
            System.out.println("  " + ANSI_FAIL + "❌ 比较Excel文件时出错: " + e.getMessage() + ANSI_RESET);
            hasFailed = true;
            return false;
        }
    }
    
    private Map<String, List<Map<String, Object>>> readExcelFile(Path filePath) throws IOException {
        Map<String, List<Map<String, Object>>> result = new HashMap<>();
        
        try (FileInputStream fis = new FileInputStream(filePath.toFile());
             Workbook workbook = new XSSFWorkbook(fis)) {
            
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                String sheetName = sheet.getSheetName();
                List<Map<String, Object>> sheetData = new ArrayList<>();
                
                // 读取表头
                Row headerRow = sheet.getRow(0);
                if (headerRow == null) continue;
                
                List<String> headers = new ArrayList<>();
                for (int j = 0; j < headerRow.getLastCellNum(); j++) {
                    Cell cell = headerRow.getCell(j);
                    headers.add(cell != null ? getCellValueAsString(cell) : "");
                }
                
                // 读取数据行
                for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
                    Row row = sheet.getRow(rowNum);
                    if (row == null) continue;
                    
                    Map<String, Object> rowData = new HashMap<>();
                    for (int colNum = 0; colNum < headers.size(); colNum++) {
                        Cell cell = row.getCell(colNum);
                        rowData.put(headers.get(colNum), getCellValueAsString(cell));
                    }
                    sheetData.add(rowData);
                }
                
                result.put(sheetName, sheetData);
            }
        }
        
        return result;
    }
    
    private String getCellValueAsString(Cell cell) {
        if (cell == null) return "";
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
    
    public void verifyTermAnalyzer() {
        printHeader("验证 term_gain_analyzer.py");
        
        // 定义输入和输出路径
        Path inputJson = Paths.get(VERIFICATION_DIR, GOLDEN_MASTER_DIR, "sample_llm_results.json");
        Path tempExcel = tempOutputPath.resolve("term_analysis.xlsx");
        
        // 执行Python脚本
        String[] command = {
            "python", 
            "term_gain_analyzer.py",
            inputJson.toString(),
            tempExcel.toString()
        };
        
        if (!runCommand(command)) {
            return;
        }
        
        // 对比生成的结果与黄金标准
        System.out.println(ANSI_OKCYAN + "🔍 开始对比生成文件..." + ANSI_RESET);
        Path expectedExcel = Paths.get(VERIFICATION_DIR, GOLDEN_MASTER_DIR, "expected_term_analysis.xlsx");
        Path expectedRegexJson = Paths.get(VERIFICATION_DIR, GOLDEN_MASTER_DIR, "expected_term_analysis_regex_suggestions.json");
        Path tempRegexJson = tempOutputPath.resolve("term_analysis_regex_suggestions.json");
        
        if (Files.exists(expectedExcel)) {
            compareExcelFiles(tempExcel, expectedExcel);
        } else {
            System.out.println(ANSI_WARNING + "⚠️  黄金标准Excel文件不存在，跳过对比" + ANSI_RESET);
        }
        
        if (Files.exists(expectedRegexJson)) {
            compareJsonFiles(tempRegexJson, expectedRegexJson);
        } else {
            System.out.println(ANSI_WARNING + "⚠️  黄金标准正则建议JSON文件不存在，跳过对比" + ANSI_RESET);
        }
    }
    
    public void verifyBacktester() {
        printHeader("验证 backtester.py");
        
        // 回测器依赖于多个文件，我们先将其拷贝到tools主目录
        try {
            Path rulesJson = Paths.get(VERIFICATION_DIR, GOLDEN_MASTER_DIR, "rules.json");
            Path negativeCases = Paths.get(VERIFICATION_DIR, GOLDEN_MASTER_DIR, "negative_cases.csv");
            Path neutralCases = Paths.get(VERIFICATION_DIR, GOLDEN_MASTER_DIR, "neutral_cases.csv");
            
            if (Files.exists(rulesJson)) {
                Files.copy(rulesJson, Paths.get(ROOT_DIR, "rules.json"), StandardCopyOption.REPLACE_EXISTING);
            }
            if (Files.exists(negativeCases)) {
                Files.copy(negativeCases, Paths.get(ROOT_DIR, "negative_cases.csv"), StandardCopyOption.REPLACE_EXISTING);
            }
            if (Files.exists(neutralCases)) {
                Files.copy(neutralCases, Paths.get(ROOT_DIR, "neutral_cases.csv"), StandardCopyOption.REPLACE_EXISTING);
            }
        } catch (IOException e) {
            System.out.println(ANSI_WARNING + "⚠️  复制依赖文件失败: " + e.getMessage() + ANSI_RESET);
        }
        
        // 执行Python脚本
        String[] command = {"python", "backtester.py"};
        if (!runCommand(command)) {
            cleanupBacktesterFiles();
            return;
        }
        
        // 对比结果
        System.out.println(ANSI_OKCYAN + "🔍 开始对比生成文件..." + ANSI_RESET);
        Path tempResultsJson = Paths.get(ROOT_DIR, "backtest_results.json");
        Path expectedResultsJson = Paths.get(VERIFICATION_DIR, GOLDEN_MASTER_DIR, "expected_backtest_results.json");
        
        if (Files.exists(expectedResultsJson)) {
            compareJsonFiles(tempResultsJson, expectedResultsJson);
        } else {
            System.out.println(ANSI_WARNING + "⚠️  黄金标准回测结果JSON文件不存在，跳过对比" + ANSI_RESET);
        }
        
        cleanupBacktesterFiles();
    }
    
    private void cleanupBacktesterFiles() {
        try {
            Files.deleteIfExists(Paths.get(ROOT_DIR, "rules.json"));
            Files.deleteIfExists(Paths.get(ROOT_DIR, "negative_cases.csv"));
            Files.deleteIfExists(Paths.get(ROOT_DIR, "neutral_cases.csv"));
            Files.deleteIfExists(Paths.get(ROOT_DIR, "backtest_results.json"));
        } catch (IOException e) {
            System.out.println(ANSI_WARNING + "⚠️  清理文件失败: " + e.getMessage() + ANSI_RESET);
        }
    }
    
    public void run() {
        System.out.println(ANSI_HEADER + ANSI_BOLD + "🚀 开始运行验证套件..." + ANSI_RESET);
        
        verifyTermAnalyzer();
        // verifyBacktester(); // 如果需要验证回测器，取消此行注释
        
        System.out.println("\n" + ANSI_HEADER + "=".repeat(54) + ANSI_RESET);
        if (hasFailed) {
            System.out.println(ANSI_FAIL + ANSI_BOLD + "❌ 验算失败！部分结果与'黄金标准'不符。请检查您的修改。" + ANSI_RESET);
            System.exit(1);
        } else {
            System.out.println(ANSI_OKGREEN + ANSI_BOLD + "✅ 验算通过！所有结果均与'黄金标准'一致。" + ANSI_RESET);
            System.exit(0);
        }
    }
    
    public static void main(String[] args) {
        VerificationSuite suite = new VerificationSuite();
        suite.run();
    }
}
