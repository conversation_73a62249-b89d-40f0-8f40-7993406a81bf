@echo off
chcp 65001 >nul
echo 🚀 开始编译和运行验证系统...

echo.
echo 📦 步骤1: 编译Java项目...
call mvn clean compile

if %ERRORLEVEL% neq 0 (
    echo ❌ 编译失败！
    pause
    exit /b 1
)

echo ✅ 编译成功！

echo.
echo 📦 步骤2: 打包项目...
call mvn package

if %ERRORLEVEL% neq 0 (
    echo ❌ 打包失败！
    pause
    exit /b 1
)

echo ✅ 打包成功！

echo.
echo 🚀 步骤3: 运行验证系统...
echo 注意: 确保已经准备好黄金标准数据文件
echo.

java -jar target/verification-suite-1.0.0.jar

echo.
echo 🎉 验证完成！
pause
