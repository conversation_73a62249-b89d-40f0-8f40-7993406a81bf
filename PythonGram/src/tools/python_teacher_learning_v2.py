#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python老师学习系统 V2 - 完整实现版

职责:
1. 遍历分析目录，加载JSON文件。
2. 根据LLM的评级对文件进行分类。
3. 使用JiebaProcessor提取每个文件中带权重的关键词。
4. 汇总所有词条在不同类别中的加权频次。
5. 调用InformationGainScorer计算每个词条的统计得分。
6. 调用SmartExcelGenerator生成最终的分析报告。
"""

import sys
import json
import logging
from pathlib import Path
import argparse
from collections import Counter
from tqdm import tqdm

# 添加core模块到路径
sys.path.append(str(Path(__file__).parent / "core"))

from smart_excel_generator import SmartExcelGenerator
from information_gain_scorer import InformationGainScorer
from jieba_processor import JiebaProcessor

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PythonTeacherLearningV2:
    def __init__(self, data_dir: str, output_dir: str, stopwords_path: str = None):
        self.data_dir = Path(data_dir)
        self.categories = ["positive", "negative", "neutral"]
        
        # 初始化各个组件
        self.excel_generator = SmartExcelGenerator(output_dir=output_dir)
        self.scorer = InformationGainScorer(categories=self.categories)
        self.processor = JiebaProcessor(stopwords_path=stopwords_path)

        # 初始化数据容器
        # word_doc_counts: 统计每个词在各类别的文档中出现的次数 (DF)
        self.word_doc_counts = {cat: Counter() for cat in self.categories}
        # word_term_frequencies: 统计每个词在各类别的总加权词频 (TF)
        self.word_term_frequencies = {cat: Counter() for cat in self.categories}
        self.rating_distribution = Counter()
        self.processed_count = 0

    def _get_category_from_rating(self, rating: str) -> str:
        """根据LLM的评级映射到我们的标准分类"""
        if rating in ['优', '好']:
            return 'positive'
        elif rating in ['差', '劣']:
            return 'negative'
        else: # '中' 或其他
            return 'neutral'

    def _get_full_text(self, data: dict) -> str:
        """从JSON数据中拼接所有相关文本列"""
        text_parts = []
        text_columns = ['title', 'summary', 'keyFactors', 'reasoning']
        for col in text_columns:
            content = data.get(col)
            if content:
                # 如果是列表，拼接起来
                if isinstance(content, list):
                    text_parts.append(" ".join(map(str, content)))
                else:
                    text_parts.append(str(content))
        return " ".join(text_parts)

    def load_and_analyze_all_data(self):
        """加载并分析所有JSON文件，填充词频统计"""
        logger.info(f"开始从目录 {self.data_dir} 加载和分析数据...")
        json_files = list(self.data_dir.glob("*.json"))
        if not json_files:
            logger.error("未找到任何JSON文件，请检查数据目录。")
            return

        for file_path in tqdm(json_files, desc="分析JSON文件"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                category = self._get_category_from_rating(data.get('ratingLevel'))
                self.rating_distribution[category] += 1
                self.processed_count += 1

                full_text = self._get_full_text(data)
                keywords_with_weights = self.processor.extract_weighted_keywords(full_text)

                # 为文档频率统计获取不重复的关键词
                unique_keywords_in_doc = {word for word, weight in keywords_with_weights}

                for word in unique_keywords_in_doc:
                    # 每篇文档只为一个词的文档频率+1
                    self.word_doc_counts[category][word] += 1

                for word, weight in keywords_with_weights:
                    self.word_term_frequencies[category][word] += int(weight * 100)

            except Exception as e:
                logger.warning(f"处理文件 {file_path.name} 失败: {e}")
                continue
        
    def run_complete_analysis(self):
        """运行完整分析流程"""
        print("🚀 Python老师学习系统 V2 启动！")
        
        # 1. 数据加载和处理
        self.load_and_analyze_all_data()
        if self.processed_count == 0:
            print("❌ 未处理任何有效数据，分析终止。")
            return
        
        # 2. 计算词汇评分
        word_scores_df = self.scorer.calculate_scores(
            doc_counts=self.word_doc_counts,
            term_freqs=self.word_term_frequencies,
            total_docs=self.processed_count,
            category_dist=self.rating_distribution
        )
        
        # 3. 生成Excel报告并返回DataFrame
        self.excel_generator.generate_analysis_report(
            word_scores_df, self.processed_count, self.rating_distribution
        )
        
        print(f"✅ 分析完成！")
        return word_scores_df

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Python Teacher Learning System V2 - Analyze keyword statistics from JSON files.")
    parser.add_argument(
        "--data_dir",
        type=str,
        default="E:/LLMData/analysis_output",
        help="Directory containing the input JSON files."
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="E:/LLMData/PythonGram/src/tools/output",
        help="Directory to save the output Excel report."
    )
    parser.add_argument(
        "--stopwords",
        type=str,
        default=None,
        help="Optional path to a custom stopwords file (one word per line)."
    )
    args = parser.parse_args()

    teacher = PythonTeacherLearningV2(data_dir=args.data_dir, output_dir=args.output_dir, stopwords_path=args.stopwords)
    teacher.run_complete_analysis()