#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键词集重叠分析器

职责:
1. 运行 `PythonTeacherLearningV2` 的完整分析流程，生成带有统计分数的词汇表。
2. 提取每个类别（正面、负面、中性）中卡方值最高的N个词，构成核心词集。
3. 计算这些核心词集之间的交集、并集和Jaccard相似度。
4. 打印分析报告，揭示特征的区分度和模糊性。
"""

import argparse
import pandas as pd
from pathlib import Path
from datetime import datetime
from python_teacher_learning_v2 import PythonTeacherLearningV2

def jaccard_similarity(set1, set2):
    """计算两个集合的Jaccard相似度"""
    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))
    return intersection / union if union != 0 else 0

def analyze_set_overlap(name1: str, set1: set, name2: str, set2: set):
    """分析并打印两个集合的重叠情况"""
    intersection = set1.intersection(set2)
    similarity = jaccard_similarity(set1, set2)
    
    print("-" * 50)
    print(f"分析: '{name1}' vs '{name2}'")
    print(f"  - '{name1}' 词集大小: {len(set1)}")
    print(f"  - '{name2}' 词集大小: {len(set2)}")
    print(f"  - 交集大小: {len(intersection)}")
    print(f"  - Jaccard 相似度: {similarity:.2%}")
    
    if intersection:
        # 只显示前10个重叠词作为示例
        print(f"  - 重叠词汇 (示例): {sorted(list(intersection))[:10]}")
    print("-" * 50)

def _adjust_excel_column_width(worksheet):
    """辅助方法：自动调整Excel工作表的列宽"""
    for column in worksheet.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                # 仅在值为字符串时计算长度
                if isinstance(cell.value, str) and len(cell.value) > max_length:
                    max_length = len(cell.value)
            except Exception:
                pass
        adjusted_width = min(max_length + 2, 50)  # 限制最大宽度为50
        worksheet.column_dimensions[column_letter].width = adjusted_width

def generate_overlap_report(neg_words: set, pos_words: set, neu_words: set, output_dir: str, top_n: int):
    """
    生成包含词集重叠分析结果的Excel报告。
    """
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_path = output_path / f"keyword_overlap_analysis_top{top_n}_{timestamp}.xlsx"

    # 1. 准备重叠分析摘要数据
    summary_data = []
    set_map = {"负面": neg_words, "正面": pos_words, "中性": neu_words}
    set_pairs = [("负面", "正面"), ("负面", "中性"), ("正面", "中性")]

    for name1, name2 in set_pairs:
        set1 = set_map[name1]
        set2 = set_map[name2]
        intersection = set1.intersection(set2)
        similarity = jaccard_similarity(set1, set2)
        summary_data.append({
            '对比组': f"{name1} vs {name2}",
            f'{name1}词集大小': len(set1),
            f'{name2}词集大小': len(set2),
            '交集大小': len(intersection),
            'Jaccard相似度': f"{similarity:.2%}",
            '重叠词汇示例': ", ".join(sorted(list(intersection))[:10]) if intersection else ""
        })
    summary_df = pd.DataFrame(summary_data)

    # 2. 准备每个词集的详细列表，用于并排比较
    full_report_df = pd.DataFrame({
        f'Top{top_n}_负面词': pd.Series(sorted(list(neg_words))),
        f'Top{top_n}_正面词': pd.Series(sorted(list(pos_words))),
        f'Top{top_n}_中性词': pd.Series(sorted(list(neu_words))),
    })

    # 3. 准备每个重叠集的详细列表
    neg_pos_df = pd.DataFrame(sorted(list(neg_words.intersection(pos_words))), columns=['负面_vs_正面_重叠词'])
    neg_neu_df = pd.DataFrame(sorted(list(neg_words.intersection(neu_words))), columns=['负面_vs_中性_重叠词'])
    pos_neu_df = pd.DataFrame(sorted(list(pos_words.intersection(neu_words))), columns=['正面_vs_中性_重叠词'])

    # 4. 写入Excel文件
    try:
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            summary_df.to_excel(writer, sheet_name='重叠分析摘要', index=False)
            full_report_df.to_excel(writer, sheet_name='各类别TopN词汇', index=False)
            neg_pos_df.to_excel(writer, sheet_name='负面_vs_正面_重叠词', index=False)
            neg_neu_df.to_excel(writer, sheet_name='负面_vs_中性_重叠词', index=False)
            pos_neu_df.to_excel(writer, sheet_name='正面_vs_中性_重叠词', index=False)

            # 自动调整所有工作表的列宽
            for sheet_name in writer.sheets:
                _adjust_excel_column_width(writer.sheets[sheet_name])

        print(f"\n✅ Excel重叠分析报告已成功导出到: {excel_path}")
    except Exception as e:
        print(f"❌ 导出Excel重叠分析报告失败: {e}")

def main():
    parser = argparse.ArgumentParser(description="Analyze keyword set overlap from the teacher learning system.")
    parser.add_argument(
        "--data_dir", type=str, default="E:/LLMData/analysis_output",
        help="Directory containing the input JSON files."
    )
    parser.add_argument(
        "--output_dir", type=str, default="E:/LLMData/PythonGram/src/tools/output",
        help="Directory to save the main Excel report."
    )
    parser.add_argument(
        "--stopwords", type=str, default=None,
        help="Optional path to a custom stopwords file."
    )
    parser.add_argument(
        "--top_n", type=int, default=500,
        help="Number of top keywords to include in each set for analysis."
    )
    args = parser.parse_args()

    # 1. 运行主分析流程
    print("步骤 1: 运行主分析流程以获取关键词评分...")
    teacher = PythonTeacherLearningV2(data_dir=args.data_dir, output_dir=args.output_dir, stopwords_path=args.stopwords)
    # 我们只需要评分的DataFrame，所以直接调用内部方法
    teacher.load_and_analyze_all_data()
    if teacher.processed_count == 0:
        print("❌ 未处理任何有效数据，分析终止。")
        return
        
    word_scores_df = teacher.scorer.calculate_scores(
        doc_counts=teacher.word_doc_counts,
        term_freqs=teacher.word_term_frequencies,
        total_docs=teacher.processed_count,
        category_dist=teacher.rating_distribution
    )
    print("主分析流程完成！\n")

    # 2. 提取核心词集并进行重叠分析
    print(f"步骤 2: 分析Top {args.top_n} 核心词集的重叠情况...")
    
    neg_words = set(word_scores_df.sort_values('卡方值_负向', ascending=False).head(args.top_n)['词条'])
    pos_words = set(word_scores_df.sort_values('卡方值_正向', ascending=False).head(args.top_n)['词条'])
    neu_words = set(word_scores_df.sort_values('卡方值_中性', ascending=False).head(args.top_n)['词条'])

    analyze_set_overlap("负面词集", neg_words, "正面词集", pos_words)
    analyze_set_overlap("负面词集", neg_words, "中性词集", neu_words)
    analyze_set_overlap("正面词集", pos_words, "中性词集", neu_words)
    
    # 3. 生成并保存Excel报告
    print(f"\n步骤 3: 生成并保存详细的Excel报告...")
    generate_overlap_report(neg_words, pos_words, neu_words, args.output_dir, args.top_n)

if __name__ == "__main__":
    main()