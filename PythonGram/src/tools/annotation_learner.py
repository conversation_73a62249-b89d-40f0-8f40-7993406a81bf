#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标注学习器
读取人工标注结果，分析标注模式，更新生产词库
"""

import json
import csv
import jieba
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Set
from collections import Counter, defaultdict

class AnnotationLearner:
    """标注学习器"""
    
    def __init__(self, lexicon_file: str = "production_lexicon.json"):
        """初始化学习器"""
        self.version = "3.4.0"
        self.lexicon_file = lexicon_file
        self.load_current_lexicon()
        
        print(f"🎓 标注学习器 v{self.version} 初始化完成")
    
    def load_current_lexicon(self):
        """加载当前生产词库"""
        try:
            with open(self.lexicon_file, 'r', encoding='utf-8') as f:
                self.current_lexicon = json.load(f)
            
            word_lists = self.current_lexicon.get("word_lists", {})
            self.current_negative = set(word_lists.get("all_negative_words", []))
            self.current_positive = set(word_lists.get("all_positive_words", []))
            self.current_controversial = set(word_lists.get("controversial_words", []))
            
            print(f"✅ 加载当前词库: v{self.current_lexicon.get('version', 'unknown')}")
            
        except Exception as e:
            print(f"⚠️ 词库加载失败，使用空词库: {e}")
            self.current_lexicon = {"word_lists": {}}
            self.current_negative = set()
            self.current_positive = set()
            self.current_controversial = set()
    
    def read_human_annotations(self, annotation_file: str) -> List[Dict]:
        """读取人工标注结果"""
        print(f"📖 读取人工标注文件: {annotation_file}")
        
        annotations = []
        
        try:
            with open(annotation_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f, delimiter='\t')
                
                for row in reader:
                    # 只处理有人工标注的数据
                    if row.get('人工Score') and row.get('人工等级'):
                        annotation = {
                            'filename': row['文件名'],
                            'summary': row['文摘'],
                            'machine_score': float(row['机器Score']) if row['机器Score'] else 0,
                            'machine_level': row['机器等级'],
                            'human_score': int(row['人工Score']),
                            'human_level': row['人工等级'],
                            'human_reason': row.get('人工理由', ''),
                            'quality_rating': int(row['质量评分']) if row.get('质量评分') else 3,
                            'keywords': row.get('关键词', '').split(',') if row.get('关键词') else [],
                            'controversial_words': row.get('争议词', '').split(',') if row.get('争议词') else []
                        }
                        annotations.append(annotation)
            
            print(f"✅ 读取完成，共 {len(annotations)} 条人工标注")
            return annotations
            
        except Exception as e:
            print(f"❌ 读取标注文件失败: {e}")
            return []
    
    def analyze_annotation_patterns(self, annotations: List[Dict]) -> Dict:
        """分析标注模式"""
        print(f"🔍 分析标注模式...")
        
        analysis = {
            'total_annotations': len(annotations),
            'agreement_analysis': self._analyze_agreement(annotations),
            'correction_patterns': self._analyze_corrections(annotations),
            'new_signal_words': self._discover_new_signals(annotations),
            'quality_analysis': self._analyze_quality(annotations),
            'controversial_analysis': self._analyze_controversial_words(annotations)
        }
        
        return analysis
    
    def _analyze_agreement(self, annotations: List[Dict]) -> Dict:
        """分析机器与人工标注的一致性"""
        agreements = {
            'score_agreement': 0,
            'level_agreement': 0,
            'score_differences': [],
            'level_mismatches': []
        }
        
        for ann in annotations:
            # 分数一致性（允许±1的误差）
            score_diff = abs(ann['machine_score'] - ann['human_score'])
            agreements['score_differences'].append(score_diff)
            if score_diff <= 1.0:
                agreements['score_agreement'] += 1
            
            # 等级一致性
            if ann['machine_level'] == ann['human_level']:
                agreements['level_agreement'] += 1
            else:
                agreements['level_mismatches'].append({
                    'filename': ann['filename'],
                    'machine': ann['machine_level'],
                    'human': ann['human_level'],
                    'reason': ann['human_reason']
                })
        
        total = len(annotations)
        agreements['score_agreement_rate'] = agreements['score_agreement'] / total
        agreements['level_agreement_rate'] = agreements['level_agreement'] / total
        agreements['avg_score_difference'] = sum(agreements['score_differences']) / total
        
        return agreements
    
    def _analyze_corrections(self, annotations: List[Dict]) -> Dict:
        """分析人工修正模式"""
        corrections = {
            'positive_corrections': [],  # 机器判负面，人工判正面
            'negative_corrections': [],  # 机器判正面，人工判负面
            'intensity_corrections': []  # 强度调整
        }
        
        for ann in annotations:
            machine_score = ann['machine_score']
            human_score = ann['human_score']
            
            if machine_score < 0 and human_score > 0:
                corrections['positive_corrections'].append(ann)
            elif machine_score > 0 and human_score < 0:
                corrections['negative_corrections'].append(ann)
            elif abs(machine_score - human_score) >= 1.5:
                corrections['intensity_corrections'].append(ann)
        
        return corrections
    
    def _discover_new_signals(self, annotations: List[Dict]) -> Dict:
        """发现新的信号词汇"""
        new_signals = {
            'new_negative_words': set(),
            'new_positive_words': set(),
            'new_controversial_words': set()
        }
        
        for ann in annotations:
            # 分析人工修正的案例
            if ann['machine_score'] >= 0 and ann['human_score'] < 0:
                # 机器判中性/正面，人工判负面 -> 可能有新的负面词
                words = self._extract_words_from_text(ann['summary'])
                for word in words:
                    if (word not in self.current_negative and 
                        word not in self.current_positive and
                        len(word) >= 2):
                        new_signals['new_negative_words'].add(word)
            
            elif ann['machine_score'] <= 0 and ann['human_score'] > 0:
                # 机器判中性/负面，人工判正面 -> 可能有新的正面词
                words = self._extract_words_from_text(ann['summary'])
                for word in words:
                    if (word not in self.current_negative and 
                        word not in self.current_positive and
                        len(word) >= 2):
                        new_signals['new_positive_words'].add(word)
        
        return new_signals
    
    def _extract_words_from_text(self, text: str) -> List[str]:
        """从文本中提取词汇"""
        words = jieba.cut(text)
        return [word for word in words if len(word) >= 2 and word.isalpha()]
    
    def _analyze_quality(self, annotations: List[Dict]) -> Dict:
        """分析标注质量"""
        quality_ratings = [ann['quality_rating'] for ann in annotations if ann['quality_rating']]
        
        return {
            'avg_quality': sum(quality_ratings) / len(quality_ratings) if quality_ratings else 0,
            'quality_distribution': Counter(quality_ratings),
            'high_quality_count': len([q for q in quality_ratings if q >= 4]),
            'low_quality_count': len([q for q in quality_ratings if q <= 2])
        }
    
    def _analyze_controversial_words(self, annotations: List[Dict]) -> Dict:
        """分析争议词汇的处理效果"""
        controversial_analysis = {
            'controversial_cases': [],
            'effectiveness': {}
        }
        
        for ann in annotations:
            if ann['controversial_words']:
                controversial_analysis['controversial_cases'].append({
                    'words': ann['controversial_words'],
                    'machine_level': ann['machine_level'],
                    'human_level': ann['human_level'],
                    'agreement': ann['machine_level'] == ann['human_level']
                })
        
        return controversial_analysis
    
    def update_lexicon(self, analysis: Dict) -> Dict:
        """基于分析结果更新词库"""
        print(f"🔄 更新生产词库...")
        
        # 创建新版本词库
        new_lexicon = self.current_lexicon.copy()
        
        # 更新版本号
        current_version = new_lexicon.get('version', '2.6.0')
        version_parts = current_version.split('.')
        patch_version = int(version_parts[2]) + 1
        new_version = f"{version_parts[0]}.{version_parts[1]}.{patch_version}"
        new_lexicon['version'] = new_version
        
        # 更新词汇列表
        word_lists = new_lexicon.setdefault('word_lists', {})
        
        # 添加新发现的词汇
        new_signals = analysis['new_signal_words']
        
        current_negative = set(word_lists.get('all_negative_words', []))
        current_positive = set(word_lists.get('all_positive_words', []))
        
        # 谨慎添加新词汇（需要多次确认）
        confirmed_new_negative = self._filter_high_confidence_words(
            new_signals['new_negative_words'], analysis['correction_patterns']['negative_corrections']
        )
        confirmed_new_positive = self._filter_high_confidence_words(
            new_signals['new_positive_words'], analysis['correction_patterns']['positive_corrections']
        )
        
        current_negative.update(confirmed_new_negative)
        current_positive.update(confirmed_new_positive)
        
        word_lists['all_negative_words'] = sorted(list(current_negative))
        word_lists['all_positive_words'] = sorted(list(current_positive))
        
        # 更新学习历史
        learning_history = new_lexicon.setdefault('learning_history', [])
        learning_history.append({
            'timestamp': datetime.now().isoformat(),
            'version': new_version,
            'annotations_processed': analysis['total_annotations'],
            'agreement_rate': analysis['agreement_analysis']['level_agreement_rate'],
            'new_negative_words': list(confirmed_new_negative),
            'new_positive_words': list(confirmed_new_positive),
            'avg_quality_rating': analysis['quality_analysis']['avg_quality']
        })
        
        return new_lexicon
    
    def _filter_high_confidence_words(self, candidate_words: Set[str], correction_cases: List[Dict]) -> Set[str]:
        """筛选高置信度的新词汇"""
        # 简单的筛选逻辑：出现在多个修正案例中的词汇
        word_counts = Counter()
        
        for case in correction_cases:
            words = self._extract_words_from_text(case['summary'])
            for word in words:
                if word in candidate_words:
                    word_counts[word] += 1
        
        # 只保留出现次数 >= 2 的词汇
        return {word for word, count in word_counts.items() if count >= 2}
    
    def save_updated_lexicon(self, updated_lexicon: Dict) -> str:
        """保存更新后的词库"""
        # 保存带时间戳的历史版本
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        history_file = f"production_lexicon_{timestamp}.json"
        
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(updated_lexicon, f, ensure_ascii=False, indent=2)
        
        # 更新当前版本
        with open(self.lexicon_file, 'w', encoding='utf-8') as f:
            json.dump(updated_lexicon, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 词库已更新: v{updated_lexicon['version']}")
        print(f"   当前版本: {self.lexicon_file}")
        print(f"   历史版本: {history_file}")
        
        return history_file

def main():
    """主函数"""
    print("🎓 标注学习器")
    print("="*50)
    
    # 创建学习器
    learner = AnnotationLearner()
    
    # 示例：处理标注文件
    annotation_file = input("请输入人工标注文件路径: ").strip()
    if not annotation_file:
        print("❌ 未提供标注文件路径")
        return
    
    # 读取标注
    annotations = learner.read_human_annotations(annotation_file)
    
    if annotations:
        # 分析模式
        analysis = learner.analyze_annotation_patterns(annotations)
        
        # 显示分析结果
        print(f"\n📊 分析结果:")
        print(f"   总标注数: {analysis['total_annotations']}")
        print(f"   等级一致率: {analysis['agreement_analysis']['level_agreement_rate']:.1%}")
        print(f"   平均质量评分: {analysis['quality_analysis']['avg_quality']:.1f}")
        print(f"   发现新负面词: {len(analysis['new_signal_words']['new_negative_words'])} 个")
        print(f"   发现新正面词: {len(analysis['new_signal_words']['new_positive_words'])} 个")
        
        # 询问是否更新词库
        update_confirm = input("\n是否更新生产词库? (y/N): ").strip().lower()
        if update_confirm == 'y':
            updated_lexicon = learner.update_lexicon(analysis)
            learner.save_updated_lexicon(updated_lexicon)
            print("🎉 词库更新完成！")

if __name__ == "__main__":
    main()
