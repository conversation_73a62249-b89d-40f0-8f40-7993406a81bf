package com.example.analysis;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.FileOutputStream;
import java.util.*;

/**
 * 词条信息增益分析器
 * 基于LLM分析结果，提取关键词条，计算信息增益，生成Excel报告
 */
public class TermGainAnalyzer {

    private static final List<String> CATEGORIES = Arrays.asList("优", "好", "中", "差", "劣");
    private static final ObjectMapper mapper = new ObjectMapper();

    /**
     * 主函数
     */
    public static void main(String[] args) throws Exception {
        String inputFile ;
        String outputFile;

        if (args.length < 2) {
//            System.out.println("用法: java TermGainAnalyzer <llm_json_file> <output_excel>");
//            System.out.println("示例: java TermGainAnalyzer llm_results.json term_analysis.xlsx");

            inputFile = "D:\\LLMData\\PythonGram\\src\\tools\\verification\\temp_outputs\\term_analysis_regex_suggestions.json";
             outputFile = "D:\\LLMData\\PythonGram\\src\\tools\\verification\\temp_outputs\\term_analysis.xlsx";
            return;
        }else {
            inputFile = args[0];
            outputFile = args[1];
        }

        System.out.println("🚀 开始分析LLM结果...");
        System.out.println("输入文件: " + inputFile);
        System.out.println("输出文件: " + outputFile);

        // 加载LLM结果
        List<LLMResult> llmResults = loadLLMResults(inputFile);
        System.out.println("✅ 加载了 " + llmResults.size() + " 条LLM分析结果");

        // 构建词条类别计数
        Map<String, Map<String, Integer>> termFrequencies = buildTermCategoryCount(llmResults);
        System.out.println("✅ 提取了 " + termFrequencies.size() + " 个词条");

        // 计算信息增益
        Map<String, Double> infoGainMap = computeInformationGain(termFrequencies, llmResults);
        System.out.println("✅ 计算了所有词条的信息增益");

        // 导出Excel
        exportToExcel(outputFile, termFrequencies, infoGainMap);
        System.out.println("✅ Excel报告已导出到: " + outputFile);

        // 显示Top词条
        showTopTerms(infoGainMap, 10);
    }

    /**
     * 加载LLM结果JSON文件
     */
    private static List<LLMResult> loadLLMResults(String filePath) throws Exception {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new RuntimeException("文件不存在: " + filePath);
        }

        return mapper.readValue(file, new TypeReference<List<LLMResult>>() {});
    }

    /**
     * 构建词条在各类别中的出现次数统计
     */
    private static Map<String, Map<String, Integer>> buildTermCategoryCount(List<LLMResult> llmResults) {
        Map<String, Map<String, Integer>> termFrequencies = new HashMap<>();

        for (LLMResult result : llmResults) {
            String level = result.getRatingLevel();
            
            // 处理keyFactors
            if (result.getKeyFactors() != null) {
                for (String factor : result.getKeyFactors()) {
                    addTermCount(termFrequencies, factor, level);
                }
            }

            // 处理reasoning（如果有的话）
            if (result.getReasoning() != null && !result.getReasoning().isEmpty()) {
                // 简单的分词处理，后续可以集成jieba等专业分词工具
                String[] words = result.getReasoning().split("[\\s\\p{Punct}]+");
                for (String word : words) {
                    if (word.length() > 1) { // 过滤单字符
                        addTermCount(termFrequencies, word, level);
                    }
                }
            }
        }

        return termFrequencies;
    }

    /**
     * 添加词条计数
     */
    private static void addTermCount(Map<String, Map<String, Integer>> termFrequencies, 
                                   String term, String category) {
        termFrequencies.putIfAbsent(term, new HashMap<>());
        Map<String, Integer> categoryCount = termFrequencies.get(term);
        categoryCount.put(category, categoryCount.getOrDefault(category, 0) + 1);
    }

    /**
     * 计算信息增益
     */
    private static Map<String, Double> computeInformationGain(Map<String, Map<String, Integer>> termFrequencies,
                                                             List<LLMResult> llmResults) {
        Map<String, Double> infoGainMap = new HashMap<>();

        // 计算总体类别分布
        Map<String, Integer> totalCategoryCounts = computeOverallCategoryCount(llmResults);
        int totalDocs = llmResults.size();
        double totalEntropy = entropy(totalCategoryCounts, totalDocs);

        System.out.println("📊 总体类别分布:");
        for (Map.Entry<String, Integer> entry : totalCategoryCounts.entrySet()) {
            double percentage = (entry.getValue() * 100.0) / totalDocs;
            System.out.printf("  %s: %d (%.1f%%)%n", entry.getKey(), entry.getValue(), percentage);
        }
        System.out.printf("总体熵: %.4f%n", totalEntropy);

        // 计算每个词条的信息增益
        for (Map.Entry<String, Map<String, Integer>> entry : termFrequencies.entrySet()) {
            String term = entry.getKey();
            Map<String, Integer> counts = entry.getValue();

            int termCount = counts.values().stream().mapToInt(Integer::intValue).sum();
            int nonTermCount = totalDocs - termCount;

            if (termCount == 0 || nonTermCount == 0) {
                infoGainMap.put(term, 0.0);
                continue;
            }

            // 词条出现时的类别分布
            double entropyWithTerm = entropy(counts, termCount);

            // 词条未出现时的类别分布
            Map<String, Integer> nonTermCounts = new HashMap<>(totalCategoryCounts);
            for (Map.Entry<String, Integer> e : counts.entrySet()) {
                nonTermCounts.merge(e.getKey(), -e.getValue(), Integer::sum);
            }
            double entropyWithoutTerm = entropy(nonTermCounts, nonTermCount);

            // 条件熵
            double conditionalEntropy =
                    ((double) termCount / totalDocs) * entropyWithTerm +
                    ((double) nonTermCount / totalDocs) * entropyWithoutTerm;

            // 信息增益
            double infoGain = totalEntropy - conditionalEntropy;
            infoGainMap.put(term, infoGain);
        }

        return infoGainMap;
    }

    /**
     * 计算总体类别分布
     */
    private static Map<String, Integer> computeOverallCategoryCount(List<LLMResult> llmResults) {
        Map<String, Integer> overall = new HashMap<>();
        for (LLMResult result : llmResults) {
            String level = result.getRatingLevel();
            overall.put(level, overall.getOrDefault(level, 0) + 1);
        }
        return overall;
    }

    /**
     * 计算熵
     */
    private static double entropy(Map<String, Integer> counts, int total) {
        if (total == 0) return 0.0;
        double entropy = 0.0;
        for (int count : counts.values()) {
            if (count > 0) {
                double p = (double) count / total;
                entropy -= p * (Math.log(p) / Math.log(2));
            }
        }
        return entropy;
    }

    /**
     * 导出Excel报告
     */
    private static void exportToExcel(String filePath,
                                     Map<String, Map<String, Integer>> termFrequencies,
                                     Map<String, Double> infoGainMap) throws Exception {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("词条分析报告");

        // 创建标题样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);

        // 创建数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);

        // 创建百分比样式
        CellStyle percentStyle = workbook.createCellStyle();
        percentStyle.setAlignment(HorizontalAlignment.CENTER);
        DataFormat percentFormat = workbook.createDataFormat();
        percentStyle.setDataFormat(percentFormat.getFormat("0.00%"));

        // 表头
        Row header = sheet.createRow(0);
        int col = 0;
        header.createCell(col++).setCellValue("词条");
        header.createCell(col++).setCellValue("总出现次数");
        
        for (String cat : CATEGORIES) {
            header.createCell(col++).setCellValue(cat + "次数");
            header.createCell(col++).setCellValue(cat + "占比");
        }
        
        header.createCell(col).setCellValue("信息增益");

        // 应用表头样式
        for (int i = 0; i < col + 1; i++) {
            Cell cell = header.getCell(i);
            cell.setCellStyle(headerStyle);
        }

        // 数据行
        int rowIdx = 1;
        for (String term : termFrequencies.keySet()) {
            Row row = sheet.createRow(rowIdx++);
            col = 0;
            
            row.createCell(col++).setCellValue(term);

            Map<String, Integer> counts = termFrequencies.get(term);
            int termTotal = counts.values().stream().mapToInt(Integer::intValue).sum();
            row.createCell(col++).setCellValue(termTotal);

            for (String cat : CATEGORIES) {
                int count = counts.getOrDefault(cat, 0);
                row.createCell(col++).setCellValue(count);
                
                double freq = termTotal == 0 ? 0.0 : (double) count / termTotal;
                Cell percentCell = row.createCell(col++);
                percentCell.setCellValue(freq);
                percentCell.setCellStyle(percentStyle);
            }

            double infoGain = infoGainMap.getOrDefault(term, 0.0);
            Cell igCell = row.createCell(col);
            igCell.setCellValue(infoGain);
            igCell.setCellStyle(dataStyle);
        }

        // 自动调整列宽
        for (int i = 0; i < CATEGORIES.size() * 2 + 3; i++) {
            sheet.autoSizeColumn(i);
        }

        // 保存文件
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            workbook.write(fos);
        }
        workbook.close();

        System.out.println("📊 Excel报告包含以下列:");
        System.out.println("  - 词条名称");
        System.out.println("  - 总出现次数");
        for (String cat : CATEGORIES) {
            System.out.println("  - " + cat + "次数和占比");
        }
        System.out.println("  - 信息增益");
    }

    /**
     * 显示信息增益最高的词条
     */
    private static void showTopTerms(Map<String, Double> infoGainMap, int topN) {
        System.out.println("\n🏆 信息增益最高的 " + topN + " 个词条:");
        System.out.println("=".repeat(60));
        System.out.printf("%-15s %-12s %s%n", "词条", "信息增益", "区分能力");
        System.out.println("=".repeat(60));

        infoGainMap.entrySet().stream()
                .sorted((a, b) -> Double.compare(b.getValue(), a.getValue()))
                .limit(topN)
                .forEach(entry -> {
                    String term = entry.getKey();
                    double ig = entry.getValue();
                    String ability = getAbilityDescription(ig);
                    System.out.printf("%-15s %-12.4f %s%n", term, ig, ability);
                });
    }

    /**
     * 根据信息增益值描述区分能力
     */
    private static String getAbilityDescription(double infoGain) {
        if (infoGain > 0.5) return "🔥 极强";
        if (infoGain > 0.3) return "⭐ 很强";
        if (infoGain > 0.1) return "✅ 较强";
        if (infoGain > 0.05) return "⚠️ 一般";
        return "❌ 较弱";
    }

    /**
     * LLM结果数据类
     */
    public static class LLMResult {
        private String id;
        private String ratingLevel;
        private List<String> keyFactors;
        private String reasoning;

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        
        public String getRatingLevel() { return ratingLevel; }
        public void setRatingLevel(String ratingLevel) { this.ratingLevel = ratingLevel; }
        
        public List<String> getKeyFactors() { return keyFactors; }
        public void setKeyFactors(List<String> keyFactors) { this.keyFactors = keyFactors; }
        
        public String getReasoning() { return reasoning; }
        public void setReasoning(String reasoning) { this.reasoning = reasoning; }
    }
}
