#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel标注文件生成器
读取原始标注文件，进行机器标注，输出Excel友好格式
"""

import re
import csv
from pathlib import Path
from datetime import datetime
from typing import List, Dict
from enhanced_machine_annotator import EnhancedMachineAnnotator

class ExcelAnnotationGenerator:
    """Excel标注文件生成器"""
    
    def __init__(self, lexicon_file: str = "production_lexicon.json"):
        """初始化生成器"""
        self.version = "3.4.0"
        self.annotator = EnhancedMachineAnnotator(lexicon_file)
        print(f"📊 Excel标注文件生成器 v{self.version} 初始化完成")
    
    def parse_original_annotation_file(self, file_path: str) -> List[Dict]:
        """解析原始标注文件V1.txt"""
        print(f"📖 解析原始标注文件: {file_path}")
        
        data_list = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 跳过表头，查找数据行
            data_started = False
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 检测表头
                if "文件名" in line and "Score" in line and "文摘" in line:
                    data_started = True
                    continue
                
                if not data_started:
                    continue
                
                # 解析数据行
                parsed_data = self._parse_data_line(line)
                if parsed_data:
                    data_list.append(parsed_data)
            
            print(f"✅ 解析完成，共 {len(data_list)} 条数据")
            return data_list
            
        except Exception as e:
            print(f"❌ 解析文件失败: {e}")
            return []
    
    def _parse_data_line(self, line: str) -> Dict:
        """解析单行数据"""
        try:
            # 使用制表符或多个空格分割
            parts = re.split(r'\t+|\s{2,}', line)
            
            if len(parts) >= 3:
                filename = parts[0].strip()
                original_score = parts[1].strip()
                summary = parts[2].strip()
                
                # 验证文件名格式
                if filename.endswith(('.PDF', '.pdf', '.json')):
                    return {
                        "filename": filename,
                        "original_score": original_score,
                        "summary": summary
                    }
            
            return None
            
        except Exception as e:
            print(f"⚠️ 解析行失败: {line[:50]}... - {e}")
            return None
    
    def generate_excel_annotation_file(self, input_file: str, output_file: str = None) -> str:
        """生成Excel标注文件"""
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"标注文件V2_{timestamp}.txt"
        
        print(f"🏷️ 开始生成Excel标注文件...")
        print(f"   输入: {input_file}")
        print(f"   输出: {output_file}")
        
        # 解析原始文件
        data_list = self.parse_original_annotation_file(input_file)
        
        if not data_list:
            print("❌ 没有有效数据，生成失败")
            return None
        
        # 生成机器标注
        annotated_data = []
        for i, data in enumerate(data_list):
            print(f"   处理进度: {i+1}/{len(data_list)} - {data['filename']}")
            
            # 机器标注
            machine_result = self.annotator.annotate_summary(data['summary'])
            
            # 合并数据
            annotated_item = {
                **data,
                **machine_result
            }
            annotated_data.append(annotated_item)
        
        # 写入Excel友好格式
        self._write_excel_format(annotated_data, output_file)
        
        print(f"✅ Excel标注文件生成完成: {output_file}")
        return output_file
    
    def _write_excel_format(self, data_list: List[Dict], output_file: str):
        """写入Excel友好格式"""
        
        # 定义表头
        headers = [
            "文件名", "原始Score", "文摘",
            "机器Score", "机器等级", "关键词", "争议词", "主题", "置信度", "机器理由",
            "人工Score", "人工等级", "人工理由", "质量评分", "备注"
        ]
        
        with open(output_file, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f, delimiter='\t', quoting=csv.QUOTE_MINIMAL)
            
            # 写入表头
            writer.writerow(headers)
            
            # 写入数据
            for data in data_list:
                row = [
                    data['filename'],
                    data['original_score'],
                    data['summary'],
                    data['score'],
                    data['level'],
                    ','.join(data['keywords']),
                    ','.join(data['controversial_words']),
                    data['topic'],
                    f"{data['confidence']:.3f}",
                    data['explanation'],
                    "",  # 人工Score - 待填写
                    "",  # 人工等级 - 待填写
                    "",  # 人工理由 - 待填写
                    "",  # 质量评分 - 待填写
                    ""   # 备注 - 待填写
                ]
                writer.writerow(row)

def main():
    """主函数"""
    print("📊 Excel标注文件生成器")
    print("="*50)
    
    # 创建生成器
    generator = ExcelAnnotationGenerator()
    
    # 生成Excel标注文件
    input_file = "E:/LLMData/research_archive/标注文件/标注文件V1.txt"
    output_file = generator.generate_excel_annotation_file(input_file)
    
    if output_file:
        print(f"\n🎉 Excel标注系统准备完成！")
        print(f"📁 标注文件: {output_file}")
        print(f"\n💡 下一步:")
        print(f"1. 用Excel打开标注文件")
        print(f"2. 进行人工标注")
        print(f"3. 保存后可用于词库更新")

if __name__ == "__main__":
    main()
