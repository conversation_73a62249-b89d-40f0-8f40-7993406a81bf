#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学生C批量测试运行脚本
"""

import sys
from pathlib import Path

# 添加core模块到路径
sys.path.append(str(Path(__file__).parent / "core"))

from core.student_c_batch_test import BatchTestRunner

def main():
    print("🎯 学生C批量对比测试")
    print("=" * 50)
    
    # 创建测试运行器
    runner = BatchTestRunner()
    
    # 运行测试
    print("🚀 开始批量测试...")
    results = runner.run_batch_test(100)
    
    if not results:
        print("❌ 没有获得测试结果")
        return
    
    # 保存结果
    print("\n💾 保存测试结果...")
    df = runner.save_results(results)
    
    # 分析结果
    print("\n📊 分析测试结果...")
    runner.analyze_results_enhanced(df)
    
    print("\n✅ 测试完成！")
    print("📋 请查看生成的Excel文件了解详细结果")
    print("🔍 重点关注学生C与其他系统的一致性差异")

if __name__ == "__main__":
    main()