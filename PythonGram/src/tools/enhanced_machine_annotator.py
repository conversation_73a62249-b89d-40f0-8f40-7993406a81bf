#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强机器标注器 - Excel标注系统
基于生产词库进行智能预标注，输出Excel友好格式
"""

import json
import jieba
import re
from pathlib import Path
from typing import Dict, List, Tuple
from collections import Counter
from datetime import datetime

class EnhancedMachineAnnotator:
    """增强机器标注器"""
    
    def __init__(self, lexicon_file: str = "production_lexicon.json"):
        """
        初始化标注器
        
        Args:
            lexicon_file: 生产词库文件路径
        """
        self.version = "3.4.0"  # 🏷️ Excel标注系统版本
        self.load_production_lexicon(lexicon_file)
        
        # 主题分类词典
        self.topic_keywords = {
            "减持": ["减持", "出售股份", "股份转让", "减少持股"],
            "增持": ["增持", "买入股份", "增加持股", "股份回购"],
            "财务": ["财报", "年报", "季报", "业绩", "利润", "收入", "亏损"],
            "处罚": ["处罚", "罚款", "违规", "立案", "调查", "监管函"],
            "重组": ["重组", "并购", "收购", "资产重组", "股权转让"],
            "分红": ["分红", "派息", "股息", "现金分红", "股票股利"],
            "公告": ["公告", "通知", "声明", "澄清", "更正", "补充"]
        }
        
        print(f"🏷️ 增强机器标注器 v{self.version} 初始化完成")
    
    def load_production_lexicon(self, lexicon_file: str):
        """加载生产词库"""
        try:
            with open(lexicon_file, 'r', encoding='utf-8') as f:
                lexicon_data = json.load(f)
            
            # 加载词汇集合
            word_lists = lexicon_data.get("word_lists", {})
            self.negative_words = set(word_lists.get("all_negative_words", []))
            self.positive_words = set(word_lists.get("all_positive_words", []))
            self.controversial_words = set(word_lists.get("controversial_words", []))
            
            # 加载增强词汇
            enhancements = lexicon_data.get("enhancements", {})
            if "critical_missing_words" in enhancements:
                critical = enhancements["critical_missing_words"]
                if "E_TERRIBLE" in critical and "争议词汇_负面优先" in critical["E_TERRIBLE"]:
                    controversial_priority = critical["E_TERRIBLE"]["争议词汇_负面优先"]
                    self.controversial_words.update(controversial_priority)
            
            self.lexicon_version = lexicon_data.get("version", "unknown")
            self.lexicon_strategy = lexicon_data.get("strategy", "unknown")
            
            print(f"✅ 加载生产词库: v{self.lexicon_version}")
            print(f"   负面词汇: {len(self.negative_words)} 个")
            print(f"   正面词汇: {len(self.positive_words)} 个")
            print(f"   争议词汇: {len(self.controversial_words)} 个")
            
        except Exception as e:
            print(f"⚠️ 词库加载失败，使用内置词库: {e}")
            self._init_builtin_lexicon()
    
    def _init_builtin_lexicon(self):
        """初始化内置词库"""
        self.negative_words = {
            "减少", "下降", "降低", "缩减", "困难", "压力", "危机", "问题",
            "流失", "恶化", "萎缩", "衰退", "严峻", "疲软", "低迷",
            "退市", "亏损", "违规", "处罚", "风险", "损失"
        }
        
        self.positive_words = {
            "突破", "签署", "获得", "实现", "重大", "显著", "优异", "卓越",
            "合作", "协议", "增长", "提升", "改善", "优化", "成功", "创新"
        }
        
        self.controversial_words = {"减少", "下降", "降低", "缩减"}
        self.lexicon_version = "builtin"
        self.lexicon_strategy = "内置词库"
    
    def annotate_summary(self, text: str) -> Dict:
        """
        对文摘进行全面机器标注
        
        Args:
            text: 文摘文本
            
        Returns:
            标注结果字典
        """
        if not text or not text.strip():
            return self._empty_annotation()
        
        # 1. 基础情感分析
        sentiment_result = self._analyze_sentiment(text)
        
        # 2. 关键词提取
        keywords = self._extract_keywords(text)
        
        # 3. 争议词检测
        controversial = self._find_controversial_words(text)
        
        # 4. 主题分类
        topic = self._classify_topic(text)
        
        # 5. 实体识别
        entities = self._extract_entities(text)
        
        # 6. 置信度计算
        confidence = self._calculate_confidence(text, sentiment_result, keywords)
        
        # 7. 生成解释
        explanation = self._generate_explanation(sentiment_result, keywords, controversial, topic)
        
        return {
            "score": sentiment_result["score"],
            "level": sentiment_result["level"],
            "keywords": keywords,
            "controversial_words": controversial,
            "topic": topic,
            "entities": entities,
            "confidence": confidence,
            "explanation": explanation,
            "lexicon_version": self.lexicon_version
        }
    
    def _analyze_sentiment(self, text: str) -> Dict:
        """情感分析"""
        negative_score = 0.0
        positive_score = 0.0
        signal_count = 0
        
        # 争议词汇检查（权重1.5）
        for word in self.controversial_words:
            if word in text:
                negative_score += 1.5
                signal_count += 1
        
        # 负面词汇检查（权重1.0）
        for word in self.negative_words:
            if word in text and word not in self.controversial_words:
                negative_score += 1.0
                signal_count += 1
        
        # 正面词汇检查（权重0.8）
        for word in self.positive_words:
            if word in text:
                positive_score += 0.8
                signal_count += 1
        
        # 计算最终分数
        if signal_count == 0:
            final_score = 0.0
        else:
            final_score = (positive_score - negative_score) / (signal_count + 1)
            final_score = max(-2.0, min(2.0, final_score))  # 限制在[-2, 2]范围
        
        # 分级（激进模式）
        if len([w for w in self.controversial_words if w in text]) > 0:
            level = "E_TERRIBLE"
        elif negative_score >= 2.0:
            level = "E_TERRIBLE"
        elif negative_score >= 1.0:
            level = "D_BAD"
        elif negative_score > 0:
            level = "C_NEGATIVE"
        elif positive_score >= 1.5:
            level = "A_EXCELLENT"
        elif positive_score > 0:
            level = "B_GOOD"
        else:
            level = "C_NEUTRAL"
        
        return {
            "score": round(final_score, 3),
            "level": level,
            "negative_score": negative_score,
            "positive_score": positive_score,
            "signal_count": signal_count
        }
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        keywords = []
        
        # 词库匹配的关键词
        all_words = self.negative_words | self.positive_words | self.controversial_words
        for word in all_words:
            if word in text:
                keywords.append(word)
        
        # jieba分词提取其他关键词
        words = jieba.cut(text)
        for word in words:
            if (len(word) >= 2 and 
                re.match(r'^[\u4e00-\u9fff]+$', word) and 
                word not in keywords and
                word not in ['公司', '股份', '有限', '责任', '集团']):  # 排除常见词
                keywords.append(word)
        
        return keywords[:10]  # 限制数量
    
    def _find_controversial_words(self, text: str) -> List[str]:
        """查找争议词汇"""
        return [word for word in self.controversial_words if word in text]
    
    def _classify_topic(self, text: str) -> str:
        """主题分类"""
        topic_scores = {}
        
        for topic, keywords in self.topic_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text)
            if score > 0:
                topic_scores[topic] = score
        
        if topic_scores:
            return max(topic_scores, key=topic_scores.get)
        else:
            return "其他"
    
    def _extract_entities(self, text: str) -> Dict:
        """实体识别"""
        entities = {
            "companies": [],
            "amounts": [],
            "percentages": [],
            "dates": []
        }
        
        # 简单的实体识别
        # 公司名（包含"股份"、"集团"等）
        company_pattern = r'[\u4e00-\u9fff]{2,10}(?:股份|集团|公司|有限)'
        entities["companies"] = re.findall(company_pattern, text)
        
        # 金额（包含"万元"、"亿元"等）
        amount_pattern = r'\d+(?:\.\d+)?(?:万|亿)?元'
        entities["amounts"] = re.findall(amount_pattern, text)
        
        # 百分比
        percentage_pattern = r'\d+(?:\.\d+)?%'
        entities["percentages"] = re.findall(percentage_pattern, text)
        
        # 日期
        date_pattern = r'\d{4}[-年]\d{1,2}[-月]\d{1,2}日?'
        entities["dates"] = re.findall(date_pattern, text)
        
        return entities
    
    def _calculate_confidence(self, text: str, sentiment_result: Dict, keywords: List[str]) -> float:
        """计算置信度"""
        confidence = 0.5  # 基础置信度
        
        # 基于信号数量
        signal_count = sentiment_result["signal_count"]
        if signal_count >= 3:
            confidence += 0.3
        elif signal_count >= 1:
            confidence += 0.2
        
        # 基于争议词汇
        controversial_count = len([w for w in self.controversial_words if w in text])
        if controversial_count > 0:
            confidence += 0.2
        
        # 基于文本长度
        if len(text) >= 20:
            confidence += 0.1
        
        # 基于关键词匹配度
        matched_keywords = len([k for k in keywords if k in (self.negative_words | self.positive_words)])
        if matched_keywords >= 2:
            confidence += 0.1
        
        return min(1.0, confidence)
    
    def _generate_explanation(self, sentiment_result: Dict, keywords: List[str], 
                            controversial: List[str], topic: str) -> str:
        """生成解释"""
        explanations = []
        
        if controversial:
            explanations.append(f"包含争议词汇: {','.join(controversial)}")
        
        if sentiment_result["negative_score"] > 0:
            explanations.append(f"检测到{sentiment_result['negative_score']:.1f}分负面信号")
        
        if sentiment_result["positive_score"] > 0:
            explanations.append(f"检测到{sentiment_result['positive_score']:.1f}分正面信号")
        
        if topic != "其他":
            explanations.append(f"主题分类: {topic}")
        
        if keywords:
            explanations.append(f"关键词: {','.join(keywords[:3])}")
        
        return "; ".join(explanations) if explanations else "未检测到明显情感信号"
    
    def _empty_annotation(self) -> Dict:
        """空文本的标注结果"""
        return {
            "score": 0.0,
            "level": "C_NEUTRAL",
            "keywords": [],
            "controversial_words": [],
            "topic": "其他",
            "entities": {"companies": [], "amounts": [], "percentages": [], "dates": []},
            "confidence": 0.0,
            "explanation": "文本为空",
            "lexicon_version": self.lexicon_version
        }
