# 词条信息增益分析器 (Term Gain Analyzer)

## 🎯 项目简介

这是一个基于LLM分析结果的词条信息增益分析工具，旨在帮助您：

1. **提取关键词条**：从LLM的`reasoning`和`keyFactors`中识别重要特征
2. **计算信息增益**：量化每个词条在区分不同情绪等级时的能力
3. **生成正则建议**：基于分析结果，为您的正则表达式规则库提供优化建议
4. **降低成本**：通过优化本地规则，减少不必要的LLM API调用

## 🚀 快速开始

### 环境要求

- **Java版本**: Java 8+
- **Python版本**: Python 3.7+
- **Maven**: 3.6+ (用于Java版本)
- **依赖包**: pandas, openpyxl (Python版本)

### 安装依赖

#### Java版本
```bash
cd PythonGram/src/tools
mvn clean install
```

#### Python版本
```bash
pip install pandas openpyxl
```

### 运行示例

#### Java版本
```bash
# 使用Maven运行
mvn exec:java -Dexec.mainClass="com.example.analysis.TermGainAnalyzer" \
    -Dexec.args="sample_llm_results.json term_analysis.xlsx"

# 或者直接运行JAR
java -cp target/term-gain-analyzer-1.0.0.jar \
    com.example.analysis.TermGainAnalyzer \
    sample_llm_results.json term_analysis.xlsx
```

#### Python版本
```bash
# 直接运行
python term_gain_analyzer.py sample_llm_results.json

# 或者使用运行脚本
python run_python_analyzer.py
```

#### Windows批处理
```bash
# 双击运行
compile_and_run.bat
```

## 📊 输入数据格式

### LLM结果JSON格式
```json
[
  {
    "id": "001",
    "ratingLevel": "劣",
    "keyFactors": ["暴跌", "严重违规", "退市风险"],
    "reasoning": "公司业绩暴跌，存在严重违规行为，面临退市风险"
  },
  {
    "id": "002",
    "ratingLevel": "优",
    "keyFactors": ["大幅增长", "行业领先"],
    "reasoning": "业绩大幅增长，在行业中保持领先地位"
  }
]
```

### 字段说明
- `id`: 唯一标识符
- `ratingLevel`: 情绪等级 (优/好/中/差/劣)
- `keyFactors`: 关键因素列表
- `reasoning`: 分析推理文本

## 📈 输出结果

### 1. Excel报告 (`term_analysis.xlsx`)

包含两个工作表：

#### 词条分析报告
| 列名 | 说明 |
|------|------|
| 词条 | 提取的词条名称 |
| 总出现次数 | 该词条在所有文章中的总出现次数 |
| 优次数/占比 | 在"优"类文章中的出现次数和占比 |
| 好次数/占比 | 在"好"类文章中的出现次数和占比 |
| 中次数/占比 | 在"中"类文章中的出现次数和占比 |
| 差次数/占比 | 在"差"类文章中的出现次数和占比 |
| 劣次数/占比 | 在"劣"类文章中的出现次数和占比 |
| 信息增益 | 该词条的区分能力量化指标 |

#### 正则表达式建议
| 列名 | 说明 |
|------|------|
| 词条 | 词条名称 |
| 信息增益 | 区分能力指标 |
| 主要类别 | 该词条主要出现的类别 |
| 置信度 | 分类置信度 (高/中/低) |
| 规则类型 | 建议的规则类型 (强/中/弱) |
| 正则表达式 | 生成的正则表达式建议 |
| 建议 | 使用建议说明 |

### 2. JSON建议文件 (`*_regex_suggestions.json`)

包含所有正则表达式建议的JSON格式数据，便于程序化处理。

## 🔍 核心算法

### 信息增益计算

基于信息论中的熵概念，计算每个词条对分类的贡献：

1. **总体熵**: 计算整个数据集的类别分布不确定性
2. **条件熵**: 计算包含/不包含某个词条时的类别分布不确定性
3. **信息增益**: 总体熵 - 条件熵，值越大表示区分能力越强

### 词条提取策略

1. **keyFactors优先**: 优先使用LLM提供的结构化关键因素
2. **reasoning补充**: 从推理文本中提取补充词条
3. **过滤规则**: 过滤单字符和常见停用词

## 🛠️ 高级功能

### 1. 自定义分析

```python
# Python版本
analyzer = TermGainAnalyzer()

# 自定义类别
analyzer.categories = ["正面", "中性", "负面"]

# 自定义词条提取规则
analyzer.custom_tokenizer = lambda text: text.split('|')
```

### 2. 批量处理

```bash
# 处理多个文件
for file in *.json; do
    python term_gain_analyzer.py "$file" "analysis_${file%.json}.xlsx"
done
```

### 3. 集成到现有系统

```python
# 在您的Python代码中使用
from term_gain_analyzer import TermGainAnalyzer

analyzer = TermGainAnalyzer()
# ... 分析逻辑 ...
suggestions = analyzer.generate_regex_suggestions(top_n=50)
```

## 📋 使用建议

### 1. 数据准备
- 确保LLM结果的质量和一致性
- 平衡各类别的样本数量
- 定期更新训练数据

### 2. 结果解读
- 重点关注信息增益 > 0.1 的词条
- 结合业务场景，选择合适的高频词条
- 注意词条的上下文和语义

### 3. 规则优化
- 从高置信度词条开始构建规则
- 逐步添加中低置信度词条
- 定期验证规则性能

### 4. 持续改进
- 收集新的LLM分析结果
- 重新计算词条信息增益
- 更新正则表达式规则库

## 🔧 故障排除

### 常见问题

1. **Maven依赖下载失败**
   ```bash
   # 清理并重新下载
   mvn clean install -U
   ```

2. **Python包安装失败**
   ```bash
   # 升级pip
   python -m pip install --upgrade pip
   
   # 使用国内镜像
   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple pandas openpyxl
   ```

3. **Excel文件无法打开**
   - 确保安装了openpyxl包
   - 检查文件权限
   - 尝试使用WPS或LibreOffice打开

4. **内存不足**
   - 减少处理的文件大小
   - 增加JVM内存: `java -Xmx4g ...`

### 日志查看

工具会输出详细的日志信息，包括：
- 数据加载状态
- 处理进度
- 错误详情
- 结果统计

## 📚 扩展开发

### 1. 添加新的分词器

```python
class CustomTokenizer:
    def tokenize(self, text):
        # 实现自定义分词逻辑
        return tokens

analyzer.tokenizer = CustomTokenizer()
```

### 2. 自定义评估指标

```python
def custom_metric(term, counts):
    # 实现自定义评估逻辑
    return score

analyzer.add_metric("custom", custom_metric)
```

### 3. 集成专业NLP工具

```python
import jieba
import nltk

# 使用jieba进行中文分词
analyzer.tokenizer = lambda text: jieba.lcut(text)

# 使用nltk进行英文处理
analyzer.tokenizer = lambda text: nltk.word_tokenize(text)
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个工具！

### 开发环境设置
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

### 代码规范
- 遵循PEP 8 (Python) 和Google Java Style
- 添加适当的注释和文档
- 包含单元测试

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**祝您使用愉快！** 🎉
