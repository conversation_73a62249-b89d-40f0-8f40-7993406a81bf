# 三步走策略工具集

基于昨天讨论的融合策略，实现完整的规则优化流程：**规则预过滤 + LLM精加工**

## 🎯 策略概述

### 核心思想
**不要猜测规则的置信度，去回测它！** 用已有的2600个负面案例和中性案例来科学评估每条规则的性能。

### 三个步骤
1. **第一步**：创建 `neutral_cases.csv`（反例数据集）
2. **第二步**：回测规则并量化性能（关键步骤）
3. **第三步**：根据回测结果，制定最终提交策略

## 📁 文件说明

### 核心工具
- `create_neutral_cases.py` - 中性案例数据提取工具
- `backtester.py` - 规则回测工具
- `run_three_step_strategy.py` - 三步走策略执行器

### 辅助文件
- `run_strategy.bat` - Windows批处理执行脚本
- `README.md` - 本说明文档

### 输入文件
- `negative_cases.csv` - 负面案例数据集（需要提供）
- `ngram_analysis_results.txt` - ngram分析结果（已存在）

### 输出文件
- `neutral_cases.csv` - 生成的中性案例数据集
- `backtest_results.json` - 详细的回测结果
- `strategy_implementation_guide.md` - 策略实施指南

## 🚀 快速开始

### 方法1：使用批处理文件（推荐）
```bash
# Windows用户
双击运行 run_strategy.bat
```

### 方法2：命令行执行
```bash
# 进入工具目录
cd PythonGram/src/tools

# 执行三步走策略
python run_three_step_strategy.py
```

### 方法3：分步执行
```bash
# 第一步：创建中性案例
python create_neutral_cases.py

# 第二步：回测规则
python backtester.py

# 第三步：生成策略建议（自动执行）
```

## 📊 规则集说明

### 当前规则集（基于昨天讨论的方案1）
1. **业绩预警类**（高置信度）
   - 模式：`(预计|预告|预亏).*(亏损|下降|减少|下滑|预警)`
   - 模式：`(万元|亿元).*(比上年同期下降|同比下降|亏损|减少)`

2. **风险提示类**（中置信度）
   - 模式：`风险因素.*关键数据.*(预计|净利润|营业收入|担保)`
   - 模式：`风险.*(提示|警示|警告|提醒)`

3. **监管关注类**（中置信度）
   - 模式：`(问询函|警示函|监管函|关注函)`
   - 模式：`(立案|调查|处罚|监管)`

4. **经营异常类**（低置信度，但包含）
   - 模式：`(停产|关闭|破产|重组|清算|纠纷|违约)`

## 📈 性能指标

### 关键指标
- **召回率 (Recall)**：在所有真实负面公告中的识别率
- **精确率 (Precision)**：被识别为负面中的真实负面比例
- **F1分数**：精确率和召回率的调和平均数

### 策略建议
- **高精确率 (>95%)**：直接标记，无需LLM
- **中精确率 (80-95%)**：标记为"疑似负面，待LLM确认"
- **低精确率 (<80%)**：需要优化规则，重复回测

## 🔧 技术实现

### 数据流程
```
negative_cases.csv → 提取中性候选 → neutral_cases.csv
                                        ↓
ngram_analysis_results.txt → 规则定义 → 规则回测
                                        ↓
                                    backtest_results.json
                                        ↓
                                策略建议 + 实施指南
```

### 核心算法
1. **文本预处理**：拼接标题、摘要、关键因素、推理等字段
2. **规则匹配**：使用正则表达式进行模式匹配
3. **性能计算**：计算TP、FP、FN、TN，得出精确率和召回率
4. **策略生成**：基于性能指标自动生成策略建议

## 📋 使用要求

### 环境要求
- Python 3.6+
- pandas
- 标准库：re, logging, os, json, pathlib

### 数据要求
- `negative_cases.csv` 必须包含以下列：
  - `fileName`: 文件名
  - `stockCode`: 股票代码
  - `tradeDate`: 交易日期
  - `title`: 标题
  - `summary`: 摘要
  - `keyFactors`: 关键因素
  - `reasoning`: 推理
  - `ratingLevel`: LLM评级
  - `sentimentScore`: 情感分数
  - `eventTags`: 事件标签

## 🎯 预期结果

### 成功执行后，您将获得：
1. **客观数据**：规则的精确率和召回率
2. **科学建议**：基于数据的策略推荐
3. **实施指南**：详细的Java集成方案
4. **成本效益**：LLM调用成本节省方案

### 下一步行动
根据策略建议，在Java应用中：
1. 集成规则引擎
2. 实施推荐的策略
3. 建立性能监控
4. 持续优化规则

## 🔍 故障排除

### 常见问题
1. **Python环境问题**
   - 确保Python在PATH中
   - 检查Python版本（需要3.6+）

2. **文件路径问题**
   - 确保在正确的目录中运行
   - 检查输入文件是否存在

3. **数据格式问题**
   - 检查CSV文件编码（应为UTF-8）
   - 验证必要列是否存在

### 调试模式
```bash
# 启用详细日志
python -u run_three_step_strategy.py 2>&1 | tee strategy.log
```

## 📚 相关文档

- [昨天讨论总结](../README.md)
- [ngram分析结果](ngram_analysis_results.txt)
- [负面案例分析工具](negative_case_analyzer.py)

## 🤝 贡献指南

如果您发现bug或有改进建议，请：
1. 检查现有issue
2. 创建新的issue
3. 提供详细的错误信息和复现步骤

---

**记住：不要猜测规则的置信度，去回测它！** 🎯






